# 低空三维空域动态气象预警系统演示指南

## 🎯 系统概览

您现在看到的是一个完整的低空三维空域动态气象预警系统，专为低空飞行安全设计。

## 🖥️ 界面布局

### 主要区域
1. **中央3D场景**: 显示三维地形和气象数据
2. **左侧控制面板**: 时间控制、图层管理、视角切换
3. **右下角信息面板**: 预警信息、天气数据、系统状态
4. **右上角图例**: 预警等级说明

## 🎮 交互操作指南

### 基础操作
- **鼠标左键拖拽**: 旋转视角
- **鼠标滚轮**: 缩放视图
- **鼠标右键拖拽**: 平移视图
- **点击地形**: 查看该位置详细天气信息

### 控制面板操作

#### 1. 时间控制
- 查看当前时间显示
- 拖拽时间滑块调整时间
- 点击播放按钮开始时间动画
- 点击重置按钮回到当前时间

#### 2. 图层管理
- ✅ **风场**: 显示/隐藏风向箭头
- ✅ **温度**: 显示/隐藏温度热力图
- ⬜ **湿度**: 显示/隐藏湿度云层
- ✅ **降水**: 显示/隐藏降水效果
- ✅ **预警区域**: 显示/隐藏危险区域标识

#### 3. 视角切换
- 🌍 **全景视角**: 鸟瞰整个区域
- 🔍 **近景视角**: 详细观察局部
- 📐 **侧面视角**: 剖面查看

#### 4. 预警设置
- 调整风速预警阈值
- 调整温度预警阈值
- 调整湿度预警阈值

#### 5. 数据控制
- 手动刷新气象数据
- 导出当前数据为JSON
- 开启/关闭自动刷新

## 🚨 预警系统说明

### 预警等级（右上角图例）
- 🔴 **红色预警**: 极度危险（风速>35m/s，温度<-20°C或>45°C）
- 🟠 **橙色预警**: 高度危险（风速>30m/s，温度异常）
- 🟡 **黄色预警**: 中等危险（风速>25m/s，高湿度）
- 🔵 **蓝色预警**: 低度危险（风速>20m/s，能见度低）

### 预警区域显示
- 3D圆柱体标识危险区域
- 颜色对应预警等级
- 半透明效果便于观察内部

## 📊 数据可视化元素

### 风场显示
- **绿色箭头**: 微风（<5m/s）
- **黄色箭头**: 轻风（5-15m/s）
- **橙色箭头**: 强风（15-25m/s）
- **红色箭头**: 烈风（>25m/s）

### 温度分布
- **蓝色球体**: 低温区域（<0°C）
- **青色球体**: 寒冷区域（0-10°C）
- **绿色球体**: 凉爽区域（10-20°C）
- **黄色球体**: 温暖区域（20-30°C）
- **橙色球体**: 炎热区域（30-40°C）
- **红色球体**: 酷热区域（>40°C）

### 地形特征
- **蓝绿色**: 水面/低地
- **绿色**: 平原
- **黄绿色**: 丘陵
- **棕色**: 山地
- **灰白色**: 高山

## 🔍 详细功能演示

### 1. 查看预警信息
1. 观察右下角信息面板的"当前预警"部分
2. 查看预警等级、类型、位置和描述
3. 在3D场景中找到对应的预警区域

### 2. 点击查看位置信息
1. 在3D地形上任意点击
2. 查看右下角"选中位置信息"
3. 观察该位置的坐标和天气数据

### 3. 图层控制演示
1. 取消勾选"风场"，观察风向箭头消失
2. 取消勾选"温度"，观察温度球体消失
3. 取消勾选"预警区域"，观察预警圆柱体消失
4. 重新勾选以恢复显示

### 4. 视角切换演示
1. 点击"全景视角"，观察视角变化
2. 点击"近景视角"，观察拉近效果
3. 点击"侧面视角"，观察侧面剖面
4. 手动调整到喜欢的角度

### 5. 时间控制演示
1. 拖拽时间滑块，观察数据变化
2. 点击播放按钮，观察时间自动前进
3. 观察气象数据的动态变化
4. 点击重置回到当前时间

## 📈 天气概况解读

右下角信息面板显示：
- **平均温度**: 当前区域平均温度
- **平均风速**: 当前区域平均风速
- **最高温度**: 区域内最高温度
- **最大风速**: 区域内最大风速
- **预警数量**: 当前活跃预警总数

## 🔧 系统状态监控

信息面板底部显示：
- **数据连接**: 气象数据源连接状态
- **渲染引擎**: Three.js渲染引擎状态
- **预警系统**: 预警算法运行状态
- **最后更新**: 数据最后更新时间

## 💡 使用技巧

### 最佳观察角度
1. 使用全景视角了解整体情况
2. 使用近景视角查看局部细节
3. 使用侧面视角观察高度分布

### 预警识别
1. 红色区域需要立即避让
2. 橙色区域需要谨慎通过
3. 黄色区域需要密切关注
4. 蓝色区域需要适当注意

### 数据分析
1. 结合多个图层综合判断
2. 关注预警区域的移动趋势
3. 观察温度和风速的关联性
4. 注意湿度对能见度的影响

## 🚀 快速操作

### 快速导出数据
1. 点击控制面板"导出数据"按钮
2. 自动下载包含当前所有数据的JSON文件
3. 可用于离线分析或备份

### 快速截图
1. 点击信息面板"📸 截图"按钮
2. 保存当前3D场景截图
3. 用于报告或记录

### 快速生成报告
1. 点击信息面板"📋 生成报告"按钮
2. 自动生成包含预警和天气摘要的报告
3. 下载JSON格式的详细报告

## 🎯 实际应用场景

### 飞行前检查
1. 查看整体天气概况
2. 识别危险区域位置
3. 规划安全飞行路径
4. 设置合适的预警阈值

### 实时监控
1. 开启自动刷新功能
2. 密切关注预警变化
3. 及时调整飞行计划
4. 记录重要天气事件

### 事后分析
1. 回放历史时间数据
2. 分析天气变化趋势
3. 总结经验教训
4. 优化预警参数

---

**提示**: 这是一个演示系统，使用模拟数据。实际应用中需要接入真实的气象数据源。
