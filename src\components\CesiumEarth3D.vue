<template>
  <div class="cesium-earth-container">
    <!-- Cesium 3D地球容器 -->
    <div ref="cesiumContainer" class="cesium-viewer"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 CesiumJS 三维地球</h3>
      
      <!-- 系统状态 -->
      <div class="status-section">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Cesium版本:</span>
          <span class="status-value">{{ cesiumVersion }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">当前时间:</span>
          <span class="status-value">{{ currentTime }}</span>
        </div>
      </div>

      <!-- 系统控制 -->
      <div class="system-section" v-if="systemStatus.class === 'error'">
        <h4>🔧 系统控制</h4>
        <div class="button-grid">
          <button @click="retryInitialization" class="control-btn retry">🔄 重新初始化</button>
          <button @click="forceReload" class="control-btn reload">🔃 强制刷新</button>
        </div>
      </div>

      <!-- 视图控制 -->
      <div class="view-section" v-if="systemStatus.class !== 'error'">
        <h4>🎥 视图控制</h4>
        <div class="button-grid">
          <button @click="flyToChina" class="control-btn">🇨🇳 中国</button>
          <button @click="flyToZhuhai" class="control-btn">🏙️ 珠海</button>
          <button @click="flyToSpace" class="control-btn">🚀 太空</button>
          <button @click="resetView" class="control-btn">🔄 重置</button>
        </div>
      </div>

      <!-- 地图控制 -->
      <div class="map-section">
        <h4>🗺️ 地图控制</h4>
        <div class="button-grid">
          <button @click="toggleImagery" class="control-btn" :class="{ active: showImagery }">
            {{ showImagery ? '🖼️ 隐藏影像' : '🖼️ 显示影像' }}
          </button>
          <button @click="toggleTerrain" class="control-btn" :class="{ active: showTerrain }">
            {{ showTerrain ? '🏔️ 平面地形' : '🏔️ 立体地形' }}
          </button>
          <button @click="toggleAtmosphere" class="control-btn" :class="{ active: showAtmosphere }">
            {{ showAtmosphere ? '🌫️ 隐藏大气' : '🌫️ 显示大气' }}
          </button>
          <button @click="toggleLighting" class="control-btn" :class="{ active: showLighting }">
            {{ showLighting ? '💡 关闭光照' : '💡 开启光照' }}
          </button>
        </div>
      </div>

      <!-- 标记控制 -->
      <div class="marker-section">
        <h4>📍 标记控制</h4>
        <div class="button-grid">
          <button @click="addZhuhaiMarkers" class="control-btn">📍 珠海标记</button>
          <button @click="addRandomMarkers" class="control-btn">🎯 随机标记</button>
          <button @click="clearMarkers" class="control-btn">🗑️ 清除标记</button>
        </div>
      </div>

      <!-- 信息显示 -->
      <div class="info-section">
        <div class="info-item">
          <span class="info-label">相机高度:</span>
          <span class="info-value">{{ cameraHeight }}km</span>
        </div>
        <div class="info-item">
          <span class="info-label">标记数量:</span>
          <span class="info-value">{{ markerCount }}</span>
        </div>
      </div>
    </div>

    <!-- 通知消息 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as Cesium from 'cesium';

// 响应式数据
const cesiumContainer = ref(null);
const cesiumVersion = ref('');
const currentTime = ref('');
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const notification = ref(null);

// 控制状态
const showImagery = ref(true);
const showTerrain = ref(false);
const showAtmosphere = ref(true);
const showLighting = ref(true);
const cameraHeight = ref(0);
const markerCount = ref(0);

// 系统变量
let viewer = null;
let timeInterval = null;
let markers = [];

// 珠海区域数据
const zhuhaiAreas = [
  { name: '香洲区', lng: 113.5767, lat: 22.2707, icon: '🏢' },
  { name: '金湾区', lng: 113.3600, lat: 22.1400, icon: '✈️' },
  { name: '斗门区', lng: 113.2900, lat: 22.2100, icon: '🌾' },
  { name: '横琴新区', lng: 113.4200, lat: 22.1300, icon: '🏗️' }
];

// 组件挂载
onMounted(async () => {
  try {
    await initializeCesium();
    startTimeUpdate();
  } catch (error) {
    console.error('系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };

    // 尝试简化初始化
    setTimeout(async () => {
      try {
        await initializeSimpleCesium();
      } catch (fallbackError) {
        console.error('简化初始化也失败:', fallbackError);
        showNotification('❌ 系统初始化完全失败，请刷新页面', 'error');
      }
    }, 2000);
  }
});

// 组件卸载
onUnmounted(() => {
  cleanup();
});

// 初始化Cesium
async function initializeCesium() {
  try {
    await nextTick();

    systemStatus.value = { text: '检查环境...', class: 'loading' };

    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到');
    }

    // 检查Cesium模块
    if (!Cesium) {
      throw new Error('CesiumJS模块未加载');
    }

    systemStatus.value = { text: '设置CesiumJS...', class: 'loading' };

    // 设置Cesium访问令牌
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';

    cesiumVersion.value = Cesium.VERSION;

    systemStatus.value = { text: '创建3D地球...', class: 'loading' };

    // 创建稳定的影像提供商
    let imageryProvider;
    try {
      imageryProvider = new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      });
    } catch (error) {
      console.warn('OpenStreetMap失败，使用备用方案');
      imageryProvider = createFallbackImageryProvider();
    }

    // 创建Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false,
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),
      imageryProvider: imageryProvider,
      skyBox: new Cesium.SkyBox({
        sources: {
          positiveX: createSkyTexture('#001122'),
          negativeX: createSkyTexture('#001122'),
          positiveY: createSkyTexture('#001122'),
          negativeY: createSkyTexture('#001122'),
          positiveZ: createSkyTexture('#001122'),
          negativeZ: createSkyTexture('#001122')
        }
      })
    });

    systemStatus.value = { text: '配置地球...', class: 'loading' };

    // 设置初始视图
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 2000000),
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });

    // 启用效果
    viewer.scene.globe.enableLighting = true;
    viewer.scene.globe.show = true;
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.fog.density = 0.0002;

    // 监听相机变化
    viewer.camera.changed.addEventListener(updateCameraInfo);

    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };
    showNotification('✅ CesiumJS 3D地球加载成功', 'success');

    // 自动添加珠海标记
    setTimeout(() => {
      addZhuhaiMarkers();
    }, 1000);

  } catch (error) {
    console.error('Cesium初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    showNotification(`❌ 初始化失败: ${error.message}`, 'error');
    throw error;
  }
}

// 简化的Cesium初始化（备用方案）
async function initializeSimpleCesium() {
  try {
    await nextTick();

    systemStatus.value = { text: '尝试简化初始化...', class: 'loading' };

    if (!cesiumContainer.value) {
      throw new Error('容器未找到');
    }

    // 使用最基本的配置
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false,
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),
      imageryProvider: createFallbackImageryProvider()
    });

    // 设置基本视图
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 2000000),
      orientation: {
        heading: 0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0
      }
    });

    // 基本设置
    viewer.scene.globe.show = true;
    viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#1a4b3a');

    // 监听相机变化
    viewer.camera.changed.addEventListener(updateCameraInfo);

    systemStatus.value = { text: '🟡 简化模式就绪', class: 'warning' };
    showNotification('⚠️ 使用简化模式，功能可能受限', 'warning');

    // 添加珠海标记
    setTimeout(() => {
      addZhuhaiMarkers();
    }, 500);

  } catch (error) {
    console.error('简化初始化失败:', error);
    systemStatus.value = { text: '❌ 完全失败', class: 'error' };
    throw error;
  }
}

// 启动时间更新
function startTimeUpdate() {
  timeInterval = setInterval(() => {
    currentTime.value = new Date().toLocaleTimeString();
  }, 1000);
}

// 重新初始化系统
async function retryInitialization() {
  try {
    systemStatus.value = { text: '重新初始化中...', class: 'loading' };

    // 清理现有viewer
    if (viewer) {
      viewer.destroy();
      viewer = null;
    }

    // 重新初始化
    await initializeCesium();

  } catch (error) {
    console.error('重新初始化失败:', error);
    // 尝试简化初始化
    try {
      await initializeSimpleCesium();
    } catch (fallbackError) {
      systemStatus.value = { text: '❌ 重新初始化失败', class: 'error' };
      showNotification('❌ 重新初始化失败，请刷新页面', 'error');
    }
  }
}

// 强制刷新页面
function forceReload() {
  showNotification('🔃 正在刷新页面...', 'info');
  setTimeout(() => {
    window.location.reload();
  }, 1000);
}

// 更新相机信息
function updateCameraInfo() {
  if (viewer && viewer.camera) {
    const height = viewer.camera.positionCartographic.height;
    cameraHeight.value = Math.round(height / 1000);
  }
}

// 飞行到中国
function flyToChina() {
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(104.0, 35.0, 10000000),
    duration: 3
  });
  showNotification('🇨🇳 飞行到中国', 'info');
}

// 飞行到珠海
function flyToZhuhai() {
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 50000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3
  });
  showNotification('🏙️ 飞行到珠海', 'info');
}

// 飞行到太空
function flyToSpace() {
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 20000000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-90),
      roll: 0.0
    },
    duration: 3
  });
  showNotification('🚀 飞行到太空视角', 'info');
}

// 重置视图
function resetView() {
  viewer.camera.flyHome(3);
  showNotification('🔄 视图已重置', 'info');
}

// 切换影像显示
function toggleImagery() {
  try {
    showImagery.value = !showImagery.value;
    if (viewer.imageryLayers.length > 0) {
      viewer.imageryLayers.get(0).show = showImagery.value;
    } else if (showImagery.value) {
      // 如果没有影像层，添加一个
      const imageryProvider = createFallbackImageryProvider();
      viewer.imageryLayers.addImageryProvider(imageryProvider);
    }
    showNotification(showImagery.value ? '🖼️ 影像已显示' : '🖼️ 影像已隐藏', 'info');
  } catch (error) {
    console.error('影像切换失败:', error);
    showNotification('❌ 影像切换失败', 'error');
  }
}

// 切换地形
function toggleTerrain() {
  try {
    showTerrain.value = !showTerrain.value;
    if (showTerrain.value) {
      // 尝试加载世界地形
      try {
        viewer.terrainProvider = Cesium.createWorldTerrain();
      } catch (error) {
        console.warn('世界地形加载失败，使用椭球地形');
        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
        showTerrain.value = false;
      }
    } else {
      viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
    }
    showNotification(showTerrain.value ? '🏔️ 立体地形已启用' : '🏔️ 平面地形已启用', 'info');
  } catch (error) {
    console.error('地形切换失败:', error);
    showNotification('❌ 地形切换失败', 'error');
  }
}

// 切换大气效果
function toggleAtmosphere() {
  showAtmosphere.value = !showAtmosphere.value;
  viewer.scene.skyAtmosphere.show = showAtmosphere.value;
  viewer.scene.fog.enabled = showAtmosphere.value;
  showNotification(showAtmosphere.value ? '🌫️ 大气效果已显示' : '🌫️ 大气效果已隐藏', 'info');
}

// 切换光照
function toggleLighting() {
  showLighting.value = !showLighting.value;
  viewer.scene.globe.enableLighting = showLighting.value;
  showNotification(showLighting.value ? '💡 光照已开启' : '💡 光照已关闭', 'info');
}

// 添加珠海标记
function addZhuhaiMarkers() {
  zhuhaiAreas.forEach(area => {
    const entity = viewer.entities.add({
      name: area.name,
      position: Cesium.Cartesian3.fromDegrees(area.lng, area.lat, 1000),
      billboard: {
        image: createMarkerCanvas(area.icon, '#FF6B35'),
        scale: 1.0,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM
      },
      label: {
        text: area.name,
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50)
      }
    });
    markers.push(entity);
  });
  
  markerCount.value = markers.length;
  showNotification('📍 珠海标记已添加', 'success');
}

// 添加随机标记
function addRandomMarkers() {
  for (let i = 0; i < 5; i++) {
    const lng = 113.0 + Math.random() * 1.0;
    const lat = 22.0 + Math.random() * 0.5;
    
    const entity = viewer.entities.add({
      name: `随机点${i + 1}`,
      position: Cesium.Cartesian3.fromDegrees(lng, lat, 1000),
      point: {
        pixelSize: 15,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: `点${i + 1}`,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.YELLOW,
        pixelOffset: new Cesium.Cartesian2(0, -30)
      }
    });
    markers.push(entity);
  }
  
  markerCount.value = markers.length;
  showNotification('🎯 随机标记已添加', 'success');
}

// 清除标记
function clearMarkers() {
  markers.forEach(marker => {
    viewer.entities.remove(marker);
  });
  markers = [];
  markerCount.value = 0;
  showNotification('🗑️ 所有标记已清除', 'info');
}

// 创建备用影像提供商
function createFallbackImageryProvider() {
  // 创建简单的蓝绿色地球纹理
  const canvas = document.createElement('canvas');
  canvas.width = 256;
  canvas.height = 256;
  const ctx = canvas.getContext('2d');

  // 绘制海洋背景
  ctx.fillStyle = '#1e3a8a';
  ctx.fillRect(0, 0, 256, 256);

  // 绘制陆地
  ctx.fillStyle = '#22c55e';
  ctx.beginPath();
  ctx.arc(128, 128, 80, 0, 2 * Math.PI);
  ctx.fill();

  // 绘制一些随机陆地块
  for (let i = 0; i < 5; i++) {
    ctx.fillStyle = '#16a34a';
    ctx.beginPath();
    ctx.arc(
      Math.random() * 256,
      Math.random() * 256,
      Math.random() * 30 + 10,
      0,
      2 * Math.PI
    );
    ctx.fill();
  }

  return new Cesium.SingleTileImageryProvider({
    url: canvas.toDataURL(),
    rectangle: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90)
  });
}

// 创建天空盒纹理
function createSkyTexture(color) {
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  const ctx = canvas.getContext('2d');
  ctx.fillStyle = color;
  ctx.fillRect(0, 0, 1, 1);
  return canvas.toDataURL();
}

// 创建标记画布
function createMarkerCanvas(icon, color) {
  const canvas = document.createElement('canvas');
  canvas.width = 48;
  canvas.height = 48;
  const ctx = canvas.getContext('2d');

  // 绘制圆形背景
  ctx.fillStyle = color;
  ctx.beginPath();
  ctx.arc(24, 24, 20, 0, 2 * Math.PI);
  ctx.fill();

  // 绘制边框
  ctx.strokeStyle = '#FFFFFF';
  ctx.lineWidth = 3;
  ctx.stroke();

  // 绘制图标
  ctx.font = '20px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = '#FFFFFF';
  ctx.fillText(icon, 24, 24);

  return canvas;
}

// 显示通知
function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理资源
function cleanup() {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
  
  markers = [];
}
</script>

<style scoped>
.cesium-earth-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.cesium-viewer {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 280px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-height: 90vh;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  text-align: center;
  color: #4CAF50;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 20px 0 10px 0;
  font-size: 14px;
  color: #FFA726;
  border-bottom: 1px solid rgba(255, 167, 38, 0.3);
  padding-bottom: 5px;
}

.status-section,
.view-section,
.map-section,
.marker-section,
.info-section {
  margin-bottom: 20px;
}

.status-item,
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.status-label,
.info-label {
  color: #B0BEC5;
}

.status-value,
.info-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FF9800;
}

.status-value.online {
  color: #4CAF50;
}

.status-value.error {
  color: #F44336;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.control-btn {
  padding: 10px 12px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.control-btn.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.control-btn.retry {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.control-btn.reload {
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
}

.system-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 25px;
  color: white;
  font-weight: bold;
  z-index: 2000;
  animation: slideDown 0.3s ease;
}

.notification.success {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.notification.info {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.notification.warning {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.notification.error {
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 滚动条样式 */
.control-panel::-webkit-scrollbar {
  width: 6px;
}

.control-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    width: 250px;
    right: 10px;
    top: 10px;
    padding: 15px;
  }

  .button-grid {
    grid-template-columns: 1fr;
  }

  .control-btn {
    font-size: 10px;
    padding: 8px 10px;
  }
}
</style>
