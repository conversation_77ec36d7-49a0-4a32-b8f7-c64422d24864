<template>
  <div class="smart-city-zhuhai">
    <!-- Cesium 3D地球容器 -->
    <div ref="cesiumContainer" class="cesium-viewer"></div>
    
    <!-- 智慧城市控制面板 -->
    <div class="smart-panel">
      <div class="panel-header">
        <h2>🏙️ 智慧珠海</h2>
        <div class="city-status">
          <span class="status-dot" :class="systemStatus.class"></span>
          <span class="status-text">{{ systemStatus.text }}</span>
        </div>
      </div>

      <!-- 城市概览 -->
      <div class="city-overview">
        <div class="overview-item">
          <div class="item-icon">🌡️</div>
          <div class="item-content">
            <div class="item-label">温度</div>
            <div class="item-value">{{ weatherData.temperature }}°C</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">💨</div>
          <div class="item-content">
            <div class="item-label">风速</div>
            <div class="item-value">{{ weatherData.windSpeed }}km/h</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">💧</div>
          <div class="item-content">
            <div class="item-label">湿度</div>
            <div class="item-value">{{ weatherData.humidity }}%</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">👥</div>
          <div class="item-content">
            <div class="item-label">人口</div>
            <div class="item-value">{{ cityData.population }}</div>
          </div>
        </div>
      </div>

      <!-- 智慧功能控制 -->
      <div class="smart-controls">
        <h3>🎛️ 智慧功能</h3>
        <div class="control-grid">
          <button @click="toggleBuildings" class="smart-btn" :class="{ active: showBuildings }">
            🏢 建筑物
          </button>
          <button @click="toggleTraffic" class="smart-btn" :class="{ active: showTraffic }">
            🚗 交通流
          </button>
          <button @click="toggleWeather" class="smart-btn" :class="{ active: showWeather }">
            🌦️ 天气层
          </button>
          <button @click="togglePOI" class="smart-btn" :class="{ active: showPOI }">
            📍 兴趣点
          </button>
          <button @click="toggleSensors" class="smart-btn" :class="{ active: showSensors }">
            📡 传感器
          </button>
          <button @click="toggleDrones" class="smart-btn" :class="{ active: showDrones }">
            🚁 无人机
          </button>
        </div>
      </div>

      <!-- 区域选择 -->
      <div class="area-selection">
        <h3>🗺️ 区域导航</h3>
        <div class="area-grid">
          <button @click="flyToArea('xiangzhou')" class="area-btn">
            🏢 香洲区
          </button>
          <button @click="flyToArea('jinwan')" class="area-btn">
            ✈️ 金湾区
          </button>
          <button @click="flyToArea('doumen')" class="area-btn">
            🌾 斗门区
          </button>
          <button @click="flyToArea('hengqin')" class="area-btn">
            🏗️ 横琴新区
          </button>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="realtime-data">
        <h3>📊 实时数据</h3>
        <div class="data-list">
          <div class="data-item">
            <span class="data-label">空气质量:</span>
            <span class="data-value good">优</span>
          </div>
          <div class="data-item">
            <span class="data-label">交通状况:</span>
            <span class="data-value normal">畅通</span>
          </div>
          <div class="data-item">
            <span class="data-label">能耗状态:</span>
            <span class="data-value good">正常</span>
          </div>
          <div class="data-item">
            <span class="data-label">安全等级:</span>
            <span class="data-value good">安全</span>
          </div>
        </div>
      </div>

      <!-- 时间显示 -->
      <div class="time-display">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      <div class="notification-icon">{{ getNotificationIcon(notification.type) }}</div>
      <div class="notification-content">
        <div class="notification-title">{{ notification.title }}</div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as Cesium from 'cesium';

// 响应式数据
const cesiumContainer = ref(null);
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const isLoading = ref(true);
const loadingText = ref('正在加载智慧城市系统...');
const notification = ref(null);
const currentTime = ref('');
const currentDate = ref('');

// 功能开关
const showBuildings = ref(true);
const showTraffic = ref(false);
const showWeather = ref(true);
const showPOI = ref(true);
const showSensors = ref(false);
const showDrones = ref(false);

// 城市数据
const weatherData = ref({
  temperature: 24,
  windSpeed: 12,
  humidity: 68
});

const cityData = ref({
  population: '244.9万'
});

// 系统变量
let viewer = null;
let timeInterval = null;
let weatherInterval = null;
let entities = {
  buildings: [],
  traffic: [],
  weather: [],
  poi: [],
  sensors: [],
  drones: []
};

// 珠海区域数据
const zhuhaiAreas = {
  xiangzhou: {
    name: '香洲区',
    position: [113.5767, 22.2707, 50000],
    description: '珠海市政治、经济、文化中心',
    icon: '🏢'
  },
  jinwan: {
    name: '金湾区',
    position: [113.3600, 22.1400, 30000],
    description: '珠海国际机场所在地',
    icon: '✈️'
  },
  doumen: {
    name: '斗门区',
    position: [113.2900, 22.2100, 40000],
    description: '农业和生态旅游区',
    icon: '🌾'
  },
  hengqin: {
    name: '横琴新区',
    position: [113.4200, 22.1300, 25000],
    description: '粤港澳大湾区重要平台',
    icon: '🏗️'
  }
};

// 组件挂载
onMounted(async () => {
  try {
    await initializeSmartCity();
    startTimeUpdate();
    startWeatherUpdate();
  } catch (error) {
    console.error('智慧城市系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    isLoading.value = false;
  }
});

// 组件卸载
onUnmounted(() => {
  cleanup();
});

// 初始化智慧城市系统
async function initializeSmartCity() {
  await nextTick();
  
  loadingText.value = '正在连接卫星数据...';
  systemStatus.value = { text: '连接中...', class: 'loading' };
  
  // 设置Cesium访问令牌
  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
  
  loadingText.value = '正在创建3D地球...';
  
  // 创建Cesium Viewer
  viewer = new Cesium.Viewer(cesiumContainer.value, {
    homeButton: false,
    sceneModePicker: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    animation: false,
    timeline: false,
    fullscreenButton: false,
    geocoder: false,
    infoBox: false,
    selectionIndicator: false,
    terrainProvider: await createTerrainProvider(),
    imageryProvider: await createImageryProvider()
  });

  loadingText.value = '正在配置智慧城市效果...';
  
  // 设置初始视图到珠海
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    }
  });

  // 启用智慧城市效果
  setupSmartCityEffects();
  
  loadingText.value = '正在加载城市数据...';
  
  // 加载城市数据
  await loadCityData();
  
  systemStatus.value = { text: '🟢 系统在线', class: 'online' };
  isLoading.value = false;
  
  showNotification('success', '智慧珠海', '系统初始化完成，欢迎使用智慧城市平台！');
  
  // 自动展示城市特色
  setTimeout(() => {
    showcaseCity();
  }, 2000);
}

// 创建地形提供商
async function createTerrainProvider() {
  try {
    return Cesium.createWorldTerrain();
  } catch (error) {
    console.warn('世界地形加载失败，使用椭球地形');
    return new Cesium.EllipsoidTerrainProvider();
  }
}

// 创建影像提供商
async function createImageryProvider() {
  try {
    return new Cesium.IonImageryProvider({ assetId: 3954 });
  } catch (error) {
    console.warn('Ion影像加载失败，使用备用方案');
    try {
      return new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      });
    } catch (osmError) {
      console.warn('OpenStreetMap也失败，使用本地方案');
      return createFallbackImageryProvider();
    }
  }
}

// 创建备用影像提供商
function createFallbackImageryProvider() {
  const canvas = document.createElement('canvas');
  canvas.width = 256;
  canvas.height = 256;
  const ctx = canvas.getContext('2d');

  // 绘制蓝绿色地球纹理
  const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
  gradient.addColorStop(0, '#4a90e2');
  gradient.addColorStop(0.7, '#2e7d32');
  gradient.addColorStop(1, '#1565c0');

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, 256, 256);

  // 添加一些陆地形状
  ctx.fillStyle = '#4caf50';
  ctx.beginPath();
  ctx.arc(128, 128, 60, 0, 2 * Math.PI);
  ctx.fill();

  // 添加珠海标记
  ctx.fillStyle = '#ff6b35';
  ctx.beginPath();
  ctx.arc(140, 120, 8, 0, 2 * Math.PI);
  ctx.fill();

  return new Cesium.SingleTileImageryProvider({
    url: canvas.toDataURL(),
    rectangle: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90)
  });
}

// 设置智慧城市效果
function setupSmartCityEffects() {
  // 启用光照
  viewer.scene.globe.enableLighting = true;
  
  // 设置大气效果
  viewer.scene.skyAtmosphere.show = true;
  viewer.scene.fog.enabled = true;
  viewer.scene.fog.density = 0.0001;
  
  // 设置地球颜色
  viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#1a4b3a');
  
  // 设置背景色
  viewer.scene.backgroundColor = Cesium.Color.fromCssColorString('#001122');
}

// 加载城市数据
async function loadCityData() {
  // 加载POI数据
  if (showPOI.value) {
    await loadPOIData();
  }
  
  // 加载建筑物数据
  if (showBuildings.value) {
    await loadBuildingData();
  }
  
  // 加载天气数据
  if (showWeather.value) {
    await loadWeatherData();
  }
}

// 加载POI数据
async function loadPOIData() {
  const pois = [
    { name: '珠海市政府', lng: 113.5767, lat: 22.2707, type: 'government', icon: '🏛️' },
    { name: '珠海机场', lng: 113.3600, lat: 22.1400, type: 'transport', icon: '✈️' },
    { name: '港珠澳大桥', lng: 113.5200, lat: 22.2100, type: 'landmark', icon: '🌉' },
    { name: '珠海大剧院', lng: 113.5800, lat: 22.2600, type: 'culture', icon: '🎭' },
    { name: '长隆海洋王国', lng: 113.4300, lat: 22.1200, type: 'tourism', icon: '🐋' }
  ];

  pois.forEach(poi => {
    const entity = viewer.entities.add({
      name: poi.name,
      position: Cesium.Cartesian3.fromDegrees(poi.lng, poi.lat, 100),
      billboard: {
        image: createPOIIcon(poi.icon, getPOIColor(poi.type)),
        scale: 1.2,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: poi.name,
        font: '14pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -60),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
    
    entities.poi.push(entity);
  });
}

// 加载建筑物数据
async function loadBuildingData() {
  const buildings = [
    {
      name: '珠海中心大厦',
      lng: 113.5767,
      lat: 22.2707,
      height: 280,
      width: 80,
      depth: 80,
      type: 'skyscraper',
      floors: 60
    },
    {
      name: '华发商都',
      lng: 113.5800,
      lat: 22.2650,
      height: 180,
      width: 120,
      depth: 100,
      type: 'commercial',
      floors: 35
    },
    {
      name: '珠海国际会展中心',
      lng: 113.5850,
      lat: 22.2600,
      height: 60,
      width: 200,
      depth: 150,
      type: 'exhibition',
      floors: 8
    },
    {
      name: '横琴金融大厦',
      lng: 113.4300,
      lat: 22.1300,
      height: 220,
      width: 70,
      depth: 70,
      type: 'finance',
      floors: 45
    },
    {
      name: '珠海大剧院',
      lng: 113.5800,
      lat: 22.2600,
      height: 45,
      width: 150,
      depth: 120,
      type: 'culture',
      floors: 6
    },
    {
      name: '金湾机场航站楼',
      lng: 113.3600,
      lat: 22.1400,
      height: 35,
      width: 300,
      depth: 200,
      type: 'airport',
      floors: 4
    }
  ];

  buildings.forEach(building => {
    // 创建主建筑体
    const mainBuilding = viewer.entities.add({
      name: building.name,
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat, building.height / 2),
      box: {
        dimensions: new Cesium.Cartesian3(building.width, building.depth, building.height),
        material: getBuildingMaterial(building.type),
        outline: true,
        outlineColor: getBuildingOutlineColor(building.type)
      },
      label: {
        text: `${building.name}\n${building.floors}层`,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -building.height / 2 - 30),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.buildings.push(mainBuilding);

    // 添加建筑细节
    addBuildingDetails(building);
  });
}

// 添加建筑细节
function addBuildingDetails(building) {
  // 添加天线/装饰
  if (building.type === 'skyscraper' || building.type === 'finance') {
    const antenna = viewer.entities.add({
      name: `${building.name}_天线`,
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat, building.height + 20),
      cylinder: {
        length: 40,
        topRadius: 2,
        bottomRadius: 5,
        material: Cesium.Color.SILVER,
        outline: true,
        outlineColor: Cesium.Color.WHITE
      }
    });
    entities.buildings.push(antenna);
  }

  // 添加停机坪（机场）
  if (building.type === 'airport') {
    const helipad = viewer.entities.add({
      name: `${building.name}_停机坪`,
      position: Cesium.Cartesian3.fromDegrees(building.lng + 0.002, building.lat, building.height + 5),
      cylinder: {
        length: 2,
        topRadius: 30,
        bottomRadius: 30,
        material: Cesium.Color.YELLOW.withAlpha(0.8),
        outline: true,
        outlineColor: Cesium.Color.ORANGE
      }
    });
    entities.buildings.push(helipad);
  }

  // 添加LED灯光效果（夜景）
  addBuildingLights(building);
}

// 添加建筑灯光效果
function addBuildingLights(building) {
  // 顶部灯光
  const topLight = viewer.entities.add({
    name: `${building.name}_顶灯`,
    position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat, building.height + 5),
    point: {
      pixelSize: 15,
      color: getBuildingLightColor(building.type),
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
    }
  });
  entities.buildings.push(topLight);

  // 楼层灯光（每10层一个）
  for (let floor = 10; floor < building.floors; floor += 10) {
    const floorHeight = (building.height / building.floors) * floor;
    const floorLight = viewer.entities.add({
      name: `${building.name}_${floor}层灯`,
      position: Cesium.Cartesian3.fromDegrees(
        building.lng + (Math.random() - 0.5) * 0.0005,
        building.lat + (Math.random() - 0.5) * 0.0005,
        floorHeight
      ),
      point: {
        pixelSize: 8,
        color: Cesium.Color.YELLOW.withAlpha(0.8),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });
    entities.buildings.push(floorLight);
  }
}

// 加载天气数据
async function loadWeatherData() {
  // 创建多层天气效果
  const weatherLayers = [
    {
      name: '低层云',
      height: 2000,
      radius: 15000,
      color: Cesium.Color.LIGHTBLUE.withAlpha(0.3),
      type: 'cloud'
    },
    {
      name: '中层云',
      height: 4000,
      radius: 18000,
      color: Cesium.Color.WHITE.withAlpha(0.2),
      type: 'cloud'
    },
    {
      name: '高层云',
      height: 6000,
      radius: 12000,
      color: Cesium.Color.LIGHTGRAY.withAlpha(0.15),
      type: 'cloud'
    }
  ];

  weatherLayers.forEach(layer => {
    const weatherEntity = viewer.entities.add({
      name: layer.name,
      position: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, layer.height),
      ellipse: {
        semiMinorAxis: layer.radius,
        semiMajorAxis: layer.radius,
        material: layer.color,
        outline: true,
        outlineColor: layer.color.withAlpha(0.5),
        height: layer.height
      }
    });

    entities.weather.push(weatherEntity);
  });

  // 添加降水效果
  addPrecipitationEffect();

  // 添加风向指示
  addWindIndicators();

  // 添加温度分布
  addTemperatureZones();
}

// 添加降水效果
function addPrecipitationEffect() {
  const precipitationAreas = [
    { lng: 113.5767, lat: 22.2707, intensity: 'light', type: 'rain' },
    { lng: 113.4300, lat: 22.1300, intensity: 'moderate', type: 'rain' },
    { lng: 113.5200, lat: 22.2100, intensity: 'heavy', type: 'rain' }
  ];

  precipitationAreas.forEach(area => {
    const precipitationEntity = viewer.entities.add({
      name: `${area.type}_${area.intensity}`,
      position: Cesium.Cartesian3.fromDegrees(area.lng, area.lat, 1000),
      cylinder: {
        length: 2000,
        topRadius: getPrecipitationRadius(area.intensity),
        bottomRadius: getPrecipitationRadius(area.intensity) * 1.5,
        material: getPrecipitationMaterial(area.type, area.intensity),
        outline: true,
        outlineColor: getPrecipitationColor(area.type)
      }
    });

    entities.weather.push(precipitationEntity);
  });
}

// 添加风向指示
function addWindIndicators() {
  const windPoints = [
    { lng: 113.5767, lat: 22.2707, direction: 45, speed: 12 },
    { lng: 113.5800, lat: 22.2650, direction: 30, speed: 8 },
    { lng: 113.4300, lat: 22.1300, direction: 60, speed: 15 }
  ];

  windPoints.forEach(wind => {
    // 风向箭头
    const windArrow = viewer.entities.add({
      name: `风向指示_${wind.speed}km/h`,
      position: Cesium.Cartesian3.fromDegrees(wind.lng, wind.lat, 100),
      cylinder: {
        length: wind.speed * 2,
        topRadius: 2,
        bottomRadius: 8,
        material: Cesium.Color.CYAN.withAlpha(0.7),
        outline: true,
        outlineColor: Cesium.Color.BLUE
      }
    });

    entities.weather.push(windArrow);
  });
}

// 添加温度分布区域
function addTemperatureZones() {
  const temperatureZones = [
    { lng: 113.5767, lat: 22.2707, temp: 24, radius: 5000, color: Cesium.Color.YELLOW },
    { lng: 113.5800, lat: 22.2650, temp: 26, radius: 4000, color: Cesium.Color.ORANGE },
    { lng: 113.4300, lat: 22.1300, temp: 22, radius: 6000, color: Cesium.Color.LIGHTBLUE }
  ];

  temperatureZones.forEach(zone => {
    const tempZone = viewer.entities.add({
      name: `温度区域_${zone.temp}°C`,
      position: Cesium.Cartesian3.fromDegrees(zone.lng, zone.lat, 50),
      ellipse: {
        semiMinorAxis: zone.radius,
        semiMajorAxis: zone.radius,
        material: zone.color.withAlpha(0.3),
        outline: true,
        outlineColor: zone.color,
        height: 50
      },
      label: {
        text: `${zone.temp}°C`,
        font: '14pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, 0),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.weather.push(tempZone);
  });
}

// 获取降水半径
function getPrecipitationRadius(intensity) {
  const radii = {
    light: 1000,
    moderate: 1500,
    heavy: 2000
  };
  return radii[intensity] || 1000;
}

// 获取降水材质
function getPrecipitationMaterial(type, intensity) {
  const alpha = {
    light: 0.3,
    moderate: 0.5,
    heavy: 0.7
  };

  if (type === 'rain') {
    return Cesium.Color.BLUE.withAlpha(alpha[intensity]);
  } else if (type === 'snow') {
    return Cesium.Color.WHITE.withAlpha(alpha[intensity]);
  }

  return Cesium.Color.GRAY.withAlpha(alpha[intensity]);
}

// 获取降水颜色
function getPrecipitationColor(type) {
  const colors = {
    rain: Cesium.Color.BLUE,
    snow: Cesium.Color.WHITE,
    hail: Cesium.Color.GRAY
  };
  return colors[type] || Cesium.Color.BLUE;
}

// 展示城市特色
function showcaseCity() {
  showNotification('info', '智慧珠海', '正在为您展示珠海市智慧城市特色...');

  // 依次展示各个区域
  setTimeout(() => flyToArea('xiangzhou'), 1000);
  setTimeout(() => flyToArea('hengqin'), 4000);
  setTimeout(() => flyToArea('jinwan'), 7000);
  setTimeout(() => flyToArea('doumen'), 10000);
  setTimeout(() => {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000),
      duration: 3
    });
  }, 13000);
}

// 飞行到指定区域
function flyToArea(areaKey) {
  const area = zhuhaiAreas[areaKey];
  if (!area || !viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(area.position[0], area.position[1], area.position[2]),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3
  });

  showNotification('info', area.name, area.description);
}

// 切换功能
function toggleBuildings() {
  showBuildings.value = !showBuildings.value;
  entities.buildings.forEach(entity => {
    entity.show = showBuildings.value;
  });
  showNotification('info', '建筑物', showBuildings.value ? '已显示' : '已隐藏');
}

function toggleTraffic() {
  showTraffic.value = !showTraffic.value;
  if (showTraffic.value) {
    loadTrafficData();
  } else {
    entities.traffic.forEach(entity => {
      viewer.entities.remove(entity);
    });
    entities.traffic = [];
  }
  showNotification('info', '交通流', showTraffic.value ? '已显示' : '已隐藏');
}

function toggleWeather() {
  showWeather.value = !showWeather.value;
  entities.weather.forEach(entity => {
    entity.show = showWeather.value;
  });
  showNotification('info', '天气层', showWeather.value ? '已显示' : '已隐藏');
}

function togglePOI() {
  showPOI.value = !showPOI.value;
  entities.poi.forEach(entity => {
    entity.show = showPOI.value;
  });
  showNotification('info', '兴趣点', showPOI.value ? '已显示' : '已隐藏');
}

function toggleSensors() {
  showSensors.value = !showSensors.value;
  if (showSensors.value) {
    loadSensorData();
  } else {
    entities.sensors.forEach(entity => {
      viewer.entities.remove(entity);
    });
    entities.sensors = [];
  }
  showNotification('info', '传感器', showSensors.value ? '已显示' : '已隐藏');
}

function toggleDrones() {
  showDrones.value = !showDrones.value;
  if (showDrones.value) {
    loadDroneData();
  } else {
    entities.drones.forEach(entity => {
      viewer.entities.remove(entity);
    });
    entities.drones = [];
  }
  showNotification('info', '无人机', showDrones.value ? '已显示' : '已隐藏');
}

// 加载交通数据
function loadTrafficData() {
  const trafficRoutes = [
    {
      name: '香洲-华发商都快速路',
      start: [113.5767, 22.2707],
      end: [113.5800, 22.2650],
      color: Cesium.Color.GREEN,
      width: 12,
      height: 15,
      type: 'highway'
    },
    {
      name: '珠海大道',
      start: [113.5800, 22.2650],
      end: [113.4300, 22.1300],
      color: Cesium.Color.YELLOW,
      width: 10,
      height: 10,
      type: 'main_road'
    },
    {
      name: '机场高速',
      start: [113.4300, 22.1300],
      end: [113.3600, 22.1400],
      color: Cesium.Color.RED,
      width: 15,
      height: 20,
      type: 'highway'
    },
    {
      name: '港珠澳大桥连接线',
      start: [113.5200, 22.2100],
      end: [113.5500, 22.2300],
      color: Cesium.Color.BLUE,
      width: 20,
      height: 30,
      type: 'bridge'
    }
  ];

  trafficRoutes.forEach((route, index) => {
    // 创建主要道路
    const roadEntity = viewer.entities.add({
      name: route.name,
      polyline: {
        positions: [
          Cesium.Cartesian3.fromDegrees(route.start[0], route.start[1], route.height),
          Cesium.Cartesian3.fromDegrees(route.end[0], route.end[1], route.height)
        ],
        width: route.width,
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: 0.3,
          color: route.color
        }),
        clampToGround: false
      }
    });

    entities.traffic.push(roadEntity);

    // 添加道路护栏（高速公路）
    if (route.type === 'highway' || route.type === 'bridge') {
      addRoadBarriers(route, index);
    }

    // 添加交通流动效果
    addTrafficFlow(route, index);

    // 添加路灯
    addStreetLights(route, index);
  });

  // 添加立交桥
  addOverpass();

  // 添加交通信号灯
  addTrafficLights();
}

// 添加道路护栏
function addRoadBarriers(route, index) {
  const barrierPositions = [];
  const steps = 20;

  for (let i = 0; i <= steps; i++) {
    const fraction = i / steps;
    const lng = route.start[0] + (route.end[0] - route.start[0]) * fraction;
    const lat = route.start[1] + (route.end[1] - route.start[1]) * fraction;
    barrierPositions.push(Cesium.Cartesian3.fromDegrees(lng, lat, route.height + 5));
  }

  const leftBarrier = viewer.entities.add({
    name: `${route.name}_左护栏`,
    polyline: {
      positions: barrierPositions,
      width: 3,
      material: Cesium.Color.SILVER,
      clampToGround: false
    }
  });

  entities.traffic.push(leftBarrier);
}

// 添加交通流动效果
function addTrafficFlow(route, index) {
  // 创建移动的车辆点
  for (let i = 0; i < 3; i++) {
    const vehicleEntity = viewer.entities.add({
      name: `${route.name}_车辆${i + 1}`,
      position: Cesium.Cartesian3.fromDegrees(
        route.start[0] + Math.random() * (route.end[0] - route.start[0]),
        route.start[1] + Math.random() * (route.end[1] - route.start[1]),
        route.height + 2
      ),
      point: {
        pixelSize: 8,
        color: getVehicleColor(),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 1,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.traffic.push(vehicleEntity);
  }
}

// 添加路灯
function addStreetLights(route, index) {
  const lightCount = 5;

  for (let i = 0; i <= lightCount; i++) {
    const fraction = i / lightCount;
    const lng = route.start[0] + (route.end[0] - route.start[0]) * fraction;
    const lat = route.start[1] + (route.end[1] - route.start[1]) * fraction;

    const streetLight = viewer.entities.add({
      name: `${route.name}_路灯${i + 1}`,
      position: Cesium.Cartesian3.fromDegrees(lng + 0.0002, lat + 0.0002, 0),
      cylinder: {
        length: 12,
        topRadius: 0.5,
        bottomRadius: 1,
        material: Cesium.Color.DARKGRAY,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    // 路灯顶部光源
    const lightTop = viewer.entities.add({
      name: `${route.name}_路灯光源${i + 1}`,
      position: Cesium.Cartesian3.fromDegrees(lng + 0.0002, lat + 0.0002, 12),
      point: {
        pixelSize: 12,
        color: Cesium.Color.YELLOW.withAlpha(0.8),
        outlineColor: Cesium.Color.ORANGE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.traffic.push(streetLight);
    entities.traffic.push(lightTop);
  }
}

// 添加立交桥
function addOverpass() {
  const overpassEntity = viewer.entities.add({
    name: '珠海大道立交桥',
    position: Cesium.Cartesian3.fromDegrees(113.5600, 22.2400, 25),
    ellipse: {
      semiMinorAxis: 100,
      semiMajorAxis: 200,
      material: Cesium.Color.LIGHTGRAY.withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.DARKGRAY,
      height: 25
    }
  });

  entities.traffic.push(overpassEntity);

  // 立交桥支柱
  for (let i = 0; i < 4; i++) {
    const angle = (i * Math.PI) / 2;
    const lng = 113.5600 + Math.cos(angle) * 0.001;
    const lat = 22.2400 + Math.sin(angle) * 0.0005;

    const pillar = viewer.entities.add({
      name: `立交桥支柱${i + 1}`,
      position: Cesium.Cartesian3.fromDegrees(lng, lat, 12.5),
      cylinder: {
        length: 25,
        topRadius: 3,
        bottomRadius: 5,
        material: Cesium.Color.CONCRETE,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    entities.traffic.push(pillar);
  }
}

// 添加交通信号灯
function addTrafficLights() {
  const intersections = [
    { lng: 113.5780, lat: 22.2680, name: '香洲路口' },
    { lng: 113.5650, lat: 22.2500, name: '珠海大道路口' },
    { lng: 113.4400, lat: 22.1350, name: '横琴路口' }
  ];

  intersections.forEach(intersection => {
    // 信号灯杆
    const lightPole = viewer.entities.add({
      name: `${intersection.name}_信号灯杆`,
      position: Cesium.Cartesian3.fromDegrees(intersection.lng, intersection.lat, 0),
      cylinder: {
        length: 8,
        topRadius: 0.3,
        bottomRadius: 0.5,
        material: Cesium.Color.DARKGRAY,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    // 红绿灯
    const redLight = viewer.entities.add({
      name: `${intersection.name}_红灯`,
      position: Cesium.Cartesian3.fromDegrees(intersection.lng, intersection.lat, 7),
      point: {
        pixelSize: 6,
        color: Math.random() > 0.5 ? Cesium.Color.RED : Cesium.Color.GRAY,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    const greenLight = viewer.entities.add({
      name: `${intersection.name}_绿灯`,
      position: Cesium.Cartesian3.fromDegrees(intersection.lng, intersection.lat, 6),
      point: {
        pixelSize: 6,
        color: Math.random() > 0.5 ? Cesium.Color.GREEN : Cesium.Color.GRAY,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.traffic.push(lightPole);
    entities.traffic.push(redLight);
    entities.traffic.push(greenLight);
  });
}

// 获取车辆颜色
function getVehicleColor() {
  const colors = [
    Cesium.Color.WHITE,
    Cesium.Color.BLACK,
    Cesium.Color.RED,
    Cesium.Color.BLUE,
    Cesium.Color.SILVER
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}

// 加载传感器数据
function loadSensorData() {
  const sensors = [
    {
      name: '空气质量监测站',
      lng: 113.5767,
      lat: 22.2707,
      type: 'air',
      height: 15,
      radius: 8,
      data: { aqi: 45, pm25: 12, status: '优' }
    },
    {
      name: '交通监控摄像头',
      lng: 113.5800,
      lat: 22.2650,
      type: 'traffic',
      height: 12,
      radius: 5,
      data: { vehicles: 156, speed: '45km/h', status: '畅通' }
    },
    {
      name: '噪音监测设备',
      lng: 113.4300,
      lat: 22.1300,
      type: 'noise',
      height: 10,
      radius: 6,
      data: { decibel: 42, level: '安静', status: '正常' }
    },
    {
      name: '气象监测站',
      lng: 113.5850,
      lat: 22.2600,
      type: 'weather',
      height: 20,
      radius: 10,
      data: { temp: 24, humidity: 68, wind: '12km/h' }
    },
    {
      name: '水质监测点',
      lng: 113.5200,
      lat: 22.2100,
      type: 'water',
      height: 8,
      radius: 7,
      data: { ph: 7.2, oxygen: '8.5mg/L', status: '良好' }
    }
  ];

  sensors.forEach(sensor => {
    // 传感器主体
    const sensorEntity = viewer.entities.add({
      name: sensor.name,
      position: Cesium.Cartesian3.fromDegrees(sensor.lng, sensor.lat, sensor.height / 2),
      cylinder: {
        length: sensor.height,
        topRadius: sensor.radius,
        bottomRadius: sensor.radius * 1.2,
        material: getSensorMaterial(sensor.type),
        outline: true,
        outlineColor: getSensorColor(sensor.type),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    // 传感器顶部指示灯
    const indicatorLight = viewer.entities.add({
      name: `${sensor.name}_指示灯`,
      position: Cesium.Cartesian3.fromDegrees(sensor.lng, sensor.lat, sensor.height + 2),
      point: {
        pixelSize: 12,
        color: getSensorStatusColor(sensor.data.status),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    // 传感器数据显示
    const dataLabel = viewer.entities.add({
      name: `${sensor.name}_数据`,
      position: Cesium.Cartesian3.fromDegrees(sensor.lng, sensor.lat, sensor.height + 8),
      label: {
        text: getSensorDataText(sensor),
        font: '10pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, 0),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
        backgroundColor: getSensorColor(sensor.type).withAlpha(0.7),
        backgroundPadding: new Cesium.Cartesian2(8, 4)
      }
    });

    // 传感器信号波纹效果
    addSensorSignalEffect(sensor);

    entities.sensors.push(sensorEntity);
    entities.sensors.push(indicatorLight);
    entities.sensors.push(dataLabel);
  });
}

// 添加传感器信号效果
function addSensorSignalEffect(sensor) {
  for (let i = 1; i <= 3; i++) {
    const signalRing = viewer.entities.add({
      name: `${sensor.name}_信号${i}`,
      position: Cesium.Cartesian3.fromDegrees(sensor.lng, sensor.lat, sensor.height + 5),
      ellipse: {
        semiMinorAxis: sensor.radius * 2 * i,
        semiMajorAxis: sensor.radius * 2 * i,
        material: getSensorColor(sensor.type).withAlpha(0.3 / i),
        outline: true,
        outlineColor: getSensorColor(sensor.type).withAlpha(0.5 / i),
        height: sensor.height + 5
      }
    });

    entities.sensors.push(signalRing);
  }
}

// 加载无人机数据
function loadDroneData() {
  const drones = [
    {
      name: '安防巡逻无人机Alpha',
      lng: 113.5767,
      lat: 22.2707,
      height: 150,
      type: 'patrol',
      status: '巡逻中',
      battery: 85,
      route: [
        [113.5767, 22.2707],
        [113.5800, 22.2650],
        [113.5850, 22.2600],
        [113.5767, 22.2707]
      ]
    },
    {
      name: '交通监控无人机Beta',
      lng: 113.4300,
      lat: 22.1300,
      height: 120,
      type: 'traffic',
      status: '监控中',
      battery: 92,
      route: [
        [113.4300, 22.1300],
        [113.4400, 22.1350],
        [113.4500, 22.1400],
        [113.4300, 22.1300]
      ]
    },
    {
      name: '环境监测无人机Gamma',
      lng: 113.5200,
      lat: 22.2100,
      height: 200,
      type: 'environment',
      status: '采样中',
      battery: 78,
      route: [
        [113.5200, 22.2100],
        [113.5300, 22.2150],
        [113.5400, 22.2200],
        [113.5200, 22.2100]
      ]
    }
  ];

  drones.forEach((drone, index) => {
    // 无人机主体
    const droneEntity = viewer.entities.add({
      name: drone.name,
      position: Cesium.Cartesian3.fromDegrees(drone.lng, drone.lat, drone.height),
      box: {
        dimensions: new Cesium.Cartesian3(8, 8, 3),
        material: getDroneMaterial(drone.type),
        outline: true,
        outlineColor: getDroneColor(drone.type)
      }
    });

    // 无人机螺旋桨
    addDronePropellers(drone);

    // 无人机状态指示灯
    const statusLight = viewer.entities.add({
      name: `${drone.name}_状态灯`,
      position: Cesium.Cartesian3.fromDegrees(drone.lng, drone.lat, drone.height + 3),
      point: {
        pixelSize: 8,
        color: getDroneStatusColor(drone.status),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 1,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    // 无人机信息标签
    const infoLabel = viewer.entities.add({
      name: `${drone.name}_信息`,
      position: Cesium.Cartesian3.fromDegrees(drone.lng, drone.lat, drone.height + 15),
      label: {
        text: `${drone.name}\n${drone.status}\n电量: ${drone.battery}%`,
        font: '10pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, 0),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
        backgroundColor: getDroneColor(drone.type).withAlpha(0.8),
        backgroundPadding: new Cesium.Cartesian2(8, 4)
      }
    });

    // 无人机巡逻路径
    addDroneRoute(drone);

    // 无人机扫描区域
    addDroneScanArea(drone);

    // 启动无人机动画
    animateDrone(drone, index);

    entities.drones.push(droneEntity);
    entities.drones.push(statusLight);
    entities.drones.push(infoLabel);
  });
}

// 添加无人机螺旋桨
function addDronePropellers(drone) {
  const propellerPositions = [
    [drone.lng - 0.00005, drone.lat - 0.00005],
    [drone.lng + 0.00005, drone.lat - 0.00005],
    [drone.lng - 0.00005, drone.lat + 0.00005],
    [drone.lng + 0.00005, drone.lat + 0.00005]
  ];

  propellerPositions.forEach((pos, index) => {
    const propeller = viewer.entities.add({
      name: `${drone.name}_螺旋桨${index + 1}`,
      position: Cesium.Cartesian3.fromDegrees(pos[0], pos[1], drone.height + 1),
      cylinder: {
        length: 0.5,
        topRadius: 3,
        bottomRadius: 3,
        material: Cesium.Color.DARKGRAY.withAlpha(0.8),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.drones.push(propeller);
  });
}

// 添加无人机巡逻路径
function addDroneRoute(drone) {
  const routePositions = drone.route.map(point =>
    Cesium.Cartesian3.fromDegrees(point[0], point[1], drone.height)
  );

  const routeLine = viewer.entities.add({
    name: `${drone.name}_巡逻路径`,
    polyline: {
      positions: routePositions,
      width: 3,
      material: new Cesium.PolylineDashMaterialProperty({
        color: getDroneColor(drone.type),
        dashLength: 10
      }),
      clampToGround: false
    }
  });

  entities.drones.push(routeLine);
}

// 添加无人机扫描区域
function addDroneScanArea(drone) {
  const scanArea = viewer.entities.add({
    name: `${drone.name}_扫描区域`,
    position: Cesium.Cartesian3.fromDegrees(drone.lng, drone.lat, drone.height - 10),
    ellipse: {
      semiMinorAxis: 100,
      semiMajorAxis: 100,
      material: getDroneColor(drone.type).withAlpha(0.2),
      outline: true,
      outlineColor: getDroneColor(drone.type).withAlpha(0.5),
      height: drone.height - 10
    }
  });

  entities.drones.push(scanArea);
}

// 无人机动画
function animateDrone(drone, index) {
  // 简单的上下浮动动画
  setInterval(() => {
    const currentTime = Date.now();
    const offset = Math.sin(currentTime * 0.001 + index) * 5;

    // 这里可以添加更复杂的路径动画
    // 由于Cesium的实体位置更新比较复杂，这里只是示例
  }, 100);
}

// 创建POI图标
function createPOIIcon(icon, color) {
  const canvas = document.createElement('canvas');
  canvas.width = 64;
  canvas.height = 64;
  const ctx = canvas.getContext('2d');

  // 绘制圆形背景
  ctx.fillStyle = color;
  ctx.beginPath();
  ctx.arc(32, 32, 28, 0, 2 * Math.PI);
  ctx.fill();

  // 绘制边框
  ctx.strokeStyle = '#FFFFFF';
  ctx.lineWidth = 4;
  ctx.stroke();

  // 绘制图标
  ctx.font = '24px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = '#FFFFFF';
  ctx.fillText(icon, 32, 32);

  return canvas.toDataURL();
}

// 获取POI颜色
function getPOIColor(type) {
  const colors = {
    government: '#FF6B35',
    transport: '#4ECDC4',
    landmark: '#45B7D1',
    culture: '#96CEB4',
    tourism: '#FFEAA7'
  };
  return colors[type] || '#95A5A6';
}

// 获取建筑材质
function getBuildingMaterial(type) {
  const materials = {
    skyscraper: new Cesium.ColorMaterialProperty(Cesium.Color.CYAN.withAlpha(0.8)),
    commercial: new Cesium.ColorMaterialProperty(Cesium.Color.ORANGE.withAlpha(0.7)),
    exhibition: new Cesium.ColorMaterialProperty(Cesium.Color.PURPLE.withAlpha(0.6)),
    finance: new Cesium.ColorMaterialProperty(Cesium.Color.GOLD.withAlpha(0.8)),
    culture: new Cesium.ColorMaterialProperty(Cesium.Color.PINK.withAlpha(0.7)),
    airport: new Cesium.ColorMaterialProperty(Cesium.Color.LIGHTBLUE.withAlpha(0.6))
  };
  return materials[type] || new Cesium.ColorMaterialProperty(Cesium.Color.GRAY.withAlpha(0.7));
}

// 获取建筑轮廓颜色
function getBuildingOutlineColor(type) {
  const colors = {
    skyscraper: Cesium.Color.CYAN,
    commercial: Cesium.Color.ORANGE,
    exhibition: Cesium.Color.PURPLE,
    finance: Cesium.Color.GOLD,
    culture: Cesium.Color.PINK,
    airport: Cesium.Color.LIGHTBLUE
  };
  return colors[type] || Cesium.Color.WHITE;
}

// 获取建筑灯光颜色
function getBuildingLightColor(type) {
  const colors = {
    skyscraper: Cesium.Color.CYAN,
    commercial: Cesium.Color.ORANGE,
    exhibition: Cesium.Color.PURPLE,
    finance: Cesium.Color.GOLD,
    culture: Cesium.Color.PINK,
    airport: Cesium.Color.LIGHTBLUE
  };
  return colors[type] || Cesium.Color.WHITE;
}

// 获取传感器材质
function getSensorMaterial(type) {
  const materials = {
    air: new Cesium.ColorMaterialProperty(Cesium.Color.GREEN.withAlpha(0.8)),
    traffic: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE.withAlpha(0.8)),
    noise: new Cesium.ColorMaterialProperty(Cesium.Color.ORANGE.withAlpha(0.8)),
    weather: new Cesium.ColorMaterialProperty(Cesium.Color.PURPLE.withAlpha(0.8)),
    water: new Cesium.ColorMaterialProperty(Cesium.Color.CYAN.withAlpha(0.8))
  };
  return materials[type] || new Cesium.ColorMaterialProperty(Cesium.Color.GRAY.withAlpha(0.8));
}

// 获取传感器颜色
function getSensorColor(type) {
  const colors = {
    air: Cesium.Color.GREEN,
    traffic: Cesium.Color.BLUE,
    noise: Cesium.Color.ORANGE,
    weather: Cesium.Color.PURPLE,
    water: Cesium.Color.CYAN
  };
  return colors[type] || Cesium.Color.GRAY;
}

// 获取传感器状态颜色
function getSensorStatusColor(status) {
  const colors = {
    '优': Cesium.Color.GREEN,
    '良好': Cesium.Color.LIGHTGREEN,
    '正常': Cesium.Color.YELLOW,
    '畅通': Cesium.Color.GREEN,
    '安静': Cesium.Color.BLUE
  };
  return colors[status] || Cesium.Color.WHITE;
}

// 获取传感器数据文本
function getSensorDataText(sensor) {
  switch (sensor.type) {
    case 'air':
      return `AQI: ${sensor.data.aqi}\nPM2.5: ${sensor.data.pm25}\n状态: ${sensor.data.status}`;
    case 'traffic':
      return `车辆: ${sensor.data.vehicles}\n速度: ${sensor.data.speed}\n状态: ${sensor.data.status}`;
    case 'noise':
      return `分贝: ${sensor.data.decibel}dB\n等级: ${sensor.data.level}\n状态: ${sensor.data.status}`;
    case 'weather':
      return `温度: ${sensor.data.temp}°C\n湿度: ${sensor.data.humidity}%\n风速: ${sensor.data.wind}`;
    case 'water':
      return `pH: ${sensor.data.ph}\n溶氧: ${sensor.data.oxygen}\n状态: ${sensor.data.status}`;
    default:
      return '数据获取中...';
  }
}

// 获取无人机材质
function getDroneMaterial(type) {
  const materials = {
    patrol: new Cesium.ColorMaterialProperty(Cesium.Color.YELLOW.withAlpha(0.9)),
    traffic: new Cesium.ColorMaterialProperty(Cesium.Color.BLUE.withAlpha(0.9)),
    environment: new Cesium.ColorMaterialProperty(Cesium.Color.GREEN.withAlpha(0.9))
  };
  return materials[type] || new Cesium.ColorMaterialProperty(Cesium.Color.GRAY.withAlpha(0.9));
}

// 获取无人机颜色
function getDroneColor(type) {
  const colors = {
    patrol: Cesium.Color.YELLOW,
    traffic: Cesium.Color.BLUE,
    environment: Cesium.Color.GREEN
  };
  return colors[type] || Cesium.Color.GRAY;
}

// 获取无人机状态颜色
function getDroneStatusColor(status) {
  const colors = {
    '巡逻中': Cesium.Color.GREEN,
    '监控中': Cesium.Color.BLUE,
    '采样中': Cesium.Color.ORANGE,
    '返航中': Cesium.Color.YELLOW,
    '充电中': Cesium.Color.RED
  };
  return colors[status] || Cesium.Color.WHITE;
}

// 创建无人机模型
function createDroneModel() {
  // 返回一个简单的几何体作为无人机模型
  return 'data:application/json;base64,eyJ2ZXJzaW9uIjoiMS4wIiwiZ2VuZXJhdG9yIjoiQ2VzaXVtIiwiZXh0ZW5zaW9ucyI6e319';
}

// 启动时间更新
function startTimeUpdate() {
  timeInterval = setInterval(() => {
    const now = new Date();
    currentTime.value = now.toLocaleTimeString('zh-CN');
    currentDate.value = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  }, 1000);
}

// 启动天气更新
function startWeatherUpdate() {
  weatherInterval = setInterval(() => {
    // 模拟天气数据更新
    weatherData.value.temperature = 20 + Math.random() * 10;
    weatherData.value.windSpeed = 5 + Math.random() * 20;
    weatherData.value.humidity = 50 + Math.random() * 30;
  }, 30000);
}

// 显示通知
function showNotification(type, title, message) {
  notification.value = { type, title, message };
  setTimeout(() => {
    notification.value = null;
  }, 4000);
}

// 获取通知图标
function getNotificationIcon(type) {
  const icons = {
    success: '✅',
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌'
  };
  return icons[type] || 'ℹ️';
}

// 清理资源
function cleanup() {
  if (timeInterval) {
    clearInterval(timeInterval);
  }

  if (weatherInterval) {
    clearInterval(weatherInterval);
  }

  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
}
</script>

<style scoped>
.smart-city-zhuhai {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

.cesium-viewer {
  width: 100%;
  height: 100%;
}

/* 智慧城市控制面板 */
.smart-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 90vh;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 25px;
  color: white;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.panel-header {
  text-align: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(74, 144, 226, 0.3);
}

.panel-header h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.loading {
  background: #ff9800;
}

.status-dot.online {
  background: #4caf50;
}

.status-dot.error {
  background: #f44336;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* 城市概览 */
.city-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(123, 104, 238, 0.1) 100%);
  border: 1px solid rgba(74, 144, 226, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.overview-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.2);
}

.item-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  border-radius: 10px;
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 12px;
  color: #b0bec5;
  margin-bottom: 4px;
}

.item-value {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

/* 智慧功能控制 */
.smart-controls {
  margin-bottom: 25px;
}

.smart-controls h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.smart-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.smart-btn:hover {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.3) 0%, rgba(123, 104, 238, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
}

.smart-btn.active {
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  border-color: #4a90e2;
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
}

/* 区域选择 */
.area-selection {
  margin-bottom: 25px;
}

.area-selection h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.area-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.area-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 10px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.area-btn:hover {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

/* 实时数据 */
.realtime-data {
  margin-bottom: 25px;
}

.realtime-data h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 13px;
}

.data-label {
  color: #b0bec5;
}

.data-value {
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
}

.data-value.good {
  background: rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.data-value.normal {
  background: rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

.data-value.warning {
  background: rgba(255, 152, 0, 0.3);
  color: #ff9800;
}

/* 时间显示 */
.time-display {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(123, 104, 238, 0.1) 100%);
  border-radius: 12px;
  border: 1px solid rgba(74, 144, 226, 0.2);
}

.current-time {
  font-size: 20px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 5px;
}

.current-date {
  font-size: 12px;
  color: #b0bec5;
}

/* 通知系统 */
.notification {
  position: fixed;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  color: white;
  z-index: 2000;
  max-width: 400px;
  animation: slideInDown 0.5s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.notification.success {
  border-left: 4px solid #4caf50;
}

.notification.info {
  border-left: 4px solid #2196f3;
}

.notification.warning {
  border-left: 4px solid #ff9800;
}

.notification.error {
  border-left: 4px solid #f44336;
}

.notification-icon {
  font-size: 24px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #b0bec5;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(12, 20, 69, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(74, 144, 226, 0.3);
  border-top: 4px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 16px;
  color: #4a90e2;
}

/* 动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 滚动条样式 */
.smart-panel::-webkit-scrollbar {
  width: 6px;
}

.smart-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.smart-panel::-webkit-scrollbar-thumb {
  background: rgba(74, 144, 226, 0.5);
  border-radius: 3px;
}

.smart-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(74, 144, 226, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .smart-panel {
    width: 300px;
    right: 10px;
    top: 10px;
    padding: 20px;
  }

  .city-overview {
    grid-template-columns: 1fr;
  }

  .control-grid,
  .area-grid {
    grid-template-columns: 1fr;
  }

  .notification {
    max-width: 90%;
    left: 5%;
    transform: none;
  }
}
</style>
