<template>
  <div class="control-panel">
    <div class="panel-header">
      <h2>低空气象预警控制台</h2>
    </div>

    <!-- 时间控制 -->
    <div class="control-section">
      <h3>时间控制</h3>
      <div class="time-controls">
        <div class="current-time">
          {{ formatTime(currentTime) }}
        </div>
        <div class="time-slider">
          <input
            type="range"
            :value="timeSliderValue"
            @input="onTimeSliderChange"
            min="0"
            max="1440"
            step="1"
          />
        </div>
        <div class="playback-controls">
          <button @click="$emit('play-pause')" class="play-btn">
            {{ isPlaying ? '⏸️' : '▶️' }}
          </button>
          <button @click="resetTime" class="reset-btn">🔄</button>
        </div>
      </div>
    </div>

    <!-- 图层控制 -->
    <div class="control-section">
      <h3>显示图层</h3>
      <div class="layer-controls">
        <div
          v-for="(layer, key) in weatherLayers"
          :key="key"
          class="layer-item"
        >
          <label class="layer-checkbox">
            <input
              type="checkbox"
              :checked="layer.visible"
              @change="$emit('toggle-layer', key)"
            />
            <span class="checkmark"></span>
            {{ layer.name }}
          </label>
        </div>
      </div>
    </div>

    <!-- 视角控制 -->
    <div class="control-section">
      <h3>视角控制</h3>
      <div class="view-controls">
        <button @click="$emit('view-change', 'overview')" class="view-btn">
          🌍 全景视角
        </button>
        <button @click="$emit('view-change', 'close')" class="view-btn">
          🔍 近景视角
        </button>
        <button @click="$emit('view-change', 'side')" class="view-btn">
          📐 侧面视角
        </button>
        <button @click="$emit('view-change', 'airport')" class="view-btn">
          ✈️ 机场视角
        </button>
        <button @click="$emit('view-change', 'bridge')" class="view-btn">
          🌉 大桥视角
        </button>
      </div>
    </div>

    <!-- 预警设置 -->
    <div class="control-section">
      <h3>预警设置</h3>
      <div class="warning-settings">
        <div class="setting-item">
          <label>风速阈值 (m/s):</label>
          <input
            type="number"
            v-model="warningThresholds.windSpeed"
            @change="updateWarningThresholds"
            min="0"
            max="50"
          />
        </div>
        <div class="setting-item">
          <label>温度阈值 (°C):</label>
          <input
            type="number"
            v-model="warningThresholds.temperature"
            @change="updateWarningThresholds"
            min="-50"
            max="60"
          />
        </div>
        <div class="setting-item">
          <label>湿度阈值 (%):</label>
          <input
            type="number"
            v-model="warningThresholds.humidity"
            @change="updateWarningThresholds"
            min="0"
            max="100"
          />
        </div>
      </div>
    </div>

    <!-- 无人机控制 -->
    <div class="control-section">
      <h3>无人机控制</h3>
      <div class="drone-controls">
        <button @click="startAllDrones" class="drone-btn start">
          🚁 启动巡航
        </button>
        <button @click="recallAllDrones" class="drone-btn recall">
          🏠 召回无人机
        </button>
        <button @click="emergencyStop" class="drone-btn emergency">
          🛑 紧急停止
        </button>
        <div class="drone-status">
          <span>活跃无人机: {{ activeDroneCount }}</span>
        </div>
      </div>
    </div>

    <!-- 数据刷新 -->
    <div class="control-section">
      <h3>数据控制</h3>
      <div class="data-controls">
        <button @click="refreshData" class="refresh-btn">
          🔄 刷新数据
        </button>
        <button @click="exportData" class="export-btn">
          💾 导出数据
        </button>
        <div class="auto-refresh">
          <label class="layer-checkbox">
            <input
              type="checkbox"
              v-model="autoRefresh"
              @change="toggleAutoRefresh"
            />
            <span class="checkmark"></span>
            自动刷新 (5秒)
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 定义事件
const emit = defineEmits([
  'toggle-layer',
  'time-change',
  'play-pause',
  'view-change',
  'drone-control'
]);

// 接收props
const props = defineProps({
  weatherLayers: Object,
  currentTime: Date,
  isPlaying: Boolean,
  droneStatus: {
    type: Array,
    default: () => []
  }
});

// 响应式数据
const warningThresholds = ref({
  windSpeed: 20,
  temperature: 35,
  humidity: 80
});

const autoRefresh = ref(true);

// 计算属性
const timeSliderValue = computed(() => {
  const hours = props.currentTime.getHours();
  const minutes = props.currentTime.getMinutes();
  return hours * 60 + minutes;
});

const activeDroneCount = computed(() => {
  return props.droneStatus.filter(drone => drone.status === 'flying').length;
});

// 方法
function formatTime(date) {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

function onTimeSliderChange(event) {
  const minutes = parseInt(event.target.value);
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  const newTime = new Date(props.currentTime);
  newTime.setHours(hours, mins, 0, 0);

  emit('time-change', newTime);
}

function resetTime() {
  emit('time-change', new Date());
}

function updateWarningThresholds() {
  // 发送阈值更新事件
  console.log('Warning thresholds updated:', warningThresholds.value);
}

function refreshData() {
  // 手动刷新数据
  console.log('Refreshing weather data...');
  // 这里可以触发数据刷新事件
}

function exportData() {
  // 导出当前数据
  const data = {
    timestamp: new Date().toISOString(),
    weatherLayers: props.weatherLayers,
    thresholds: warningThresholds.value
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: 'application/json'
  });

  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `weather-data-${Date.now()}.json`;
  a.click();

  URL.revokeObjectURL(url);
}

function toggleAutoRefresh() {
  console.log('Auto refresh:', autoRefresh.value);
}

// 无人机控制方法
function startAllDrones() {
  emit('drone-control', 'start-all');
}

function recallAllDrones() {
  emit('drone-control', 'recall-all');
}

function emergencyStop() {
  emit('drone-control', 'emergency-stop');
}
</script>

<style scoped>
.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 320px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.panel-header h2 {
  margin: 0 0 20px 0;
  font-size: 18px;
  text-align: center;
  color: #00ff88;
  border-bottom: 2px solid #00ff88;
  padding-bottom: 10px;
}

.control-section {
  margin-bottom: 25px;
}

.control-section h3 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #88ccff;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 时间控制样式 */
.time-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.current-time {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  text-align: center;
  font-family: monospace;
  font-size: 12px;
}

.time-slider input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #333;
  outline: none;
  -webkit-appearance: none;
}

.time-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00ff88;
  cursor: pointer;
}

.playback-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.play-btn, .reset-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-btn:hover, .reset-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #00ff88;
}

/* 图层控制样式 */
.layer-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  align-items: center;
}

.layer-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
}

.layer-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid #555;
  border-radius: 3px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.layer-checkbox input[type="checkbox"]:checked + .checkmark {
  background: #00ff88;
  border-color: #00ff88;
}

.layer-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: black;
  font-weight: bold;
  font-size: 12px;
}

/* 视角控制样式 */
.view-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.view-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 13px;
}

.view-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #00ff88;
  transform: translateX(5px);
}

/* 预警设置样式 */
.warning-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.setting-item label {
  font-size: 12px;
  color: #ccc;
}

.setting-item input[type="number"] {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
}

.setting-item input[type="number"]:focus {
  outline: none;
  border-color: #00ff88;
  background: rgba(255, 255, 255, 0.15);
}

/* 无人机控制样式 */
.drone-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.drone-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
}

.drone-btn.start:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: #00ff88;
}

.drone-btn.recall:hover {
  background: rgba(255, 255, 0, 0.2);
  border-color: #ffff00;
}

.drone-btn.emergency:hover {
  background: rgba(255, 0, 0, 0.2);
  border-color: #ff0000;
}

.drone-status {
  margin-top: 5px;
  font-size: 12px;
  color: #00ff88;
  text-align: center;
}

/* 数据控制样式 */
.data-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.refresh-btn, .export-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
}

.refresh-btn:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: #00ff88;
}

.export-btn:hover {
  background: rgba(136, 204, 255, 0.2);
  border-color: #88ccff;
}

.auto-refresh {
  margin-top: 5px;
}

/* 滚动条样式 */
.control-panel::-webkit-scrollbar {
  width: 6px;
}

.control-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb {
  background: #00ff88;
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover {
  background: #00cc6a;
}
</style>
