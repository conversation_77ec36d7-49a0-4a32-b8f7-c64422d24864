<template>
  <div class="cesium-weather-system">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市CesiumJS三维气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">CesiumJS版本:</span>
          <span class="status-value" :class="cesiumVersion ? 'online' : 'error'">{{ cesiumVersion || '未加载' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">地形提供商:</span>
          <span class="status-value" :class="terrainProvider.class">{{ terrainProvider.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">影像提供商:</span>
          <span class="status-value" :class="imageryProvider.class">{{ imageryProvider.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">实体数量:</span>
          <span class="status-value online">{{ entityCount }} �?/span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 4)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="flyToWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          �?生成新预�?        </button>
        <button @click="generateSevereWarning" class="control-btn severe" :disabled="!isSystemReady">
          🚨 生成严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑�?清除所有预�?        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }" :disabled="!isSystemReady">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更�? }}
        </button>
      </div>

      <!-- 视图控制 -->
      <div class="view-control">
        <h4>🎥 视图控制</h4>
        <div class="view-buttons">
          <button @click="setView('overview')" class="view-btn">🌍 总览</button>
          <button @click="setView('aerial')" class="view-btn">🚁 航拍</button>
          <button @click="setView('ground')" class="view-btn">👁�?地面</button>
          <button @click="setView('satellite')" class="view-btn">🛰�?卫星</button>
        </div>
      </div>

      <!-- 图层控制 -->
      <div class="layer-control">
        <h4>📊 图层控制</h4>
        <div class="layer-items">
          <label class="layer-item">
            <input type="checkbox" v-model="layers.terrain" @change="toggleTerrain">
            <span>🏔�?地形</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.buildings" @change="toggleBuildings">
            <span>🏢 建筑�?/span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.weather" @change="toggleWeather">
            <span>☁️ 气象预警</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.labels" @change="toggleLabels">
            <span>🏷�?区域标签</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.coastline" @change="toggleCoastline">
            <span>🏖�?海岸�?/span>
          </label>
        </div>
      </div>

      <!-- 地图样式 -->
      <div class="map-style-control">
        <h4>🗺�?地图样式</h4>
        <div class="style-buttons">
          <button @click="setImageryProvider('satellite')" class="style-btn" :class="{ active: currentImagery === 'satellite' }">🛰�?卫星</button>
          <button @click="setImageryProvider('street')" class="style-btn" :class="{ active: currentImagery === 'street' }">🗺�?街道</button>
          <button @click="setImageryProvider('terrain')" class="style-btn" :class="{ active: currentImagery === 'terrain' }">🏔�?地形</button>
        </div>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
            <div class="area-description">{{ area.description }}</div>
            <div class="area-coords">{{ area.longitude.toFixed(3) }}°E, {{ area.latitude.toFixed(3) }}°N</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 相机信息面板 -->
    <div class="camera-info-panel">
      <h4>📹 相机信息</h4>
      <div class="camera-details">
        <div class="camera-item">
          <span class="camera-label">经度:</span>
          <span class="camera-value">{{ cameraPosition.longitude.toFixed(6) }}°</span>
        </div>
        <div class="camera-item">
          <span class="camera-label">纬度:</span>
          <span class="camera-value">{{ cameraPosition.latitude.toFixed(6) }}°</span>
        </div>
        <div class="camera-item">
          <span class="camera-label">高度:</span>
          <span class="camera-value">{{ cameraPosition.height.toFixed(0) }}m</span>
        </div>
        <div class="camera-item">
          <span class="camera-label">方向:</span>
          <span class="camera-value">{{ cameraPosition.heading.toFixed(1) }}°</span>
        </div>
        <div class="camera-item">
          <span class="camera-label">俯仰:</span>
          <span class="camera-value">{{ cameraPosition.pitch.toFixed(1) }}°</span>
        </div>
      </div>
    </div>

    <!-- 性能监控面板 -->
    <div class="performance-panel">
      <h4>�?性能监控</h4>
      <div class="performance-stats">
        <div class="perf-item">
          <span class="perf-label">FPS:</span>
          <span class="perf-value" :class="fps >= 30 ? 'good' : fps >= 15 ? 'medium' : 'poor'">{{ fps }}</span>
        </div>
        <div class="perf-item">
          <span class="perf-label">渲染时间:</span>
          <span class="perf-value">{{ renderTime.toFixed(1) }}ms</span>
        </div>
        <div class="perf-item">
          <span class="perf-label">内存使用:</span>
          <span class="perf-value">{{ memoryUsage.toFixed(1) }}MB</span>
        </div>
        <div class="perf-item">
          <span class="perf-label">网络状�?</span>
          <span class="perf-value" :class="networkStatus.class">{{ networkStatus.text }}</span>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import * as Cesium from 'cesium';

// Cesium相关变量
let viewer = null;
let cesiumLoaded = false;

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const notification = ref(null);
const cesiumVersion = ref('');
const autoUpdate = ref(false);
const entityCount = ref(0);
const currentImagery = ref('satellite');

// 系统状态
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const terrainProvider = ref({ text: '未知', class: 'loading' });
const imageryProvider = ref({ text: '未知', class: 'loading' });
const isSystemReady = computed(() => systemStatus.value.class === 'online');

// 图层控制
const layers = ref({
  terrain: true,
  buildings: true,
  weather: true,
  labels: true,
  coastline: true
});

// 相机信息
const cameraPosition = ref({
  longitude: 113.5767,
  latitude: 22.2707,
  height: 50000,
  heading: 0,
  pitch: -90
});

// 性能监控
const fps = ref(0);
const renderTime = ref(0);
const memoryUsage = ref(0);
const networkStatus = ref({ text: '良好', class: 'good' });

// 系统变量
let warningEntities = [];
let buildingEntities = [];
let labelEntities = [];
let updateInterval = null;
let warningIdCounter = 0;
let lastTime = 0;
let frameCount = 0;

// 珠海市重要区域（真实经纬度坐标）
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2707,
    description: '珠海市中心区域，商务繁华'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.4200,
    latitude: 22.1300,
    description: '珠海经济特区，发展迅速'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3600,
    latitude: 22.1400,
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    longitude: 113.2900,
    latitude: 22.2100,
    description: '珠海农业区，生态优美'
  },
  {
    name: '港珠澳大桥',
    icon: '🌉',
    longitude: 113.5800,
    latitude: 22.2100,
    description: '连接港澳的跨海大桥'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化CesiumJS三维气象预警系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem() {
  try {
    // 步骤1: 设置CesiumJS
    systemStatus.value = { text: '设置CesiumJS...', class: 'loading' };
    setupCesiumJS();

    // 步骤2: 初始化Cesium Viewer
    systemStatus.value = { text: '初始化Cesium Viewer...', class: 'loading' };
    await initializeCesiumViewer();

    // 步骤3: 创建珠海3D模型
    systemStatus.value = { text: '创建珠海3D模型...', class: 'loading' };
    await createZhuhai3DModels();

    // 步骤4: 设置相机位置
    systemStatus.value = { text: '设置初始视图...', class: 'loading' };
    setInitialView();

    // 步骤5: 启动监控循环
    systemStatus.value = { text: '启动监控系统...', class: 'loading' };
    startMonitoringLoop();

    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };

    console.log('✅ CesiumJS三维气象预警系统初始化完成');
    showNotification('✅ CesiumJS 3D系统启动成功', 'success');

    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 2000);

  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };

    // 检查是否是CesiumJS相关错误
    if (error.message.includes('Cesium') || error.message.includes('网络') || error.message.includes('加载')) {
      showNotification('❌ CesiumJS初始化失败，建议切换到Canvas版本', 'error');
      showCesiumFallbackMessage();
    } else {
      showNotification(`❌ 初始化失败: ${error.message}`, 'error');
    }
  }
}

// 显示CesiumJS降级提示
function showCesiumFallbackMessage() {
  // 创建降级提示界面
  const fallbackDiv = document.createElement('div');
  fallbackDiv.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 30px;
      border-radius: 10px;
      text-align: center;
      z-index: 10000;
      max-width: 500px;
    ">
      <h3>🌍 CesiumJS加载失败</h3>
      <p>CesiumJS无法正常加载，可能的原因：</p>
      <ul style="text-align: left; margin: 20px 0;">
        <li>网络连接问题</li>
        <li>CDN服务不可用</li>
        <li>浏览器兼容性问题</li>
        <li>防火墙或代理设置</li>
      </ul>
      <p><strong>建议解决方案：</strong></p>
      <ul style="text-align: left; margin: 20px 0;">
        <li>检查网络连接</li>
        <li>刷新页面重试</li>
        <li>使用VPN或更换网络</li>
        <li>切换到Canvas版本</li>
      </ul>
      <button onclick="switchToCanvas()" style="
        background: #4CAF50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px;
      ">切换到Canvas版本</button>
      <button onclick="retryCesium()" style="
        background: #2196F3;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px;
      ">重试CesiumJS</button>
      <button onclick="this.parentElement.parentElement.remove()" style="
        background: #f44336;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px;
      ">关闭</button>
    </div>
  `;

  document.body.appendChild(fallbackDiv);

  // 添加全局函数
  window.switchToCanvas = () => {
    console.log('切换到Canvas版本');
    fallbackDiv.remove();
    // 触发切换事件
    window.dispatchEvent(new CustomEvent('switchToCanvas'));
  };

  window.retryCesium = () => {
    console.log('重试CesiumJS');
    fallbackDiv.remove();
    // 重新初始�?    setTimeout(() => {
      initializeSystem();
    }, 1000);
  };
}

// 设置CesiumJS
function setupCesiumJS() {
  try {
    // 设置Cesium访问令牌（使用公共令牌）
    if (Cesium && Cesium.Ion) {
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
      console.log('Cesium访问令牌设置成功');
    }

    // 设置全局Cesium对象以保持兼容性
    window.Cesium = Cesium;

    cesiumVersion.value = Cesium.VERSION;
    cesiumLoaded = true;
    console.log('CesiumJS版本:', Cesium.VERSION);
    console.log('�?本地CesiumJS设置成功');
  } catch (error) {
    throw new Error(`CesiumJS设置失败: ${error.message}`);
  }
}

// 初始化Cesium Viewer
async function initializeCesiumViewer() {
  try {
    await nextTick();

    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找�?);
    }

    if (!cesiumLoaded || !Cesium) {
      throw new Error('CesiumJS未正确加�?);
    }

    console.log('开始创建Cesium Viewer...');

    // 使用更简单的配置创建Cesium Viewer
    const viewerOptions = {
      // 界面选项 - 关闭所有UI组件
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false,

      // 性能选项
      requestRenderMode: false, // 关闭请求渲染模式以避免问�?      maximumRenderTimeChange: Infinity,

      // 渲染选项
      shadows: false, // 暂时关闭阴影
      terrainShadows: Cesium.ShadowMode.DISABLED
    };

    // 尝试使用基础地形提供�?    try {
      viewerOptions.terrainProvider = new Cesium.EllipsoidTerrainProvider();
      terrainProvider.value = { text: 'Ellipsoid (Basic)', class: 'online' };
    } catch (terrainError) {
      console.warn('地形提供商设置失�?', terrainError);
    }

    // 尝试使用基础影像提供�?    try {
      viewerOptions.imageryProvider = new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      });
      imageryProvider.value = { text: 'OpenStreetMap', class: 'online' };
    } catch (imageryError) {
      console.warn('影像提供商设置失败，使用默认:', imageryError);
      imageryProvider.value = { text: 'Default', class: 'online' };
    }

    // 创建Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, viewerOptions);

    console.log('Cesium Viewer创建成功');

    // 禁用默认的双击行为
    try {
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      );
    } catch (error) {
      console.warn('禁用双击行为失败:', error);
    }

    // 设置相机移动监听
    try {
      viewer.camera.moveEnd.addEventListener(updateCameraPosition);
    } catch (error) {
      console.warn('相机监听设置失败:', error);
    }

    // 设置渲染监听
    try {
      viewer.scene.postRender.addEventListener(updatePerformanceStats);
    } catch (error) {
      console.warn('渲染监听设置失败:', error);
    }

    console.log('✅ Cesium Viewer初始化完成');
  } catch (error) {
    console.error('Cesium Viewer初始化详细错误:', error);
    throw new Error(`Cesium Viewer初始化失败: ${error.message}`);
  }
}

// 创建珠海3D模型
async function createZhuhai3DModels() {
  try {
    console.log('创建珠海3D模型...');

    if (!viewer) {
      throw new Error('Cesium Viewer未初始化');
    }

    // 创建简化的区域标记
    await createSimpleAreaMarkers();

    // 创建简化的地标
    await createSimpleLandmarks();

    // 更新实体计数
    updateEntityCount();

    console.log('�?珠海3D模型创建完成');
  } catch (error) {
    console.error('3D模型创建失败:', error);
    // 不抛出错误，允许系统继续运行
    console.log('⚠️ 3D模型创建失败，但系统继续运行');
  }
}

// 创建建筑�?async function createBuildings() {
  const buildingAreas = [
    { name: '香洲商务�?, longitude: 113.5767, latitude: 22.2707, count: 20, maxHeight: 150 },
    { name: '拱北商圈', longitude: 113.5500, latitude: 22.2200, count: 15, maxHeight: 120 },
    { name: '横琴新区', longitude: 113.4200, latitude: 22.1300, count: 18, maxHeight: 200 },
    { name: '金湾�?, longitude: 113.3600, latitude: 22.1400, count: 12, maxHeight: 100 },
    { name: '斗门�?, longitude: 113.2900, latitude: 22.2100, count: 10, maxHeight: 80 }
  ];

  buildingAreas.forEach(area => {
    for (let i = 0; i < area.count; i++) {
      // 在区域周围随机分布建�?      const offsetLon = (Math.random() - 0.5) * 0.02; // �?km范围
      const offsetLat = (Math.random() - 0.5) * 0.02;
      const height = 20 + Math.random() * area.maxHeight;

      const building = viewer.entities.add({
        name: `${area.name}-建筑${i + 1}`,
        position: Cesium.Cartesian3.fromDegrees(
          area.longitude + offsetLon,
          area.latitude + offsetLat
        ),
        box: {
          dimensions: new Cesium.Cartesian3(
            30 + Math.random() * 40, // 宽度
            30 + Math.random() * 40, // 深度
            height // 高度
          ),
          material: Cesium.Color.fromRandom({
            red: 0.3,
            green: 0.3,
            blue: 0.3,
            alpha: 0.8
          }),
          outline: true,
          outlineColor: Cesium.Color.WHITE
        }
      });

      buildingEntities.push(building);
    }
  });
}

// 创建地标
async function createLandmarks() {
  const landmarks = [
    {
      name: '珠海大剧�?,
      longitude: 113.5800,
      latitude: 22.2100,
      height: 50,
      color: Cesium.Color.PURPLE,
      description: '珠海标志性建筑，贝壳形状的现代艺术建�?
    },
    {
      name: '珠海渔女',
      longitude: 113.5900,
      latitude: 22.2500,
      height: 30,
      color: Cesium.Color.CYAN,
      description: '珠海市标志性雕塑，象征着珠海的海洋文�?
    },
    {
      name: '圆明新园',
      longitude: 113.4100,
      latitude: 22.1200,
      height: 25,
      color: Cesium.Color.GOLD,
      description: '仿圆明园建筑群，展现中国古典园林艺术'
    },
    {
      name: '长隆海洋王国',
      longitude: 113.4300,
      latitude: 22.1100,
      height: 40,
      color: Cesium.Color.HOTPINK,
      description: '世界最大的海洋主题公园之一'
    },
    {
      name: '珠海机场',
      longitude: 113.3800,
      latitude: 22.1400,
      height: 20,
      color: Cesium.Color.BLUE,
      description: '珠海金湾机场，连接世界的空中门户'
    }
  ];

  landmarks.forEach(landmark => {
    const entity = viewer.entities.add({
      name: landmark.name,
      position: Cesium.Cartesian3.fromDegrees(
        landmark.longitude,
        landmark.latitude,
        landmark.height
      ),
      cylinder: {
        length: landmark.height * 2,
        topRadius: 15,
        bottomRadius: 20,
        material: landmark.color,
        outline: true,
        outlineColor: Cesium.Color.WHITE
      },
      label: {
        text: landmark.name,
        font: '14pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50),
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      },
      description: landmark.description
    });

    buildingEntities.push(entity);
  });
}

// 创建简化的区域标记
async function createSimpleAreaMarkers() {
  try {
    zhuhaiAreas.value.forEach(area => {
      const entity = viewer.entities.add({
        name: area.name,
        position: Cesium.Cartesian3.fromDegrees(
          area.longitude,
          area.latitude,
          100
        ),
        point: {
          pixelSize: 12,
          color: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        },
        label: {
          text: `${area.icon} ${area.name}`,
          font: '12pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -30),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });

      labelEntities.push(entity);
    });

    console.log('�?区域标记创建成功');
  } catch (error) {
    console.error('区域标记创建失败:', error);
  }
}

// 创建简化的地标
async function createSimpleLandmarks() {
  try {
    const landmarks = [
      {
        name: '珠海大剧�?,
        longitude: 113.5800,
        latitude: 22.2100,
        color: Cesium.Color.PURPLE
      },
      {
        name: '珠海渔女',
        longitude: 113.5900,
        latitude: 22.2500,
        color: Cesium.Color.CYAN
      },
      {
        name: '横琴新区',
        longitude: 113.4200,
        latitude: 22.1300,
        color: Cesium.Color.GOLD
      },
      {
        name: '珠海机场',
        longitude: 113.3800,
        latitude: 22.1400,
        color: Cesium.Color.BLUE
      }
    ];

    landmarks.forEach(landmark => {
      const entity = viewer.entities.add({
        name: landmark.name,
        position: Cesium.Cartesian3.fromDegrees(
          landmark.longitude,
          landmark.latitude,
          50
        ),
        point: {
          pixelSize: 15,
          color: landmark.color,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        },
        label: {
          text: landmark.name,
          font: '10pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 1,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -25),
          disableDepthTestDistance: Number.POSITIVE_INFINITY
        }
      });

      buildingEntities.push(entity);
    });

    console.log('�?地标创建成功');
  } catch (error) {
    console.error('地标创建失败:', error);
  }
}

// 创建区域标记
async function createAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    const entity = viewer.entities.add({
      name: area.name,
      position: Cesium.Cartesian3.fromDegrees(
        area.longitude,
        area.latitude,
        100
      ),
      point: {
        pixelSize: 15,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      },
      label: {
        text: `${area.icon} ${area.name}`,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      },
      description: `
        <div style="font-family: Microsoft YaHei;">
          <h3>${area.icon} ${area.name}</h3>
          <p><strong>描述:</strong> ${area.description}</p>
          <p><strong>坐标:</strong> ${area.longitude.toFixed(4)}°E, ${area.latitude.toFixed(4)}°N</p>
        </div>
      `
    });

    labelEntities.push(entity);
  });
}

// 创建海岸�?async function createCoastline() {
  // 珠海海岸线坐标（简化版本）
  const coastlinePositions = [
    113.5200, 22.1800,
    113.5400, 22.2000,
    113.5600, 22.2200,
    113.5800, 22.2400,
    113.6000, 22.2600,
    113.5900, 22.2800,
    113.5700, 22.3000,
    113.5500, 22.2800,
    113.5300, 22.2600,
    113.5100, 22.2400,
    113.4900, 22.2200,
    113.4700, 22.2000,
    113.4500, 22.1800,
    113.4300, 22.1600,
    113.4100, 22.1400,
    113.3900, 22.1600,
    113.3700, 22.1800,
    113.3500, 22.2000
  ];

  const coastline = viewer.entities.add({
    name: '珠海海岸�?,
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArray(coastlinePositions),
      width: 3,
      material: Cesium.Color.LIGHTBLUE,
      clampToGround: true
    }
  });

  labelEntities.push(coastline);
}

// 设置初始视图
function setInitialView() {
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 50000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    }
  });

  updateCameraPosition();
}

// 更新相机位置信息
function updateCameraPosition() {
  if (!viewer) return;

  const position = viewer.camera.positionCartographic;
  cameraPosition.value = {
    longitude: Cesium.Math.toDegrees(position.longitude),
    latitude: Cesium.Math.toDegrees(position.latitude),
    height: position.height,
    heading: Cesium.Math.toDegrees(viewer.camera.heading),
    pitch: Cesium.Math.toDegrees(viewer.camera.pitch)
  };
}

// 更新实体计数
function updateEntityCount() {
  if (viewer) {
    entityCount.value = viewer.entities.values.length;
  }
}

// 启动监控循环
function startMonitoringLoop() {
  // 性能监控
  setInterval(() => {
    updatePerformanceStats();
  }, 1000);
}

// 更新性能统计
function updatePerformanceStats() {
  frameCount++;
  const currentTime = performance.now();

  if (currentTime - lastTime >= 1000) {
    fps.value = Math.round((frameCount * 1000) / (currentTime - lastTime));
    frameCount = 0;
    lastTime = currentTime;

    // 估算内存使用
    if (performance.memory) {
      memoryUsage.value = performance.memory.usedJSHeapSize / 1024 / 1024;
    }

    // 检查网络状�?    if (navigator.onLine) {
      networkStatus.value = { text: '在线', class: 'good' };
    } else {
      networkStatus.value = { text: '离线', class: 'poor' };
    }
  }
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 3;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 1000);
  }
}

// 生成新预�?function generateNewWarning() {
  if (!isSystemReady.value) return;

  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷�?];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  // 在选定区域附近随机偏移
  const offsetLon = (Math.random() - 0.5) * 0.01;
  const offsetLat = (Math.random() - 0.5) * 0.01;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLon,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWarningEntity(warning);
  updateEntityCount();

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWarningEntity(oldWarning.id);
  }
}

// 生成严重预警
function generateSevereWarning() {
  if (!isSystemReady.value) return;

  const warningTypes = ['龙卷�?, '冰雹', '暴雨', '大风'];
  const warningLevels = ['red', 'orange'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetLon = (Math.random() - 0.5) * 0.01;
  const offsetLat = (Math.random() - 0.5) * 0.01;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLon,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 2) + 2,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWarningEntity(warning);
  updateEntityCount();

  console.log(`🚨 新增严重${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 严重${warning.type}预警 - ${warning.location}`, 'error');

  setTimeout(() => {
    flyToWarning(warning);
  }, 500);

  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWarningEntity(oldWarning.id);
  }
}

// 创建预警实体
function createWarningEntity(warning) {
  if (!viewer) return;

  const color = getWarningColor(warning.level);
  const height = warning.intensity * 500 + 1000;
  const radius = warning.intensity * 1000 + 2000;

  // 创建预警圆柱�?  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    name: `${warning.type}预警`,
    position: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      height / 2
    ),
    cylinder: {
      length: height,
      topRadius: radius * 0.8,
      bottomRadius: radius,
      material: color.withAlpha(0.6),
      outline: true,
      outlineColor: color
    },
    label: {
      text: `${getWarningIcon(warning.type)} ${warning.type}预警`,
      font: '14pt Microsoft YaHei',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -50),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    },
    description: `
      <div style="font-family: Microsoft YaHei;">
        <h3>${getWarningIcon(warning.type)} ${warning.type}预警</h3>
        <p><strong>等级:</strong> ${getWarningLevelText(warning.level)}</p>
        <p><strong>位置:</strong> ${warning.location}</p>
        <p><strong>强度:</strong> ${warning.intensity}/3</p>
        <p><strong>时间:</strong> ${warning.time}</p>
        <p><strong>坐标:</strong> ${warning.longitude.toFixed(4)}°E, ${warning.latitude.toFixed(4)}°N</p>
      </div>
    `
  });

  warningEntities.push(entity);
}

// 移除预警实体
function removeWarningEntity(warningId) {
  if (!viewer) return;

  const entityId = `warning-${warningId}`;
  const entity = viewer.entities.getById(entityId);
  if (entity) {
    viewer.entities.remove(entity);
  }

  warningEntities = warningEntities.filter(e => e.id !== entityId);
  updateEntityCount();
}

// 清除所有预�?function clearAllWarnings() {
  activeWarnings.value = [];

  warningEntities.forEach(entity => {
    if (viewer) {
      viewer.entities.remove(entity);
    }
  });
  warningEntities = [];
  updateEntityCount();

  showNotification('🗑�?已清除所有预�?, 'info');
}

// 飞行到预警位�?function flyToWarning(warning) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      10000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行�?${warning.location} ${warning.type}预警`, 'info');
}

// 飞行到区�?function flyToArea(area) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      15000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-60),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行�?${area.name}`, 'info');
}

// 设置视图
function setView(viewType) {
  if (!viewer) return;

  let destination, orientation;

  switch (viewType) {
    case 'overview':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 50000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      };
      break;
    case 'aerial':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 20000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0
      };
      break;
    case 'ground':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 1000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-15),
        roll: 0.0
      };
      break;
    case 'satellite':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0
      };
      break;
    default:
      return;
  }

  viewer.camera.flyTo({
    destination: destination,
    orientation: orientation,
    duration: 2.0
  });

  const viewNames = {
    overview: '总览视图',
    aerial: '航拍视图',
    ground: '地面视图',
    satellite: '卫星视图'
  };

  showNotification(`📹 切换�?{viewNames[viewType]}`, 'info');
}

// 设置影像提供�?function setImageryProvider(providerType) {
  if (!viewer) return;

  let imageryProvider;

  switch (providerType) {
    case 'satellite':
      imageryProvider = new Cesium.IonImageryProvider({ assetId: 3 });
      currentImagery.value = 'satellite';
      imageryProvider.value = { text: 'Bing Maps Satellite', class: 'online' };
      break;
    case 'street':
      imageryProvider = new Cesium.IonImageryProvider({ assetId: 4 });
      currentImagery.value = 'street';
      imageryProvider.value = { text: 'Bing Maps Road', class: 'online' };
      break;
    case 'terrain':
      imageryProvider = new Cesium.ArcGisMapServerImageryProvider({
        url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
      });
      currentImagery.value = 'terrain';
      imageryProvider.value = { text: 'ArcGIS World Imagery', class: 'online' };
      break;
    default:
      return;
  }

  viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(imageryProvider);

  const providerNames = {
    satellite: '卫星影像',
    street: '街道地图',
    terrain: '地形影像'
  };

  showNotification(`🗺�?切换�?{providerNames[providerType]}`, 'info');
}

// 图层控制函数
function toggleTerrain() {
  if (!viewer) return;

  if (layers.terrain) {
    viewer.terrainProvider = Cesium.createWorldTerrain({
      requestWaterMask: true,
      requestVertexNormals: true
    });
    terrainProvider.value = { text: 'Cesium World Terrain', class: 'online' };
  } else {
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
    terrainProvider.value = { text: 'Ellipsoid (Flat)', class: 'online' };
  }

  showNotification(`🏔�?地形${layers.terrain ? '显示' : '隐藏'}`, 'info');
}

function toggleBuildings() {
  buildingEntities.forEach(entity => {
    entity.show = layers.buildings;
  });
  showNotification(`🏢 建筑�?{layers.buildings ? '显示' : '隐藏'}`, 'info');
}

function toggleWeather() {
  warningEntities.forEach(entity => {
    entity.show = layers.weather;
  });
  showNotification(`☁️ 气象预警${layers.weather ? '显示' : '隐藏'}`, 'info');
}

function toggleLabels() {
  labelEntities.forEach(entity => {
    entity.show = layers.labels;
  });
  showNotification(`🏷�?区域标签${layers.labels ? '显示' : '隐藏'}`, 'info');
}

function toggleCoastline() {
  // 海岸线在labelEntities中，这里可以单独控制
  const coastlineEntity = viewer.entities.values.find(e => e.name === '珠海海岸�?);
  if (coastlineEntity) {
    coastlineEntity.show = layers.coastline;
  }
  showNotification(`🏖�?海岸�?{layers.coastline ? '显示' : '隐藏'}`, 'info');
}

// 自动更新控制
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (Math.random() < 0.3) {
        generateNewWarning();
      }
    }, 8000);
    showNotification('▶️ 自动更新已开�?, 'info');
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
    showNotification('⏸️ 自动更新已暂�?, 'info');
  }
}

// 工具函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧�?,
    '雷电': '�?,
    '大雾': '🌫�?,
    '冰雹': '🧊',
    '龙卷�?: '🌪�?
  };
  return icons[type] || '⚠️';
}

function getWarningColor(level) {
  const colors = {
    'red': Cesium.Color.RED,
    'orange': Cesium.Color.ORANGE,
    'yellow': Cesium.Color.YELLOW,
    'blue': Cesium.Color.BLUE
  };
  return colors[level] || Cesium.Color.GRAY;
}

function getWarningLevelText(level) {
  const texts = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(w => w.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理CesiumJS系统资源...');

  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }

  if (viewer) {
    viewer.destroy();
    viewer = null;
  }

  // 清空实体数组
  warningEntities = [];
  buildingEntities = [];
  labelEntities = [];

  console.log('✅ CesiumJS系统资源清理完成');
}
</script>

<style scoped>
.cesium-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: #000;
  overflow: hidden;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.error {
  color: #FF6B6B;
}

/* 预警相关样式 */
.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red {
  border-left: 4px solid #ff0000;
  background: rgba(255, 0, 0, 0.1);
}
.level-orange {
  border-left: 4px solid #ff8800;
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow {
  border-left: 4px solid #ffff00;
  background: rgba(255, 255, 0, 0.1);
}
.level-blue {
  border-left: 4px solid #0088ff;
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 20px;
  min-width: 25px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 9px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 8px;
  color: #666;
}

/* 统计样式 */
.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

/* 控制按钮样式 */
.warning-control, .view-control, .layer-control, .map-style-control {
  margin-bottom: 20px;
}

.control-btn, .view-btn, .style-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled, .view-btn:disabled, .style-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.severe {
  background: #FF6B35;
  color: white;
}

.control-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.active, .view-btn.active, .style-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active), .view-btn:not(.active), .style-btn:not(.active) {
  background: #666;
  color: white;
}

.view-buttons, .style-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.view-btn, .style-btn {
  padding: 6px 4px;
  font-size: 9px;
}

/* 图层控制样式 */
.layer-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.layer-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.layer-item input[type="checkbox"] {
  margin: 0;
}

/* 右侧面板样式 */
.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  max-width: 280px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 350px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.area-description {
  font-size: 9px;
  color: #999;
  margin-bottom: 2px;
}

.area-coords {
  font-size: 8px;
  color: #666;
}

/* 底部面板样式 */
.camera-info-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.camera-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.camera-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.camera-label {
  color: #ccc;
}

.camera-value {
  font-weight: bold;
  color: #90EE90;
}

.performance-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 180px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.performance-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.performance-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.perf-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.perf-label {
  color: #ccc;
}

.perf-value {
  font-weight: bold;
}

.perf-value.good {
  color: #90EE90;
}

.perf-value.medium {
  color: #FFA500;
}

.perf-value.poor {
  color: #FF6B6B;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样�?*/
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
