/**
 * 天气数据生成工具
 * 用于生成模拟的气象数据和预警信息
 */

/**
 * 生成完整的天气数据
 * @param {object} options - 生成选项
 * @returns {object} 包含各种天气数据的对象
 */
export function generateWeatherData(options = {}) {
  const {
    gridSize = 20,           // 网格大小
    areaSize = 2000,         // 区域大小（米）
    timeOffset = 0           // 时间偏移（用于动画）
  } = options;

  const windData = generateWindField(gridSize, areaSize, timeOffset);
  const temperatureData = generateTemperatureField(gridSize, areaSize, timeOffset);
  const humidityData = generateHumidityField(gridSize, areaSize, timeOffset);
  const precipitationData = generatePrecipitationField(gridSize, areaSize, timeOffset);
  const warnings = generateWeatherWarnings(windData, temperatureData, humidityData);

  return {
    wind: windData,
    temperature: temperatureData,
    humidity: humidityData,
    precipitation: precipitationData,
    warnings: warnings,
    timestamp: new Date()
  };
}

/**
 * 生成风场数据
 * @param {number} gridSize - 网格大小
 * @param {number} areaSize - 区域大小
 * @param {number} timeOffset - 时间偏移
 * @returns {Array} 风场数据数组
 */
function generateWindField(gridSize, areaSize, timeOffset) {
  const windData = [];
  const step = areaSize / gridSize;
  const time = Date.now() / 1000 + timeOffset;

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const x = (i - gridSize / 2) * step;
      const z = (j - gridSize / 2) * step;
      const y = Math.random() * 200 + 100; // 高度在100-300米之间

      // 基础风向（主要是西风）
      let windDirection = Math.PI * 0.25; // 东北方向
      
      // 添加地形影响和湍流
      const terrainInfluence = Math.sin(x * 0.01) * Math.cos(z * 0.01) * 0.5;
      const turbulence = (Math.sin(time * 0.1 + x * 0.05) + Math.cos(time * 0.15 + z * 0.03)) * 0.3;
      
      windDirection += terrainInfluence + turbulence;

      // 风速计算（基础风速 + 变化）
      let windSpeed = 8 + Math.sin(time * 0.05 + x * 0.02 + z * 0.02) * 5;
      
      // 添加阵风效果
      const gustFactor = Math.sin(time * 0.3 + x * 0.1 + z * 0.1);
      if (gustFactor > 0.7) {
        windSpeed += Math.random() * 15; // 突然的阵风
      }

      // 确保风速为正值
      windSpeed = Math.max(0, windSpeed);

      windData.push({
        x: x,
        y: y,
        z: z,
        speed: windSpeed,
        direction: windDirection,
        gustSpeed: windSpeed + Math.random() * 5,
        turbulence: Math.abs(turbulence)
      });
    }
  }

  return windData;
}

/**
 * 生成温度场数据
 * @param {number} gridSize - 网格大小
 * @param {number} areaSize - 区域大小
 * @param {number} timeOffset - 时间偏移
 * @returns {Array} 温度数据数组
 */
function generateTemperatureField(gridSize, areaSize, timeOffset) {
  const temperatureData = [];
  const step = areaSize / gridSize;
  const time = Date.now() / 1000 + timeOffset;
  const baseTemperature = 20; // 基础温度20°C

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const x = (i - gridSize / 2) * step;
      const z = (j - gridSize / 2) * step;
      const y = Math.random() * 200 + 100;

      // 日温变化
      const hourOfDay = (time / 3600) % 24;
      const dailyVariation = Math.sin((hourOfDay - 6) * Math.PI / 12) * 8;

      // 地理位置影响（模拟纬度效应）
      const latitudeEffect = -z * 0.001; // 北方更冷

      // 高度影响（海拔每升高100米，温度下降0.6°C）
      const altitudeEffect = -y * 0.006;

      // 局部天气系统
      const weatherSystem = Math.sin(time * 0.02 + x * 0.003 + z * 0.003) * 5;

      // 热岛效应（模拟城市区域）
      const urbanHeatIsland = Math.exp(-((x * x + z * z) / (500 * 500))) * 3;

      const temperature = baseTemperature + 
                         dailyVariation + 
                         latitudeEffect + 
                         altitudeEffect + 
                         weatherSystem + 
                         urbanHeatIsland +
                         (Math.random() - 0.5) * 2; // 随机变化

      temperatureData.push({
        x: x,
        y: y,
        z: z,
        temperature: temperature,
        heatIndex: calculateHeatIndex(temperature, 60), // 假设湿度60%
        dewPoint: temperature - 5 + Math.random() * 3
      });
    }
  }

  return temperatureData;
}

/**
 * 生成湿度场数据
 * @param {number} gridSize - 网格大小
 * @param {number} areaSize - 区域大小
 * @param {number} timeOffset - 时间偏移
 * @returns {Array} 湿度数据数组
 */
function generateHumidityField(gridSize, areaSize, timeOffset) {
  const humidityData = [];
  const step = areaSize / gridSize;
  const time = Date.now() / 1000 + timeOffset;

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const x = (i - gridSize / 2) * step;
      const z = (j - gridSize / 2) * step;
      const y = Math.random() * 200 + 100;

      // 基础湿度
      let humidity = 60;

      // 水体影响（模拟河流、湖泊）
      const waterBodyDistance = Math.min(
        Math.abs(z - areaSize * 0.3), // 模拟河流
        Math.sqrt((x - 200) * (x - 200) + (z - 300) * (z - 300)) // 模拟湖泊
      );
      const waterInfluence = Math.max(0, 20 - waterBodyDistance * 0.1);

      // 时间变化（夜间湿度较高）
      const hourOfDay = (time / 3600) % 24;
      const dailyHumidityVariation = Math.cos((hourOfDay - 12) * Math.PI / 12) * 15;

      // 天气系统影响
      const weatherInfluence = Math.sin(time * 0.03 + x * 0.002 + z * 0.002) * 20;

      // 高度影响（高海拔湿度较低）
      const altitudeEffect = -y * 0.05;

      humidity += waterInfluence + dailyHumidityVariation + weatherInfluence + altitudeEffect;
      humidity = Math.max(10, Math.min(100, humidity)); // 限制在10-100%之间

      humidityData.push({
        x: x,
        y: y,
        z: z,
        humidity: humidity,
        absoluteHumidity: calculateAbsoluteHumidity(20, humidity), // 假设温度20°C
        cloudCover: Math.max(0, (humidity - 70) * 2) // 湿度高时云量增加
      });
    }
  }

  return humidityData;
}

/**
 * 生成降水场数据
 * @param {number} gridSize - 网格大小
 * @param {number} areaSize - 区域大小
 * @param {number} timeOffset - 时间偏移
 * @returns {Array} 降水数据数组
 */
function generatePrecipitationField(gridSize, areaSize, timeOffset) {
  const precipitationData = [];
  const step = areaSize / gridSize;
  const time = Date.now() / 1000 + timeOffset;

  // 降水系统中心
  const stormCenterX = Math.sin(time * 0.01) * 500;
  const stormCenterZ = Math.cos(time * 0.01) * 500;

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const x = (i - gridSize / 2) * step;
      const z = (j - gridSize / 2) * step;
      const y = Math.random() * 200 + 100;

      // 距离降水中心的距离
      const distanceToStorm = Math.sqrt(
        (x - stormCenterX) * (x - stormCenterX) + 
        (z - stormCenterZ) * (z - stormCenterZ)
      );

      // 降水强度（距离风暴中心越近越强）
      let intensity = Math.max(0, 50 - distanceToStorm * 0.05);
      
      // 添加随机变化
      intensity *= (0.5 + Math.random() * 0.5);

      // 降水类型判断
      let precipitationType = 'none';
      if (intensity > 0.1) {
        if (intensity < 2) precipitationType = 'drizzle';
        else if (intensity < 10) precipitationType = 'rain';
        else if (intensity < 25) precipitationType = 'heavy_rain';
        else precipitationType = 'storm';
      }

      precipitationData.push({
        x: x,
        y: y,
        z: z,
        intensity: intensity, // mm/h
        type: precipitationType,
        dropletSize: Math.min(5, intensity * 0.2), // 雨滴大小
        visibility: Math.max(100, 10000 - intensity * 200) // 能见度
      });
    }
  }

  return precipitationData;
}

/**
 * 生成天气预警
 * @param {Array} windData - 风场数据
 * @param {Array} temperatureData - 温度数据
 * @param {Array} humidityData - 湿度数据
 * @returns {Array} 预警数组
 */
function generateWeatherWarnings(windData, temperatureData, humidityData) {
  const warnings = [];

  // 检查风速预警
  windData.forEach(point => {
    if (point.speed > 25) { // 强风预警
      warnings.push({
        id: `wind_${point.x}_${point.z}`,
        type: '强风预警',
        level: point.speed > 35 ? 'red' : point.speed > 30 ? 'orange' : 'yellow',
        x: point.x,
        y: point.y,
        z: point.z,
        radius: 200,
        height: 300,
        description: `风速达到 ${point.speed.toFixed(1)} m/s，请注意飞行安全`,
        time: new Date(),
        severity: point.speed > 35 ? 'extreme' : point.speed > 30 ? 'severe' : 'moderate'
      });
    }
  });

  // 检查温度预警
  temperatureData.forEach(point => {
    if (point.temperature > 35 || point.temperature < -10) {
      warnings.push({
        id: `temp_${point.x}_${point.z}`,
        type: point.temperature > 35 ? '高温预警' : '低温预警',
        level: Math.abs(point.temperature - 20) > 20 ? 'red' : 'orange',
        x: point.x,
        y: point.y,
        z: point.z,
        radius: 150,
        height: 250,
        description: `温度 ${point.temperature.toFixed(1)}°C，极端温度条件`,
        time: new Date(),
        severity: Math.abs(point.temperature - 20) > 20 ? 'extreme' : 'severe'
      });
    }
  });

  // 检查湿度相关预警（雾、结冰等）
  humidityData.forEach(point => {
    if (point.humidity > 95) {
      warnings.push({
        id: `fog_${point.x}_${point.z}`,
        type: '大雾预警',
        level: 'yellow',
        x: point.x,
        y: point.y,
        z: point.z,
        radius: 300,
        height: 100,
        description: `湿度 ${point.humidity.toFixed(1)}%，能见度严重受限`,
        time: new Date(),
        severity: 'moderate'
      });
    }
  });

  // 综合预警（多种危险因素结合）
  const dangerousAreas = findDangerousAreas(windData, temperatureData, humidityData);
  dangerousAreas.forEach(area => {
    warnings.push({
      id: `composite_${area.x}_${area.z}`,
      type: '综合气象预警',
      level: 'red',
      x: area.x,
      y: area.y,
      z: area.z,
      radius: 400,
      height: 500,
      description: '多种恶劣天气条件同时存在，极度危险',
      time: new Date(),
      severity: 'extreme',
      factors: area.factors
    });
  });

  return warnings;
}

/**
 * 寻找危险区域（多种恶劣条件同时存在）
 * @param {Array} windData - 风场数据
 * @param {Array} temperatureData - 温度数据
 * @param {Array} humidityData - 湿度数据
 * @returns {Array} 危险区域数组
 */
function findDangerousAreas(windData, temperatureData, humidityData) {
  const dangerousAreas = [];
  const threshold = 100; // 距离阈值

  windData.forEach(windPoint => {
    if (windPoint.speed > 20) {
      const factors = ['强风'];
      
      // 查找附近的温度异常
      const nearbyTempPoint = temperatureData.find(tempPoint => 
        Math.sqrt(
          (windPoint.x - tempPoint.x) ** 2 + 
          (windPoint.z - tempPoint.z) ** 2
        ) < threshold && 
        (tempPoint.temperature > 35 || tempPoint.temperature < 0)
      );
      
      if (nearbyTempPoint) {
        factors.push(nearbyTempPoint.temperature > 35 ? '高温' : '低温');
      }

      // 查找附近的湿度异常
      const nearbyHumidityPoint = humidityData.find(humidPoint => 
        Math.sqrt(
          (windPoint.x - humidPoint.x) ** 2 + 
          (windPoint.z - humidPoint.z) ** 2
        ) < threshold && 
        humidPoint.humidity > 90
      );
      
      if (nearbyHumidityPoint) {
        factors.push('高湿度');
      }

      // 如果有多个危险因素，标记为危险区域
      if (factors.length >= 2) {
        dangerousAreas.push({
          x: windPoint.x,
          y: windPoint.y,
          z: windPoint.z,
          factors: factors
        });
      }
    }
  });

  return dangerousAreas;
}

/**
 * 计算热指数
 * @param {number} temperature - 温度（°C）
 * @param {number} humidity - 相对湿度（%）
 * @returns {number} 热指数
 */
function calculateHeatIndex(temperature, humidity) {
  // 简化的热指数计算
  if (temperature < 27) return temperature;
  
  const T = temperature;
  const H = humidity;
  
  return T + 0.5 * (T + 61.0 + ((T - 68.0) * 1.2) + (H * 0.094));
}

/**
 * 计算绝对湿度
 * @param {number} temperature - 温度（°C）
 * @param {number} relativeHumidity - 相对湿度（%）
 * @returns {number} 绝对湿度（g/m³）
 */
function calculateAbsoluteHumidity(temperature, relativeHumidity) {
  // 简化的绝对湿度计算
  const saturationVaporPressure = 6.112 * Math.exp((17.67 * temperature) / (temperature + 243.5));
  const actualVaporPressure = saturationVaporPressure * (relativeHumidity / 100);
  return (actualVaporPressure * 2.1674) / (temperature + 273.15);
}

/**
 * 获取指定位置的天气数据
 * @param {number} x - X坐标
 * @param {number} z - Z坐标
 * @param {object} weatherData - 完整天气数据
 * @returns {object} 该位置的天气信息
 */
export function getWeatherAtPosition(x, z, weatherData) {
  const findNearestPoint = (dataArray) => {
    let nearest = null;
    let minDistance = Infinity;
    
    dataArray.forEach(point => {
      const distance = Math.sqrt((point.x - x) ** 2 + (point.z - z) ** 2);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = point;
      }
    });
    
    return nearest;
  };

  const windPoint = findNearestPoint(weatherData.wind);
  const tempPoint = findNearestPoint(weatherData.temperature);
  const humidityPoint = findNearestPoint(weatherData.humidity);
  const precipitationPoint = findNearestPoint(weatherData.precipitation);

  return {
    wind: windPoint,
    temperature: tempPoint,
    humidity: humidityPoint,
    precipitation: precipitationPoint,
    warnings: weatherData.warnings.filter(warning => 
      Math.sqrt((warning.x - x) ** 2 + (warning.z - z) ** 2) < warning.radius
    )
  };
}
