<template>
  <div class="basic-zhuhai-city">
    <!-- Cesium 3D地球容器 -->
    <div ref="cesiumContainer" class="cesium-viewer"></div>
    
    <!-- 智慧城市控制面板 -->
    <div class="smart-panel">
      <div class="panel-header">
        <h2>🏙️ 智慧珠海</h2>
        <div class="city-status">
          <span class="status-dot" :class="systemStatus.class"></span>
          <span class="status-text">{{ systemStatus.text }}</span>
        </div>
      </div>

      <!-- 城市概览 -->
      <div class="city-overview">
        <div class="overview-item">
          <div class="item-icon">🌡️</div>
          <div class="item-content">
            <div class="item-label">温度</div>
            <div class="item-value">24°C</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">💨</div>
          <div class="item-content">
            <div class="item-label">风速</div>
            <div class="item-value">12km/h</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">💧</div>
          <div class="item-content">
            <div class="item-label">湿度</div>
            <div class="item-value">68%</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">👥</div>
          <div class="item-content">
            <div class="item-label">人口</div>
            <div class="item-value">244.9万</div>
          </div>
        </div>
      </div>

      <!-- 智慧功能控制 -->
      <div class="smart-controls">
        <h3>🎛️ 智慧功能</h3>
        <div class="control-grid">
          <button @click="toggleBuildings" class="smart-btn" :class="{ active: showBuildings }">
            🏢 建筑物
          </button>
          <button @click="toggleTraffic" class="smart-btn" :class="{ active: showTraffic }">
            🚗 交通流
          </button>
          <button @click="toggleWeather" class="smart-btn" :class="{ active: showWeather }">
            🌦️ 天气层
          </button>
          <button @click="togglePOI" class="smart-btn" :class="{ active: showPOI }">
            📍 兴趣点
          </button>
          <button @click="toggleSensors" class="smart-btn" :class="{ active: showSensors }">
            📡 传感器
          </button>
          <button @click="toggleDrones" class="smart-btn" :class="{ active: showDrones }">
            🚁 无人机
          </button>
        </div>
      </div>

      <!-- 区域选择 -->
      <div class="area-selection">
        <h3>🗺️ 区域导航</h3>
        <div class="area-grid">
          <button @click="flyToArea('xiangzhou')" class="area-btn">🏢 香洲区</button>
          <button @click="flyToArea('jinwan')" class="area-btn">✈️ 金湾区</button>
          <button @click="flyToArea('doumen')" class="area-btn">🌾 斗门区</button>
          <button @click="flyToArea('hengqin')" class="area-btn">🏗️ 横琴新区</button>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="realtime-data">
        <h3>📊 实时数据</h3>
        <div class="data-list">
          <div class="data-item">
            <span class="data-label">空气质量:</span>
            <span class="data-value good">优</span>
          </div>
          <div class="data-item">
            <span class="data-label">交通状况:</span>
            <span class="data-value normal">畅通</span>
          </div>
          <div class="data-item">
            <span class="data-label">能耗状态:</span>
            <span class="data-value good">正常</span>
          </div>
          <div class="data-item">
            <span class="data-label">安全等级:</span>
            <span class="data-value good">安全</span>
          </div>
        </div>
      </div>

      <!-- 时间显示 -->
      <div class="time-display">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification">
      <div class="notification-content">
        <div class="notification-title">{{ notification.title }}</div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as Cesium from 'cesium';

// 响应式数据
const cesiumContainer = ref(null);
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const notification = ref(null);
const currentTime = ref('');
const currentDate = ref('');

// 功能开关
const showBuildings = ref(false);
const showTraffic = ref(false);
const showWeather = ref(false);
const showPOI = ref(true);
const showSensors = ref(false);
const showDrones = ref(false);

// 系统变量
let viewer = null;
let timeInterval = null;

// 珠海区域数据
const zhuhaiAreas = {
  xiangzhou: { name: '香洲区', lng: 113.5767, lat: 22.2707, height: 50000 },
  jinwan: { name: '金湾区', lng: 113.3600, lat: 22.1400, height: 30000 },
  doumen: { name: '斗门区', lng: 113.2900, lat: 22.2100, height: 40000 },
  hengqin: { name: '横琴新区', lng: 113.4200, lat: 22.1300, height: 25000 }
};

// 组件挂载
onMounted(async () => {
  try {
    await initializeBasicCity();
    startTimeUpdate();
  } catch (error) {
    console.error('基础城市系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
  }
});

// 组件卸载
onUnmounted(() => {
  cleanup();
});

// 初始化基础城市系统
async function initializeBasicCity() {
  await nextTick();
  
  systemStatus.value = { text: '创建3D地球...', class: 'loading' };
  
  try {
    // 设置Cesium Ion访问令牌为空，使用默认设置
    Cesium.Ion.defaultAccessToken = '';
    
    // 创建最基本的Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false
    });

    // 设置初始视图到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000),
      orientation: {
        heading: 0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0
      }
    });

    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };
    
    showNotification('智慧珠海', '系统初始化完成！');
    
    // 添加珠海标记
    addZhuhaiMarker();
    
  } catch (error) {
    console.error('基础系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    throw error;
  }
}

// 添加珠海标记
function addZhuhaiMarker() {
  viewer.entities.add({
    name: '珠海市',
    position: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 0),
    point: {
      pixelSize: 20,
      color: Cesium.Color.YELLOW,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    label: {
      text: '珠海市',
      font: '16pt Microsoft YaHei',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -50)
    }
  });
}

// 飞行到指定区域
function flyToArea(areaKey) {
  const area = zhuhaiAreas[areaKey];
  if (!area || !viewer) return;
  
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(area.lng, area.lat, area.height),
    duration: 3
  });
  
  showNotification(area.name, `正在飞行到${area.name}`);
}

// 切换功能
function toggleBuildings() {
  showBuildings.value = !showBuildings.value;
  showNotification('建筑物', showBuildings.value ? '已显示' : '已隐藏');
}

function toggleTraffic() {
  showTraffic.value = !showTraffic.value;
  showNotification('交通流', showTraffic.value ? '已显示' : '已隐藏');
}

function toggleWeather() {
  showWeather.value = !showWeather.value;
  showNotification('天气层', showWeather.value ? '已显示' : '已隐藏');
}

function togglePOI() {
  showPOI.value = !showPOI.value;
  showNotification('兴趣点', showPOI.value ? '已显示' : '已隐藏');
}

function toggleSensors() {
  showSensors.value = !showSensors.value;
  showNotification('传感器', showSensors.value ? '已显示' : '已隐藏');
}

function toggleDrones() {
  showDrones.value = !showDrones.value;
  showNotification('无人机', showDrones.value ? '已显示' : '已隐藏');
}

// 启动时间更新
function startTimeUpdate() {
  timeInterval = setInterval(() => {
    const now = new Date();
    currentTime.value = now.toLocaleTimeString('zh-CN');
    currentDate.value = now.toLocaleDateString('zh-CN');
  }, 1000);
}

// 显示通知
function showNotification(title, message) {
  notification.value = { title, message };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理资源
function cleanup() {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
}
</script>

<style scoped>
.basic-zhuhai-city {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

.cesium-viewer {
  width: 100%;
  height: 100%;
}

/* 智慧城市控制面板 */
.smart-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 90vh;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 25px;
  color: white;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.panel-header {
  text-align: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(74, 144, 226, 0.3);
}

.panel-header h2 {
  margin: 0 0 10px 0;
  font-size: 22px;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.loading {
  background: #ff9800;
}

.status-dot.online {
  background: #4caf50;
}

.status-dot.error {
  background: #f44336;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* 城市概览 */
.city-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(123, 104, 238, 0.1) 100%);
  border: 1px solid rgba(74, 144, 226, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.overview-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.2);
}

.item-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  border-radius: 10px;
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 12px;
  color: #b0bec5;
  margin-bottom: 4px;
}

.item-value {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

/* 智慧功能控制 */
.smart-controls {
  margin-bottom: 25px;
}

.smart-controls h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.smart-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.smart-btn:hover {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.3) 0%, rgba(123, 104, 238, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
}

.smart-btn.active {
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  border-color: #4a90e2;
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
}

/* 区域选择 */
.area-selection {
  margin-bottom: 25px;
}

.area-selection h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.area-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.area-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 10px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.area-btn:hover {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

/* 实时数据 */
.realtime-data {
  margin-bottom: 25px;
}

.realtime-data h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 13px;
}

.data-label {
  color: #b0bec5;
}

.data-value {
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
}

.data-value.good {
  background: rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.data-value.normal {
  background: rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

/* 时间显示 */
.time-display {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(123, 104, 238, 0.1) 100%);
  border-radius: 12px;
  border: 1px solid rgba(74, 144, 226, 0.2);
}

.current-time {
  font-size: 20px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 5px;
}

.current-date {
  font-size: 12px;
  color: #b0bec5;
}

/* 通知系统 */
.notification {
  position: fixed;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px 20px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  z-index: 2000;
  max-width: 300px;
  animation: slideInDown 0.5s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-left: 4px solid #4caf50;
}

.notification-content {
  text-align: center;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #b0bec5;
}

/* 动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 滚动条样式 */
.smart-panel::-webkit-scrollbar {
  width: 6px;
}

.smart-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.smart-panel::-webkit-scrollbar-thumb {
  background: rgba(74, 144, 226, 0.5);
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .smart-panel {
    width: 300px;
    right: 10px;
    top: 10px;
    padding: 20px;
  }
  
  .city-overview {
    grid-template-columns: 1fr;
  }
  
  .control-grid,
  .area-grid {
    grid-template-columns: 1fr;
  }
}
</style>
