/**
 * 真实珠海地图渲染器
 * 基于实际地理数据创建3D地图
 */

import * as THREE from 'three';
import {
  ZHUHAI_BOUNDS,
  ZHUHAI_DISTRICTS,
  ZHUHAI_LANDMARKS,
  ZHUHAI_ROADS,
  ZHUHAI_COASTLINE,
  ZHUHAI_ISLANDS,
  BUILDING_HEIGHT_ZONES,
  latLngToWorld
} from './zhuhaiRealData.js';

/**
 * 创建真实珠海地图
 */
export function createRealZhuhaiMap(scene, options = {}) {
  const {
    scale = 10000,
    showBuildings = true,
    showRoads = true,
    showWater = true,
    showLandmarks = true
  } = options;

  const mapGroup = new THREE.Group();
  mapGroup.name = 'ZhuhaiRealMap';

  // 创建地形基础
  if (showWater) {
    const terrain = createBaseTerrain(scale);
    mapGroup.add(terrain);
  }

  // 创建海岸线和岛屿
  const coastline = createCoastlineAndIslands(scale);
  mapGroup.add(coastline);

  // 创建道路网络
  if (showRoads) {
    const roads = createRoadNetwork(scale);
    mapGroup.add(roads);
  }

  // 创建建筑群
  if (showBuildings) {
    const buildings = createRealBuildings(scale);
    mapGroup.add(buildings);
  }

  // 创建地标建筑
  if (showLandmarks) {
    const landmarks = createLandmarkBuildings(scale);
    mapGroup.add(landmarks);
  }

  scene.add(mapGroup);
  return mapGroup;
}

/**
 * 创建基础地形
 */
function createBaseTerrain(scale) {
  const terrainGroup = new THREE.Group();
  terrainGroup.name = 'Terrain';

  // 海水
  const waterGeometry = new THREE.PlaneGeometry(scale * 1.5, scale * 1.2);
  const waterMaterial = new THREE.MeshLambertMaterial({
    color: 0x006994,
    transparent: true,
    opacity: 0.7
  });
  const water = new THREE.Mesh(waterGeometry, waterMaterial);
  water.rotation.x = -Math.PI / 2;
  water.position.y = -5;
  terrainGroup.add(water);

  // 主要陆地区域
  ZHUHAI_DISTRICTS.forEach(district => {
    const landGeometry = new THREE.PlaneGeometry(
      Math.abs(district.bounds.east - district.bounds.west) * scale * 100,
      Math.abs(district.bounds.north - district.bounds.south) * scale * 111
    );
    
    let landColor;
    switch (district.type) {
      case 'commercial': landColor = 0xD5DBDB; break;
      case 'industrial': landColor = 0xAEB6BF; break;
      case 'residential': landColor = 0x85C1E9; break;
      case 'financial': landColor = 0xF8C471; break;
      default: landColor = 0x8FBC8F;
    }

    const landMaterial = new THREE.MeshLambertMaterial({
      color: landColor,
      transparent: true,
      opacity: 0.8
    });
    
    const land = new THREE.Mesh(landGeometry, landMaterial);
    land.rotation.x = -Math.PI / 2;
    land.position.y = -2;
    
    const worldPos = latLngToWorld(district.center.lat, district.center.lng, scale);
    land.position.x = worldPos.x;
    land.position.z = worldPos.z;
    
    terrainGroup.add(land);
  });

  return terrainGroup;
}

/**
 * 创建海岸线和岛屿
 */
function createCoastlineAndIslands(scale) {
  const coastGroup = new THREE.Group();
  coastGroup.name = 'Coastline';

  // 海岸线
  const coastlinePoints = ZHUHAI_COASTLINE.map(point => {
    const worldPos = latLngToWorld(point.lat, point.lng, scale);
    return new THREE.Vector3(worldPos.x, 2, worldPos.z);
  });

  const coastlineGeometry = new THREE.BufferGeometry().setFromPoints(coastlinePoints);
  const coastlineMaterial = new THREE.LineBasicMaterial({
    color: 0x2E86AB,
    linewidth: 3
  });
  const coastline = new THREE.Line(coastlineGeometry, coastlineMaterial);
  coastGroup.add(coastline);

  // 岛屿
  ZHUHAI_ISLANDS.forEach(island => {
    const radius = Math.sqrt(island.area) * 50; // 根据面积计算半径
    const islandGeometry = new THREE.CircleGeometry(radius, 16);
    
    let islandColor;
    switch (island.type) {
      case 'development': islandColor = 0xF4D03F; break;
      case 'ecological': islandColor = 0x58D68D; break;
      case 'tourism': islandColor = 0x85C1E9; break;
      default: islandColor = 0x90EE90;
    }

    const islandMaterial = new THREE.MeshLambertMaterial({ color: islandColor });
    const islandMesh = new THREE.Mesh(islandGeometry, islandMaterial);
    islandMesh.rotation.x = -Math.PI / 2;
    islandMesh.position.y = -1;
    
    const worldPos = latLngToWorld(island.center.lat, island.center.lng, scale);
    islandMesh.position.x = worldPos.x;
    islandMesh.position.z = worldPos.z;
    
    coastGroup.add(islandMesh);
  });

  return coastGroup;
}

/**
 * 创建道路网络
 */
function createRoadNetwork(scale) {
  const roadGroup = new THREE.Group();
  roadGroup.name = 'Roads';

  ZHUHAI_ROADS.forEach(road => {
    for (let i = 0; i < road.points.length - 1; i++) {
      const start = latLngToWorld(road.points[i].lat, road.points[i].lng, scale);
      const end = latLngToWorld(road.points[i + 1].lat, road.points[i + 1].lng, scale);
      
      const length = Math.sqrt(
        Math.pow(end.x - start.x, 2) + Math.pow(end.z - start.z, 2)
      );
      
      const roadGeometry = new THREE.BoxGeometry(length, 1, road.width);
      
      let roadColor;
      switch (road.type) {
        case 'highway': roadColor = 0x1C2833; break;
        case 'main': roadColor = 0x2C3E50; break;
        case 'coastal': roadColor = 0x34495E; break;
        default: roadColor = 0x566573;
      }
      
      const roadMaterial = new THREE.MeshLambertMaterial({ color: roadColor });
      const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
      
      const centerX = (start.x + end.x) / 2;
      const centerZ = (start.z + end.z) / 2;
      const angle = Math.atan2(end.z - start.z, end.x - start.x);
      
      roadMesh.position.set(centerX, 0.5, centerZ);
      roadMesh.rotation.y = angle;
      
      roadGroup.add(roadMesh);
    }
  });

  return roadGroup;
}

/**
 * 创建真实建筑群
 */
function createRealBuildings(scale) {
  const buildingGroup = new THREE.Group();
  buildingGroup.name = 'Buildings';

  BUILDING_HEIGHT_ZONES.forEach(zone => {
    const worldPos = latLngToWorld(zone.center.lat, zone.center.lng, scale);
    const buildingCount = Math.floor(zone.density * 50); // 根据密度确定建筑数量

    for (let i = 0; i < buildingCount; i++) {
      // 在区域内随机分布建筑
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * zone.radius;
      const x = worldPos.x + Math.cos(angle) * distance;
      const z = worldPos.z + Math.sin(angle) * distance;

      // 根据距离中心的远近调整建筑高度
      const distanceRatio = distance / zone.radius;
      const heightVariation = 1 - distanceRatio * 0.5; // 中心区域建筑更高
      const height = zone.avgHeight * heightVariation + Math.random() * (zone.maxHeight - zone.avgHeight) * heightVariation;

      const building = createRealisticBuilding(height, zone.name);
      building.position.set(x, height / 2, z);
      buildingGroup.add(building);
    }
  });

  return buildingGroup;
}

/**
 * 创建真实建筑
 */
function createRealisticBuilding(height, zoneName) {
  const group = new THREE.Group();
  
  const width = 15 + Math.random() * 25;
  const depth = 12 + Math.random() * 20;
  
  const geometry = new THREE.BoxGeometry(width, height, depth);
  
  // 根据区域选择建筑颜色
  let buildingColor;
  if (zoneName.includes('CBD') || zoneName.includes('金融')) {
    buildingColor = 0x2C3E50; // 商务蓝灰
  } else if (zoneName.includes('工业')) {
    buildingColor = 0x7F8C8D; // 工业灰
  } else {
    buildingColor = 0x5D6D7E; // 住宅灰蓝
  }
  
  const material = new THREE.MeshLambertMaterial({
    color: buildingColor,
    transparent: true,
    opacity: 0.9
  });
  
  const building = new THREE.Mesh(geometry, material);
  building.castShadow = true;
  building.receiveShadow = true;
  group.add(building);
  
  // 为高层建筑添加玻璃幕墙
  if (height > 60) {
    const glassGeometry = new THREE.BoxGeometry(width + 0.5, height + 0.5, depth + 0.5);
    const glassMaterial = new THREE.MeshLambertMaterial({
      color: 0x87CEEB,
      transparent: true,
      opacity: 0.3
    });
    const glass = new THREE.Mesh(glassGeometry, glassMaterial);
    group.add(glass);
  }
  
  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建地标建筑
 */
function createLandmarkBuildings(scale) {
  const landmarkGroup = new THREE.Group();
  landmarkGroup.name = 'Landmarks';

  ZHUHAI_LANDMARKS.forEach(landmark => {
    const worldPos = latLngToWorld(landmark.lat, landmark.lng, scale);
    const building = createLandmarkBuilding(landmark);
    building.position.set(worldPos.x, landmark.height / 2, worldPos.z);
    landmarkGroup.add(building);
  });

  return landmarkGroup;
}

/**
 * 创建特定地标建筑
 */
function createLandmarkBuilding(landmark) {
  const group = new THREE.Group();
  
  let geometry, material;
  
  switch (landmark.type) {
    case 'cultural': // 珠海大剧院
      geometry = new THREE.SphereGeometry(40, 16, 8);
      material = new THREE.MeshLambertMaterial({ color: 0xF39C12 });
      break;
      
    case 'monument': // 渔女雕像
      geometry = new THREE.ConeGeometry(8, landmark.height, 8);
      material = new THREE.MeshLambertMaterial({ color: 0xBDC3C7 });
      break;
      
    case 'airport': // 机场
      geometry = new THREE.BoxGeometry(200, landmark.height, 80);
      material = new THREE.MeshLambertMaterial({ color: 0x95A5A6 });
      break;
      
    case 'bridge': // 大桥口岸
      geometry = new THREE.BoxGeometry(100, landmark.height, 60);
      material = new THREE.MeshLambertMaterial({ color: 0x85929E });
      break;
      
    default:
      geometry = new THREE.BoxGeometry(30, landmark.height, 25);
      material = new THREE.MeshLambertMaterial({ color: 0x3498DB });
  }
  
  const building = new THREE.Mesh(geometry, material);
  building.castShadow = true;
  building.receiveShadow = true;
  group.add(building);
  
  // 添加标签
  group.userData = {
    name: landmark.name,
    type: landmark.type,
    description: landmark.description
  };
  
  return group;
}
