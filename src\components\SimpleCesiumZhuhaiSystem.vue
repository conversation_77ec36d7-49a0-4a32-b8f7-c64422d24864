<template>
  <div class="cesium-weather-system">
    <!-- Cesium容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市CesiumJS三维气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">CesiumJS版本:</span>
          <span class="status-value" :class="cesiumVersion ? 'online' : 'error'">{{ cesiumVersion || '未加载' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">实体数量:</span>
          <span class="status-value online">{{ entityCount }} 个</span>
        </div>
        <div class="status-item">
          <span class="status-label">活跃预警:</span>
          <span class="status-value" :class="activeWarnings.length > 0 ? 'warning' : 'online'">{{ activeWarnings.length }} 个</span>
        </div>
        <div class="status-item">
          <span class="status-label">系统时间:</span>
          <span class="status-value online">{{ currentTime }}</span>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="realtime-data">
        <h4>📊 实时数据</h4>
        <div class="data-grid">
          <div class="data-item">
            <span class="data-label">温度</span>
            <span class="data-value">{{ weatherData.temperature }}°C</span>
          </div>
          <div class="data-item">
            <span class="data-label">湿度</span>
            <span class="data-value">{{ weatherData.humidity }}%</span>
          </div>
          <div class="data-item">
            <span class="data-label">风速</span>
            <span class="data-value">{{ weatherData.windSpeed }}m/s</span>
          </div>
          <div class="data-item">
            <span class="data-label">气压</span>
            <span class="data-value">{{ weatherData.pressure }}hPa</span>
          </div>
          <div class="data-item">
            <span class="data-label">能见度</span>
            <span class="data-value">{{ weatherData.visibility }}km</span>
          </div>
          <div class="data-item">
            <span class="data-label">降雨量</span>
            <span class="data-value">{{ weatherData.rainfall }}mm</span>
          </div>
        </div>
      </div>
      
      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          ➕ 生成新预警
        </button>
        <button @click="generateSevereWarning" class="control-btn severe" :disabled="!isSystemReady">
          🚨 严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn auto" :class="{ active: autoUpdate }" :disabled="!isSystemReady">
          {{ autoUpdate ? '⏸️ 暂停自动' : '▶️ 自动更新' }}
        </button>
        <button @click="showTerrainDemo" class="control-btn demo" :disabled="!isSystemReady">
          🏔️ 地形演示
        </button>
        <button @click="exportWarningData" class="control-btn export" :disabled="activeWarnings.length === 0">
          📊 导出数据
        </button>
        <button @click="fixBlueScreen" class="control-btn fix" :disabled="!isSystemReady">
          🌍 修复蓝屏
        </button>
        <button @click="showEarthSurface" class="control-btn earth" :disabled="!isSystemReady">
          🌎 显示地球
        </button>
      </div>

      <!-- 地形控制 -->
      <div class="terrain-control">
        <h4>🏔️ 地形控制</h4>
        <div class="terrain-options">
          <label class="terrain-option">
            <input type="radio" v-model="currentTerrain" value="flat" @change="switchTerrain">
            <span>🌊 平面地形</span>
          </label>
          <label class="terrain-option">
            <input type="radio" v-model="currentTerrain" value="cesium" @change="switchTerrain">
            <span>🌍 Cesium地形</span>
          </label>
          <label class="terrain-option">
            <input type="radio" v-model="currentTerrain" value="custom" @change="switchTerrain">
            <span>🏔️ 珠海DEM</span>
          </label>
        </div>
        <div class="terrain-status">
          <span class="terrain-label">当前地形:</span>
          <span class="terrain-value" :class="terrainStatus.class">{{ terrainStatus.text }}</span>
        </div>
      </div>

      <!-- 视图控制 -->
      <div class="view-control">
        <h4>🎥 视图控制</h4>
        <div class="view-buttons">
          <button @click="setView('overview')" class="view-btn">🌍 总览</button>
          <button @click="setView('aerial')" class="view-btn">🚁 航拍</button>
          <button @click="setView('ground')" class="view-btn">👁️ 地面</button>
          <button @click="setView('terrain')" class="view-btn">🏔️ 地形</button>
          <button @click="setView('earth')" class="view-btn">🌎 地球</button>
        </div>
      </div>

      <!-- 预警列表 -->
      <div class="warning-list">
        <h4>🚨 活跃预警</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-container">
          <div
            v-for="warning in activeWarnings"
            :key="warning.id"
            class="warning-item"
            :class="warning.level"
            @click="flyToWarning(warning)"
          >
            <div class="warning-header">
              <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
              <span class="warning-type">{{ warning.type }}预警</span>
              <span class="warning-level">{{ getWarningLevelText(warning.level) }}</span>
            </div>
            <div class="warning-details">
              <div class="warning-location">📍 {{ warning.location }}</div>
              <div class="warning-time">🕐 {{ warning.time }}</div>
              <div class="warning-intensity">💪 强度: {{ warning.intensity || '中等' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-description">{{ area.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import * as Cesium from 'cesium';
import { createZhuhaiTerrainProvider, ZHUHAI_BOUNDS, ZHUHAI_ELEVATIONS } from '../utils/zhuhaiDEM.js';

// Cesium相关变量
let viewer = null;
let cesiumLoaded = false;

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const notification = ref(null);
const cesiumVersion = ref('');
const entityCount = ref(0);
const currentTerrain = ref('flat');
const currentTime = ref('');
const autoUpdate = ref(false);

// 实时气象数据
const weatherData = ref({
  temperature: 25,
  humidity: 68,
  windSpeed: 3.2,
  pressure: 1013,
  visibility: 15,
  rainfall: 0
});

// 系统状态
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const terrainStatus = ref({ text: '平面地形', class: 'online' });
const isSystemReady = computed(() => systemStatus.value.class === 'online');

// 系统变量
let warningEntities = [];
let terrainEntities = [];
let warningIdCounter = 0;
let timeUpdateInterval = null;
let weatherUpdateInterval = null;
let autoWarningInterval = null;

// 珠海市重要区域（真实经纬度坐标）
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2707,
    description: '珠海市中心区域，商务繁华'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.4200,
    latitude: 22.1300,
    description: '珠海经济特区，发展迅速'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3600,
    latitude: 22.1400,
    description: '珠海机场所在地'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化简化版CesiumJS系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem(retryCount = 0) {
  const maxRetries = 3;

  try {
    console.log(`🚀 开始初始化系统 (尝试 ${retryCount + 1}/${maxRetries + 1})`);

    // 步骤1: 环境检查
    systemStatus.value = { text: '检查运行环境...', class: 'loading' };
    await checkEnvironment();

    // 步骤2: 设置CesiumJS
    systemStatus.value = { text: '设置CesiumJS...', class: 'loading' };
    setupCesiumJS();

    // 等待一帧确保设置生效
    await new Promise(resolve => requestAnimationFrame(resolve));

    // 步骤3: 初始化Cesium Viewer
    systemStatus.value = { text: '初始化3D地球...', class: 'loading' };
    await initializeCesiumViewer();

    // 等待Viewer完全初始化
    await waitForViewerReady();

    // 步骤4: 创建珠海标记
    systemStatus.value = { text: '创建珠海标记...', class: 'loading' };
    await createZhuhaiMarkers();

    // 步骤5: 设置相机位置
    systemStatus.value = { text: '设置初始视图...', class: 'loading' };
    await setInitialViewSafely();

    // 步骤6: 强制显示地球
    systemStatus.value = { text: '优化地球显示...', class: 'loading' };
    await forceShowEarth();

    // 步骤7: 启动系统定时器
    systemStatus.value = { text: '启动系统服务...', class: 'loading' };
    startSystemTimers();

    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };

    console.log('✅ CesiumJS系统初始化完成');
    showNotification('✅ 3D地球系统启动成功', 'success');

    // 延迟生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 3000);

  } catch (error) {
    console.error(`❌ 系统初始化失败 (尝试 ${retryCount + 1}):`, error);

    // 如果还有重试次数，则重试
    if (retryCount < maxRetries) {
      console.log(`🔄 ${2}秒后重试初始化...`);
      systemStatus.value = { text: `重试中... (${retryCount + 1}/${maxRetries})`, class: 'loading' };

      // 清理资源
      cleanup();

      // 延迟重试
      setTimeout(() => {
        initializeSystem(retryCount + 1);
      }, 2000);
    } else {
      // 所有重试都失败了
      systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
      showNotification(`❌ 系统初始化失败: ${error.message}`, 'error');
      showRetryOptions();
    }
  }
}

// 检查运行环境
async function checkEnvironment() {
  try {
    // 检查WebGL支持
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      throw new Error('您的浏览器不支持WebGL，无法运行3D地球');
    }

    // 检查Cesium模块
    if (!Cesium) {
      throw new Error('CesiumJS模块未正确加载');
    }

    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('3D地球容器未找到');
    }

    console.log('✅ 运行环境检查通过');
  } catch (error) {
    throw new Error(`环境检查失败: ${error.message}`);
  }
}

// 等待Viewer准备就绪
async function waitForViewerReady() {
  return new Promise((resolve, reject) => {
    if (!viewer) {
      reject(new Error('Viewer未创建'));
      return;
    }

    let attempts = 0;
    const maxAttempts = 50; // 5秒超时

    const checkReady = () => {
      attempts++;

      try {
        // 检查Viewer是否可用
        if (viewer.scene && viewer.camera && viewer.entities) {
          console.log('✅ Viewer准备就绪');
          resolve();
          return;
        }
      } catch (error) {
        console.warn('Viewer检查失败:', error);
      }

      if (attempts >= maxAttempts) {
        reject(new Error('Viewer初始化超时'));
        return;
      }

      setTimeout(checkReady, 100);
    };

    checkReady();
  });
}

// 安全设置初始视图
async function setInitialViewSafely() {
  try {
    if (!viewer || !viewer.camera) {
      throw new Error('相机未准备就绪');
    }

    // 使用更稳定的相机设置方法 - 设置到珠海上空
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000), // 10万米高度
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-60), // 60度俯视角，可以看到地球曲率
        roll: 0.0
      }
    });

    console.log('✅ 初始视图设置完成');
  } catch (error) {
    console.warn('初始视图设置失败:', error);
    // 不抛出错误，使用默认视图
  }
}

// 强制显示地球
async function forceShowEarth() {
  try {
    if (!viewer) {
      throw new Error('Viewer未创建');
    }

    console.log('🌍 强制显示真实地球...');

    // 确保地球显示
    viewer.scene.globe.show = true;

    // 如果没有影像层，添加备用地球纹理
    if (!viewer.imageryLayers || viewer.imageryLayers.length === 0) {
      console.log('添加备用地球纹理...');
      const earthProvider = createFallbackImageryProvider();
      viewer.imageryLayers.addImageryProvider(earthProvider);
    }

    // 确保第一个影像层可见
    if (viewer.imageryLayers.length > 0) {
      const layer = viewer.imageryLayers.get(0);
      layer.show = true;
      layer.alpha = 1.0;
    }

    // 不设置地球基础颜色，让影像层显示
    // viewer.scene.globe.baseColor = undefined;

    // 启用真实地球效果
    viewer.scene.globe.enableLighting = true; // 启用光照显示球形
    viewer.scene.globe.showWaterEffect = true; // 显示水面效果
    viewer.scene.globe.showGroundAtmosphere = true; // 显示地面大气

    // 设置太空背景色
    viewer.scene.backgroundColor = Cesium.Color.fromCssColorString('#000011');

    // 启用大气和天空效果
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.skyBox.show = true;
    viewer.scene.sun.show = true;

    // 设置合适的相机位置以显示地球表面
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 2000000), // 200万米高度，可以看到地球曲率
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45), // 45度俯视角度
        roll: 0.0
      }
    });

    // 强制渲染
    viewer.scene.requestRender();

    // 等待一帧确保渲染
    await new Promise(resolve => requestAnimationFrame(resolve));

    console.log('✅ 真实地球显示完成');
  } catch (error) {
    console.warn('强制显示地球失败:', error);
  }
}

// 显示重试选项
function showRetryOptions() {
  showNotification('系统初始化失败，您可以尝试刷新页面或切换到Canvas版本', 'error');
}

// 设置CesiumJS
function setupCesiumJS() {
  try {
    // 设置Cesium访问令牌（使用更稳定的令牌）
    if (Cesium && Cesium.Ion) {
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
      console.log('Cesium访问令牌设置成功');
    }

    // 设置全局Cesium对象以保持兼容性
    window.Cesium = Cesium;

    // 设置Cesium全局配置以提高稳定性
    if (Cesium.RequestScheduler) {
      Cesium.RequestScheduler.requestsByServer = {
        'tile.openstreetmap.org:443': 6,
        'a.tile.openstreetmap.org:443': 6,
        'b.tile.openstreetmap.org:443': 6,
        'c.tile.openstreetmap.org:443': 6
      };
      Cesium.RequestScheduler.maximumRequestsPerServer = 6;
    }

    // 设置默认的错误处理
    if (Cesium.TileProviderError) {
      Cesium.TileProviderError.handleError = function(provider, error) {
        console.warn('瓦片加载错误，但继续运行:', error);
        return false; // 不抛出错误
      };
    }

    cesiumVersion.value = Cesium.VERSION;
    cesiumLoaded = true;
    console.log('CesiumJS版本:', Cesium.VERSION);
    console.log('✅ 本地CesiumJS设置成功');
  } catch (error) {
    throw new Error(`CesiumJS设置失败: ${error.message}`);
  }
}

// 初始化Cesium Viewer
async function initializeCesiumViewer() {
  try {
    await nextTick();

    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到');
    }

    if (!cesiumLoaded || !Cesium) {
      throw new Error('CesiumJS未正确加载');
    }

    console.log('开始创建Cesium Viewer...');

    // 创建稳定的地形提供商
    const terrainProvider = new Cesium.EllipsoidTerrainProvider();

    // 创建多重备用的影像提供商
    let imageryProvider;
    try {
      // 首先尝试使用Cesium默认影像
      imageryProvider = await createReliableImageryProvider();
    } catch (error) {
      console.warn('所有影像提供商失败，使用绿色地球背景');
      imageryProvider = createFallbackImageryProvider();
    }

    // 使用高度优化的配置创建Cesium Viewer
    const viewerOptions = {
      // 禁用所有UI组件以提高稳定性
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false,
      vrButton: false,

      // 使用稳定的提供商
      terrainProvider: terrainProvider,
      imageryProvider: imageryProvider,

      // 性能和稳定性优化
      requestRenderMode: false, // 连续渲染模式
      maximumRenderTimeChange: Infinity,
      targetFrameRate: 30, // 限制帧率

      // 渲染优化
      shadows: false,
      terrainShadows: Cesium.ShadowMode.DISABLED,

      // 场景优化 - 使用默认天空盒
      skyBox: undefined, // 使用默认天空盒

      // 错误处理
      contextOptions: {
        webgl: {
          alpha: false,
          depth: true,
          stencil: false,
          antialias: true,
          premultipliedAlpha: true,
          preserveDrawingBuffer: false,
          failIfMajorPerformanceCaveat: false
        }
      }
    };

    // 创建Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, viewerOptions);

    // 后处理优化设置
    await optimizeViewerSettings();

    console.log('✅ Cesium Viewer创建成功');
  } catch (error) {
    console.error('Cesium Viewer初始化详细错误:', error);
    throw new Error(`Cesium Viewer初始化失败: ${error.message}`);
  }
}

// 创建可靠的影像提供商
async function createReliableImageryProvider() {
  const providers = [
    // 1. 尝试使用Cesium World Imagery
    () => new Cesium.ArcGisMapServerImageryProvider({
      url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
      enablePickFeatures: false
    }),

    // 2. 尝试使用OpenStreetMap
    () => new Cesium.OpenStreetMapImageryProvider({
      url: 'https://a.tile.openstreetmap.org/',
      maximumLevel: 18
    }),

    // 3. 尝试使用CartoDB
    () => new Cesium.UrlTemplateImageryProvider({
      url: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}.png',
      subdomains: ['a', 'b', 'c', 'd'],
      maximumLevel: 18
    }),

    // 4. 备用：使用Stamen地图
    () => new Cesium.UrlTemplateImageryProvider({
      url: 'https://stamen-tiles.a.ssl.fastly.net/terrain/{z}/{x}/{y}.jpg',
      maximumLevel: 16,
      credit: 'Map tiles by Stamen Design'
    })
  ];

  for (let i = 0; i < providers.length; i++) {
    try {
      console.log(`尝试影像提供商 ${i + 1}...`);
      const provider = providers[i]();

      // 测试提供商是否可用
      await testImageryProvider(provider);

      console.log(`✅ 影像提供商 ${i + 1} 可用`);
      return provider;
    } catch (error) {
      console.warn(`影像提供商 ${i + 1} 失败:`, error);
    }
  }

  throw new Error('所有影像提供商都不可用');
}

// 测试影像提供商
function testImageryProvider(provider) {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('影像提供商测试超时'));
    }, 5000);

    try {
      // 简单测试：检查提供商是否有必要的属性
      if (provider && provider.url) {
        clearTimeout(timeout);
        resolve();
      } else {
        clearTimeout(timeout);
        reject(new Error('影像提供商无效'));
      }
    } catch (error) {
      clearTimeout(timeout);
      reject(error);
    }
  });
}

// 创建备用影像提供商（真实地球纹理）
function createFallbackImageryProvider() {
  // 创建一个更真实的地球纹理
  const canvas = document.createElement('canvas');
  canvas.width = 512;
  canvas.height = 256;
  const ctx = canvas.getContext('2d');

  // 创建渐变背景（模拟地球的海洋和陆地）
  const gradient = ctx.createLinearGradient(0, 0, 512, 256);
  gradient.addColorStop(0, '#1e40af');    // 深蓝色（海洋）
  gradient.addColorStop(0.3, '#2563eb');  // 蓝色
  gradient.addColorStop(0.5, '#22c55e');  // 绿色（陆地）
  gradient.addColorStop(0.7, '#16a34a');  // 深绿色
  gradient.addColorStop(1, '#15803d');    // 更深绿色

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, 512, 256);

  // 添加陆地纹理
  ctx.fillStyle = '#22c55e';
  for (let i = 0; i < 100; i++) {
    const x = Math.random() * 512;
    const y = Math.random() * 256;
    const width = Math.random() * 50 + 20;
    const height = Math.random() * 30 + 10;

    ctx.beginPath();
    ctx.ellipse(x, y, width, height, Math.random() * Math.PI, 0, 2 * Math.PI);
    ctx.fill();
  }

  // 添加云层效果
  ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
  for (let i = 0; i < 30; i++) {
    const x = Math.random() * 512;
    const y = Math.random() * 256;
    const radius = Math.random() * 20 + 10;

    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);
    ctx.fill();
  }

  // 转换为数据URL
  const dataUrl = canvas.toDataURL();

  return new Cesium.SingleTileImageryProvider({
    url: dataUrl,
    rectangle: Cesium.Rectangle.MAX_VALUE,
    credit: '离线地球纹理'
  });
}

// 优化Viewer设置
async function optimizeViewerSettings() {
  try {
    // 确保地球可见性设置
    viewer.scene.globe.show = true; // 确保地球显示
    viewer.scene.globe.enableLighting = true; // 启用光照以显示球形效果
    viewer.scene.globe.dynamicAtmosphereLighting = false;
    viewer.scene.globe.dynamicAtmosphereLightingFromSun = false;

    // 设置地球基础颜色以确保可见
    viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#1a4b3a'); // 深绿色基础
    viewer.scene.backgroundColor = Cesium.Color.fromCssColorString('#001122'); // 深蓝色太空背景

    // 雾效设置
    viewer.scene.fog.enabled = true;
    viewer.scene.fog.density = 0.0002;

    // 天空和大气设置 - 显示真实的地球大气
    viewer.scene.skyBox.show = true; // 显示天空盒
    viewer.scene.skyAtmosphere.show = true; // 显示大气层
    viewer.scene.sun.show = true; // 显示太阳
    viewer.scene.moon.show = false; // 隐藏月亮

    // 确保影像层可见
    if (viewer.imageryLayers && viewer.imageryLayers.length > 0) {
      const imageryLayer = viewer.imageryLayers.get(0);
      imageryLayer.show = true;
      imageryLayer.alpha = 1.0;
      console.log('✅ 影像层设置完成');
    }

    // 相机控制优化
    const controller = viewer.scene.screenSpaceEventHandler;

    // 禁用可能导致问题的默认行为
    try {
      controller.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
      controller.removeInputAction(Cesium.ScreenSpaceEventType.MIDDLE_CLICK);
    } catch (error) {
      console.warn('移除默认事件失败:', error);
    }

    // 设置相机限制
    const cameraController = viewer.scene.screenSpaceCameraController;
    cameraController.minimumZoomDistance = 1000; // 最小距离1公里
    cameraController.maximumZoomDistance = 50000000; // 最大距离5万公里
    cameraController.enableRotate = true;
    cameraController.enableTranslate = true;
    cameraController.enableZoom = true;
    cameraController.enableTilt = true;
    cameraController.enableLook = true;

    // 错误处理
    viewer.scene.renderError.addEventListener((scene, error) => {
      console.warn('渲染错误，但继续运行:', error);
    });

    // 瓦片加载错误处理
    viewer.scene.globe.tileLoadProgressEvent.addEventListener((queuedTileCount) => {
      if (queuedTileCount === 0) {
        console.log('所有瓦片加载完成');
      }
    });

    // 强制渲染一帧
    viewer.scene.requestRender();

    console.log('✅ Viewer优化设置完成');
  } catch (error) {
    console.warn('Viewer优化设置失败:', error);
  }
}

// 创建珠海标记
async function createZhuhaiMarkers() {
  try {
    console.log('创建珠海区域标记...');

    if (!viewer || !viewer.entities) {
      throw new Error('Viewer实体管理器未准备就绪');
    }

    // 逐个创建标记，增加错误处理
    for (const area of zhuhaiAreas.value) {
      try {
        const entity = viewer.entities.add({
          name: area.name,
          position: Cesium.Cartesian3.fromDegrees(
            area.longitude,
            area.latitude,
            100
          ),
          point: {
            pixelSize: 12,
            color: Cesium.Color.YELLOW,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
          },
          label: {
            text: `${area.icon} ${area.name}`,
            font: '12pt sans-serif',
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            pixelOffset: new Cesium.Cartesian2(0, -30),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
            heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
          }
        });

        console.log(`✅ 创建标记: ${area.name}`);
      } catch (error) {
        console.warn(`标记创建失败: ${area.name}`, error);
      }
    }

    updateEntityCount();
    console.log('✅ 珠海标记创建完成');
  } catch (error) {
    console.error('珠海标记创建失败:', error);
    throw error; // 重新抛出错误以便上层处理
  }
}

// 设置初始视图
function setInitialView() {
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 50000),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    }
  });
}

// 更新实体计数
function updateEntityCount() {
  if (viewer) {
    entityCount.value = viewer.entities.values.length;
  }
}

// 生成初始预警
function generateInitialWarnings() {
  generateNewWarning();
  setTimeout(() => generateNewWarning(), 1000);
}

// 启动系统定时器
function startSystemTimers() {
  // 时间更新定时器
  timeUpdateInterval = setInterval(() => {
    currentTime.value = new Date().toLocaleTimeString();
  }, 1000);

  // 气象数据更新定时器
  weatherUpdateInterval = setInterval(() => {
    updateWeatherData();
  }, 5000);

  // 系统健康检查定时器
  const healthCheckInterval = setInterval(() => {
    performHealthCheck();
  }, 30000); // 每30秒检查一次

  // 保存定时器引用以便清理
  if (!window.systemIntervals) {
    window.systemIntervals = [];
  }
  window.systemIntervals.push(healthCheckInterval);
}

// 系统健康检查
function performHealthCheck() {
  try {
    if (!viewer) {
      console.warn('健康检查: Viewer不存在');
      return;
    }

    if (!viewer.scene) {
      console.warn('健康检查: Scene不存在');
      return;
    }

    // 检查渲染状态
    const frameNumber = viewer.scene.frameState?.frameNumber;
    if (frameNumber && frameNumber > 0) {
      console.log('✅ 系统健康检查通过');
    } else {
      console.warn('健康检查: 渲染可能有问题');
    }

    // 检查实体数量
    const entityCount = viewer.entities.values.length;
    if (entityCount !== entityCount.value) {
      updateEntityCount();
    }

  } catch (error) {
    console.warn('健康检查失败:', error);
  }
}

// 更新气象数据
function updateWeatherData() {
  // 模拟真实的气象数据变化
  weatherData.value.temperature = Math.round((25 + (Math.random() - 0.5) * 10) * 10) / 10;
  weatherData.value.humidity = Math.round((68 + (Math.random() - 0.5) * 20));
  weatherData.value.windSpeed = Math.round((3.2 + (Math.random() - 0.5) * 4) * 10) / 10;
  weatherData.value.pressure = Math.round(1013 + (Math.random() - 0.5) * 20);
  weatherData.value.visibility = Math.round((15 + (Math.random() - 0.5) * 10) * 10) / 10;
  weatherData.value.rainfall = Math.round((Math.random() * 5) * 10) / 10;
}

// 生成新预警
function generateNewWarning() {
  if (!viewer) return;

  const warningTypes = ['大风', '暴雨', '雷电', '冰雹', '大雾', '高温'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const intensityLevels = ['轻微', '中等', '较强', '强烈', '极强'];
  const areas = zhuhaiAreas.value;

  const area = areas[Math.floor(Math.random() * areas.length)];
  const type = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const level = warningLevels[Math.floor(Math.random() * warningLevels.length)];
  const intensity = intensityLevels[Math.floor(Math.random() * intensityLevels.length)];

  const warning = {
    id: ++warningIdCounter,
    type,
    level,
    intensity,
    location: area.name,
    longitude: area.longitude + (Math.random() - 0.5) * 0.01,
    latitude: area.latitude + (Math.random() - 0.5) * 0.01,
    time: new Date().toLocaleTimeString(),
    duration: Math.floor(Math.random() * 6) + 1 // 1-6小时
  };

  activeWarnings.value.push(warning);

  // 在地图上创建预警实体
  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    name: `${warning.type}预警`,
    position: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      200
    ),
    cylinder: {
      length: getWarningHeight(warning.level),
      topRadius: getWarningRadius(warning.level, 'top'),
      bottomRadius: getWarningRadius(warning.level, 'bottom'),
      material: getWarningColor(warning.level).withAlpha(0.7),
      outline: true,
      outlineColor: getWarningColor(warning.level)
    },
    label: {
      text: `${getWarningIcon(warning.type)} ${warning.type}预警\n${getWarningLevelText(warning.level)}`,
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -50),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    }
  });

  warningEntities.push(entity);
  updateEntityCount();

  console.log(`生成${warning.type}预警:`, warning);
  showNotification(`🚨 ${area.name}发布${warning.type}${getWarningLevelText(warning.level)}预警`, 'warning');
}

// 生成严重预警
function generateSevereWarning() {
  if (!viewer) return;

  const severeTypes = ['台风', '暴雪', '暴雨', '雷暴'];
  const areas = zhuhaiAreas.value;

  const area = areas[Math.floor(Math.random() * areas.length)];
  const type = severeTypes[Math.floor(Math.random() * severeTypes.length)];

  const warning = {
    id: ++warningIdCounter,
    type,
    level: 'red',
    intensity: '极强',
    location: area.name,
    longitude: area.longitude + (Math.random() - 0.5) * 0.01,
    latitude: area.latitude + (Math.random() - 0.5) * 0.01,
    time: new Date().toLocaleTimeString(),
    duration: Math.floor(Math.random() * 12) + 6 // 6-18小时
  };

  activeWarnings.value.push(warning);

  // 创建更大的预警实体
  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    name: `${warning.type}预警`,
    position: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      300
    ),
    cylinder: {
      length: 1000,
      topRadius: 500,
      bottomRadius: 800,
      material: Cesium.Color.RED.withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.DARKRED
    },
    label: {
      text: `⚠️ ${warning.type}红色预警\n极度危险`,
      font: '16pt sans-serif',
      fillColor: Cesium.Color.YELLOW,
      outlineColor: Cesium.Color.RED,
      outlineWidth: 3,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -80),
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    }
  });

  warningEntities.push(entity);
  updateEntityCount();

  console.log(`生成严重${warning.type}预警:`, warning);
  showNotification(`🚨 ${area.name}发布${warning.type}红色预警！`, 'error');
}

// 切换自动更新
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    autoWarningInterval = setInterval(() => {
      if (activeWarnings.value.length < 8) { // 最多8个预警
        generateNewWarning();
      }
    }, 10000); // 每10秒生成一个预警
    showNotification('▶️ 已开启自动预警更新', 'info');
  } else {
    if (autoWarningInterval) {
      clearInterval(autoWarningInterval);
      autoWarningInterval = null;
    }
    showNotification('⏸️ 已暂停自动预警更新', 'info');
  }
}

// 导出预警数据
function exportWarningData() {
  const data = {
    exportTime: new Date().toISOString(),
    totalWarnings: activeWarnings.value.length,
    warnings: activeWarnings.value.map(warning => ({
      ...warning,
      coordinates: [warning.longitude, warning.latitude]
    })),
    weatherData: weatherData.value,
    systemInfo: {
      cesiumVersion: cesiumVersion.value,
      entityCount: entityCount.value,
      terrainMode: currentTerrain.value
    }
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `珠海气象预警数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showNotification('📊 预警数据导出成功', 'success');
}

// 飞行到预警位置
function flyToWarning(warning) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      5000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2
  });

  showNotification(`🎯 飞行到${warning.location}的${warning.type}预警`, 'info');
}

// 显示地球表面
async function showEarthSurface() {
  if (!viewer) {
    showNotification('❌ 系统未初始化', 'error');
    return;
  }

  try {
    showNotification('🌎 正在显示地球表面...', 'info');

    // 确保地球可见
    viewer.scene.globe.show = true;
    viewer.scene.globe.enableLighting = true;

    // 设置地球基础颜色
    viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#1a4b3a');

    // 确保影像层可见
    if (viewer.imageryLayers && viewer.imageryLayers.length > 0) {
      const layer = viewer.imageryLayers.get(0);
      layer.show = true;
      layer.alpha = 1.0;
    } else {
      // 如果没有影像层，添加一个
      const earthProvider = createFallbackImageryProvider();
      viewer.imageryLayers.addImageryProvider(earthProvider);
    }

    // 飞行到珠海上空，可以看到地球表面
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 50000), // 5万米高度
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45), // 45度俯视角
        roll: 0.0
      },
      duration: 3
    });

    // 启用视觉效果
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.fog.density = 0.0002;

    showNotification('✅ 地球表面显示完成！', 'success');
    console.log('✅ 地球表面显示完成');

  } catch (error) {
    console.error('显示地球表面失败:', error);
    showNotification('❌ 显示地球表面失败', 'error');
  }
}

// 修复蓝屏问题
async function fixBlueScreen() {
  if (!viewer) {
    showNotification('❌ 系统未初始化', 'error');
    return;
  }

  try {
    showNotification('🔧 正在修复显示问题...', 'info');

    // 1. 移除所有现有影像层
    viewer.imageryLayers.removeAll();

    // 2. 添加真实地球纹理
    const earthProvider = createFallbackImageryProvider();
    const layer = viewer.imageryLayers.addImageryProvider(earthProvider);
    layer.show = true;
    layer.alpha = 1.0;

    // 3. 强制显示真实地球
    await forceShowEarth();

    // 4. 设置合适的相机位置显示球形地球
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 15000000), // 1500万米高度
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-30), // 30度俯视角
        roll: 0.0
      },
      duration: 3
    });

    // 5. 强制渲染
    viewer.scene.requestRender();

    showNotification('✅ 显示问题已修复！现在显示真实地球', 'success');
    console.log('✅ 地球显示修复完成');

  } catch (error) {
    console.error('显示修复失败:', error);
    showNotification('❌ 显示修复失败', 'error');
  }
}

// 清除所有预警
function clearAllWarnings() {
  warningEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  warningEntities = [];
  activeWarnings.value = [];
  updateEntityCount();
  showNotification('✅ 已清除所有预警', 'success');
}

// 显示地形演示
async function showTerrainDemo() {
  if (!viewer) return;

  try {
    showNotification('🏔️ 正在创建地形演示...', 'info');

    // 清除现有地形实体
    clearTerrainEntities();

    // 创建明显的山峰模型
    createDemoMountains();

    // 创建地形网格
    createDemoTerrainGrid();

    // 飞行到最佳观察位置
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 15000),
      orientation: {
        heading: Cesium.Math.toRadians(45),
        pitch: Cesium.Math.toRadians(-30),
        roll: 0.0
      },
      duration: 3
    });

    // 启用视觉效果
    viewer.scene.globe.enableLighting = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.fog.density = 0.0002;

    updateEntityCount();
    showNotification('🏔️ 地形演示创建完成！', 'success');

  } catch (error) {
    console.error('地形演示创建失败:', error);
    showNotification('❌ 地形演示创建失败', 'error');
  }
}

// 创建演示山峰
function createDemoMountains() {
  const demoMountains = [
    { name: '黄杨山', lng: 113.4167, lat: 22.1833, height: 2000, color: Cesium.Color.BROWN },
    { name: '板樟山', lng: 113.3833, lat: 22.2500, height: 1800, color: Cesium.Color.DARKGREEN },
    { name: '凤凰山', lng: 113.5333, lat: 22.2167, height: 1500, color: Cesium.Color.GREEN },
    { name: '将军山', lng: 113.4500, lat: 22.1667, height: 1200, color: Cesium.Color.OLIVE },
    { name: '狮山', lng: 113.5167, lat: 22.2833, height: 1000, color: Cesium.Color.DARKKHAKI }
  ];

  demoMountains.forEach(mountain => {
    const mountainEntity = viewer.entities.add({
      name: mountain.name,
      position: Cesium.Cartesian3.fromDegrees(
        mountain.lng,
        mountain.lat,
        mountain.height / 2
      ),
      cylinder: {
        length: mountain.height,
        topRadius: 100,
        bottomRadius: 800,
        material: mountain.color.withAlpha(0.8),
        outline: true,
        outlineColor: mountain.color.withAlpha(1.0)
      },
      label: {
        text: `${mountain.name}\n${Math.floor(mountain.height/3)}m`,
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -mountain.height / 3),
        disableDepthTestDistance: Number.POSITIVE_INFINITY
      }
    });

    terrainEntities.push(mountainEntity);
  });
}

// 创建演示地形网格
function createDemoTerrainGrid() {
  const centerLat = 22.2707;
  const centerLng = 113.5767;
  const gridSize = 15;
  const step = 0.02;

  for (let i = -gridSize; i <= gridSize; i++) {
    for (let j = -gridSize; j <= gridSize; j++) {
      const lat = centerLat + i * step;
      const lng = centerLng + j * step;

      // 计算距离中心的距离
      const distance = Math.sqrt(i * i + j * j);

      // 创建地形起伏
      let elevation = 50;
      if (distance < 5) {
        elevation += (5 - distance) * 100; // 中心区域较高
      }

      // 添加噪声
      elevation += Math.sin(i * 0.5) * Math.cos(j * 0.5) * 50;
      elevation = Math.max(20, elevation);

      if (elevation > 80) { // 只显示较高的地形点
        const terrainPoint = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(lng, lat, elevation / 2),
          box: {
            dimensions: new Cesium.Cartesian3(800, 800, elevation),
            material: getTerrainColor(elevation),
            outline: false
          }
        });

        terrainEntities.push(terrainPoint);
      }
    }
  }
}

// 获取地形颜色
function getTerrainColor(elevation) {
  if (elevation > 200) {
    return Cesium.Color.BROWN.withAlpha(0.7);
  } else if (elevation > 150) {
    return Cesium.Color.DARKGREEN.withAlpha(0.7);
  } else if (elevation > 100) {
    return Cesium.Color.GREEN.withAlpha(0.7);
  } else {
    return Cesium.Color.LIGHTGREEN.withAlpha(0.7);
  }
}

// 飞行到区域
function flyToArea(area) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      10000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-60),
      roll: 0.0
    },
    duration: 2
  });

  showNotification(`🎯 飞行到${area.name}`, 'info');
}

// 切换地形
async function switchTerrain() {
  if (!viewer) return;

  try {
    terrainStatus.value = { text: '切换中...', class: 'loading' };

    switch (currentTerrain.value) {
      case 'flat':
        // 平面地形（椭球体）
        viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
        terrainStatus.value = { text: '平面地形', class: 'online' };
        showNotification('🌊 已切换到平面地形', 'info');
        break;

      case 'cesium':
        // Cesium World Terrain（需要网络）
        try {
          viewer.terrainProvider = await Cesium.createWorldTerrainAsync({
            requestWaterMask: true,
            requestVertexNormals: true
          });
          terrainStatus.value = { text: 'Cesium世界地形', class: 'online' };
          showNotification('🌍 已切换到Cesium世界地形', 'success');
        } catch (error) {
          console.warn('Cesium世界地形加载失败，使用备用方案:', error);
          viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
          terrainStatus.value = { text: '平面地形(备用)', class: 'warning' };
          showNotification('⚠️ 世界地形加载失败，已切换到平面地形', 'warning');
        }
        break;

      case 'custom':
        // 自定义珠海DEM地形
        await loadCustomZhuhaiTerrain();
        break;
    }

    // 启用地形深度测试以获得更好的视觉效果
    viewer.scene.globe.depthTestAgainstTerrain = currentTerrain.value !== 'flat';

  } catch (error) {
    console.error('地形切换失败:', error);
    terrainStatus.value = { text: '切换失败', class: 'error' };
    showNotification('❌ 地形切换失败', 'error');
  }
}

// 加载自定义珠海DEM地形
async function loadCustomZhuhaiTerrain() {
  try {
    terrainStatus.value = { text: '加载DEM数据...', class: 'loading' };

    // 创建珠海地形的3D实体来模拟地形效果
    await createZhuhai3DTopography();

    // 使用基础地形但添加视觉效果
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();

    terrainStatus.value = { text: '珠海3D地形', class: 'online' };
    showNotification('🏔️ 已加载珠海3D地形模型', 'success');

    // 启用地形相关的视觉效果
    viewer.scene.globe.enableLighting = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.fog.density = 0.0001;

    console.log('✅ 珠海3D地形加载成功');

  } catch (error) {
    console.error('珠海地形加载失败:', error);
    // 降级到平面地形
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
    currentTerrain.value = 'flat';
    terrainStatus.value = { text: '平面地形(降级)', class: 'warning' };
    showNotification('⚠️ 地形加载失败，已降级到平面地形', 'warning');
  }
}

// 创建珠海3D地形模型
async function createZhuhai3DTopography() {
  try {
    console.log('开始创建珠海3D地形模型...');

    // 清除之前的地形实体
    clearTerrainEntities();

    // 创建主要山峰的3D模型
    await createMountain3DModels();

    // 创建地形网格
    await createTerrainMesh();

    // 创建海岸线
    await createCoastline3D();

    console.log('✅ 珠海3D地形模型创建完成');

  } catch (error) {
    console.error('3D地形模型创建失败:', error);
    throw error;
  }
}

// 创建山峰3D模型
async function createMountain3DModels() {
  const mountains = ZHUHAI_ELEVATIONS.mountains;

  mountains.forEach(mountain => {
    // 创建山峰的圆锥体模型
    const mountainEntity = viewer.entities.add({
      name: mountain.name,
      position: Cesium.Cartesian3.fromDegrees(
        mountain.lng,
        mountain.lat,
        mountain.height / 2 // 圆锥体中心高度
      ),
      cylinder: {
        length: mountain.height,
        topRadius: 50, // 山顶半径
        bottomRadius: mountain.radius / 10, // 山底半径
        material: getMountainColor(mountain.height),
        outline: true,
        outlineColor: Cesium.Color.DARKGREEN.withAlpha(0.8)
      },
      label: {
        text: `${mountain.name}\n${mountain.height}m`,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -mountain.height / 4),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        scale: 0.8
      }
    });

    // 添加到地形实体列表
    terrainEntities.push(mountainEntity);
  });
}

// 创建地形网格
async function createTerrainMesh() {
  // 创建珠海地区的地形网格
  const bounds = ZHUHAI_BOUNDS;
  const gridSize = 20; // 20x20网格

  const latStep = (bounds.north - bounds.south) / gridSize;
  const lngStep = (bounds.east - bounds.west) / gridSize;

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const lat = bounds.south + i * latStep;
      const lng = bounds.west + j * lngStep;

      // 计算该点的高程
      const elevation = calculateSimpleElevation(lat, lng);

      if (elevation > 20) { // 只显示较高的地形
        const terrainPoint = viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(lng, lat, elevation / 2),
          box: {
            dimensions: new Cesium.Cartesian3(500, 500, elevation),
            material: getElevationColor(elevation),
            outline: false
          }
        });

        terrainEntities.push(terrainPoint);
      }
    }
  }
}

// 创建3D海岸线
async function createCoastline3D() {
  const coastline = ZHUHAI_ELEVATIONS.coastline;

  // 创建海岸线路径
  const positions = coastline.map(point =>
    Cesium.Cartesian3.fromDegrees(point.lng, point.lat, 5)
  );

  const coastlineEntity = viewer.entities.add({
    name: '珠海海岸线',
    polyline: {
      positions: positions,
      width: 8,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.CYAN
      }),
      clampToGround: false
    }
  });

  terrainEntities.push(coastlineEntity);
}

// 计算简化的高程
function calculateSimpleElevation(lat, lng) {
  let elevation = 5; // 基础海拔

  // 检查山峰影响
  ZHUHAI_ELEVATIONS.mountains.forEach(mountain => {
    const distance = calculateDistance(lat, lng, mountain.lat, mountain.lng);
    if (distance < mountain.radius) {
      const influence = Math.pow(1 - (distance / mountain.radius), 2);
      elevation += mountain.height * influence;
    }
  });

  return elevation;
}

// 获取山峰颜色
function getMountainColor(height) {
  if (height > 500) {
    return Cesium.Color.BROWN.withAlpha(0.8);
  } else if (height > 300) {
    return Cesium.Color.DARKGREEN.withAlpha(0.8);
  } else {
    return Cesium.Color.GREEN.withAlpha(0.8);
  }
}

// 获取高程颜色
function getElevationColor(elevation) {
  if (elevation > 100) {
    return Cesium.Color.BROWN.withAlpha(0.6);
  } else if (elevation > 50) {
    return Cesium.Color.DARKGREEN.withAlpha(0.6);
  } else {
    return Cesium.Color.GREEN.withAlpha(0.6);
  }
}

// 清除地形实体
function clearTerrainEntities() {
  terrainEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  terrainEntities = [];
}

// 计算两点间距离
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}



// 设置视图
function setView(viewType) {
  if (!viewer) return;

  let destination, orientation;

  switch (viewType) {
    case 'overview':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 50000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      };
      break;
    case 'aerial':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 20000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0
      };
      break;
    case 'ground':
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 1000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-15),
        roll: 0.0
      };
      break;
    case 'terrain':
      // 地形视图 - 低角度观察地形起伏
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 5000);
      orientation = {
        heading: Cesium.Math.toRadians(45),
        pitch: Cesium.Math.toRadians(-30),
        roll: 0.0
      };
      break;
    case 'earth':
      // 地球视图 - 显示地球表面和曲率
      destination = Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000);
      orientation = {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      };
      break;
  }

  viewer.camera.flyTo({
    destination,
    orientation,
    duration: 2
  });
}

// 获取预警高度
function getWarningHeight(level) {
  const heights = {
    'red': 800,
    'orange': 600,
    'yellow': 400,
    'blue': 300
  };
  return heights[level] || 300;
}

// 获取预警半径
function getWarningRadius(level, position) {
  const radii = {
    'red': { top: 400, bottom: 600 },
    'orange': { top: 300, bottom: 500 },
    'yellow': { top: 200, bottom: 400 },
    'blue': { top: 150, bottom: 300 }
  };
  return radii[level]?.[position] || 200;
}

// 获取预警颜色
function getWarningColor(level) {
  const colors = {
    'red': Cesium.Color.RED,
    'orange': Cesium.Color.ORANGE,
    'yellow': Cesium.Color.YELLOW,
    'blue': Cesium.Color.BLUE
  };
  return colors[level] || Cesium.Color.GRAY;
}

// 获取预警图标
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '冰雹': '🧊',
    '大雾': '🌫️',
    '高温': '🌡️',
    '台风': '🌀',
    '暴雪': '❄️',
    '雷暴': '⛈️'
  };
  return icons[type] || '⚠️';
}

// 获取预警级别文本
function getWarningLevelText(level) {
  const texts = {
    'red': '红色',
    'orange': '橙色',
    'yellow': '黄色',
    'blue': '蓝色'
  };
  return texts[level] || '未知';
}

// 显示通知
function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理CesiumJS系统资源...');

  try {
    // 清理定时器
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
      timeUpdateInterval = null;
    }

    if (weatherUpdateInterval) {
      clearInterval(weatherUpdateInterval);
      weatherUpdateInterval = null;
    }

    if (autoWarningInterval) {
      clearInterval(autoWarningInterval);
      autoWarningInterval = null;
    }

    // 清理系统定时器
    if (window.systemIntervals) {
      window.systemIntervals.forEach(interval => {
        clearInterval(interval);
      });
      window.systemIntervals = [];
    }

    // 清理Cesium资源
    if (viewer) {
      try {
        // 移除所有事件监听器
        if (viewer.scene && viewer.scene.renderError) {
          viewer.scene.renderError.removeEventListener();
        }

        // 清理实体
        if (viewer.entities) {
          viewer.entities.removeAll();
        }

        // 销毁Viewer
        viewer.destroy();
        viewer = null;
      } catch (error) {
        console.warn('Viewer清理失败:', error);
        viewer = null;
      }
    }

    // 重置状态
    warningEntities = [];
    terrainEntities = [];
    cesiumLoaded = false;

    console.log('✅ CesiumJS系统资源清理完成');
  } catch (error) {
    console.error('资源清理失败:', error);
  }
}
</script>

<style scoped>
.cesium-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: #000;
  overflow: hidden;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  text-align: center;
  color: #4CAF50;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #FFA500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.error {
  color: #FF6B6B;
}

.status-value.warning {
  color: #FFA500;
}

.realtime-data {
  margin-bottom: 20px;
}

.data-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  font-size: 10px;
}

.data-label {
  color: #ccc;
}

.data-value {
  font-weight: bold;
  color: #90EE90;
}

.warning-list {
  margin-bottom: 20px;
  max-height: 200px;
  overflow-y: auto;
}

.no-warnings {
  text-align: center;
  color: #90EE90;
  font-size: 11px;
  padding: 10px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 5px;
}

.warnings-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.warning-item {
  padding: 8px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid;
}

.warning-item.red {
  background: rgba(244, 67, 54, 0.1);
  border-left-color: #f44336;
}

.warning-item.orange {
  background: rgba(255, 152, 0, 0.1);
  border-left-color: #ff9800;
}

.warning-item.yellow {
  background: rgba(255, 235, 59, 0.1);
  border-left-color: #ffeb3b;
}

.warning-item.blue {
  background: rgba(33, 150, 243, 0.1);
  border-left-color: #2196f3;
}

.warning-item:hover {
  transform: translateX(5px);
  background-opacity: 0.2;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 4px;
}

.warning-icon {
  font-size: 14px;
}

.warning-type {
  font-weight: bold;
  font-size: 11px;
  flex: 1;
}

.warning-level {
  font-size: 9px;
  padding: 2px 6px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.2);
}

.warning-details {
  font-size: 9px;
  color: #ccc;
  line-height: 1.3;
}

.warning-location,
.warning-time,
.warning-intensity {
  margin-bottom: 2px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin: 5px 0;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.demo {
  background: #9C27B0;
  color: white;
}

.control-btn.severe {
  background: #E91E63;
  color: white;
}

.control-btn.auto {
  background: #607D8B;
  color: white;
}

.control-btn.auto.active {
  background: #4CAF50;
  color: white;
}

.control-btn.export {
  background: #FF9800;
  color: white;
}

.control-btn.fix {
  background: #00BCD4;
  color: white;
}

.control-btn.earth {
  background: #4CAF50;
  color: white;
}

.control-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.terrain-control {
  margin-bottom: 20px;
}

.terrain-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 10px;
}

.terrain-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.terrain-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.terrain-option input[type="radio"] {
  margin-right: 8px;
  accent-color: #4CAF50;
}

.terrain-option span {
  font-size: 11px;
  color: #ccc;
}

.terrain-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  font-size: 10px;
}

.terrain-label {
  color: #ccc;
}

.terrain-value {
  font-weight: bold;
}

.terrain-value.loading {
  color: #FFA500;
}

.terrain-value.online {
  color: #90EE90;
}

.terrain-value.warning {
  color: #FFD700;
}

.terrain-value.error {
  color: #FF6B6B;
}

.view-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
}

.view-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 3px;
  background: #2196F3;
  color: white;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: #1976D2;
}

.zhuhai-info-panel {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 280px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.zhuhai-info-panel h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #4CAF50;
  text-align: center;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.area-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.area-icon {
  font-size: 16px;
  margin-right: 10px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.area-description {
  font-size: 10px;
  color: #ccc;
}

.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10001;
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  max-width: 400px;
  animation: slideIn 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification.success {
  background: rgba(76, 175, 80, 0.9);
}

.notification.error {
  background: rgba(244, 67, 54, 0.9);
}

.notification.info {
  background: rgba(33, 150, 243, 0.9);
}

.notification.warning {
  background: rgba(255, 152, 0, 0.9);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
</style>
