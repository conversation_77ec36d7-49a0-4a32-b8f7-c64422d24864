/**
 * 增强地形渲染系统
 * 提供更真实的地形可视化效果
 */

import * as THREE from 'three';
import { generateZhuhaiTerrain, ZHUHAI_LANDMARKS, ZHUHAI_COASTLINE } from './zhuhaiTerrain.js';

/**
 * 创建增强的珠海地形
 * @param {THREE.Scene} scene - Three.js场景
 * @param {object} options - 配置选项
 * @returns {object} 地形对象
 */
export function createEnhancedZhuhaiTerrain(scene, options = {}) {
  const {
    resolution = 300,        // 地形分辨率
    scale = 10000,          // 地形缩放
    enableWater = true,     // 启用水面效果
    enableVegetation = true, // 启用植被
    enableBuildings = true   // 启用建筑物
  } = options;

  const terrainGroup = new THREE.Group();
  
  // 生成地形数据
  const terrainData = generateZhuhaiTerrain(resolution, resolution, { scale });
  
  // 创建主地形
  const mainTerrain = createMainTerrain(terrainData, resolution, scale);
  terrainGroup.add(mainTerrain);
  
  // 添加水面
  if (enableWater) {
    const water = createWaterSurface(scale);
    terrainGroup.add(water);
  }
  
  // 添加植被
  if (enableVegetation) {
    const vegetation = createVegetation(terrainData, resolution, scale);
    terrainGroup.add(vegetation);
  }
  
  // 添加建筑物
  if (enableBuildings) {
    const buildings = createBuildings(scale);
    terrainGroup.add(buildings);
  }
  
  // 添加道路网络
  const roads = createRoadNetwork(scale);
  terrainGroup.add(roads);
  
  scene.add(terrainGroup);
  
  return {
    group: terrainGroup,
    mainTerrain,
    water: enableWater ? terrainGroup.children.find(child => child.userData.type === 'water') : null,
    vegetation: enableVegetation ? terrainGroup.children.find(child => child.userData.type === 'vegetation') : null,
    buildings: enableBuildings ? terrainGroup.children.find(child => child.userData.type === 'buildings') : null,
    roads: terrainGroup.children.find(child => child.userData.type === 'roads')
  };
}

/**
 * 创建主地形网格
 */
function createMainTerrain(terrainData, resolution, scale) {
  const geometry = new THREE.PlaneGeometry(scale, scale, resolution - 1, resolution - 1);
  const vertices = geometry.attributes.position.array;
  
  // 应用高度数据
  for (let i = 0; i < terrainData.length; i++) {
    vertices[i * 3 + 2] = terrainData[i] * 600; // Z轴高度
  }
  
  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();
  
  // 创建纹理
  const texture = createTerrainTexture(terrainData, resolution);
  const normalMap = createNormalMap(terrainData, resolution);
  
  // 创建材质
  const material = new THREE.MeshPhongMaterial({
    map: texture,
    normalMap: normalMap,
    shininess: 30,
    transparent: false
  });
  
  const mesh = new THREE.Mesh(geometry, material);
  mesh.rotation.x = -Math.PI / 2;
  mesh.receiveShadow = true;
  mesh.castShadow = false;
  mesh.userData.type = 'terrain';
  
  return mesh;
}

/**
 * 创建地形纹理
 */
function createTerrainTexture(terrainData, resolution) {
  const canvas = document.createElement('canvas');
  canvas.width = resolution;
  canvas.height = resolution;
  const ctx = canvas.getContext('2d');
  
  const imageData = ctx.createImageData(resolution, resolution);
  
  for (let i = 0; i < terrainData.length; i++) {
    const height = terrainData[i];
    const color = getEnhancedTerrainColor(height);
    
    const pixelIndex = i * 4;
    imageData.data[pixelIndex] = color.r;
    imageData.data[pixelIndex + 1] = color.g;
    imageData.data[pixelIndex + 2] = color.b;
    imageData.data[pixelIndex + 3] = 255;
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.ClampToEdgeWrapping;
  texture.wrapT = THREE.ClampToEdgeWrapping;
  texture.minFilter = THREE.LinearFilter;
  texture.magFilter = THREE.LinearFilter;
  
  return texture;
}

/**
 * 获取增强的地形颜色
 */
function getEnhancedTerrainColor(height) {
  if (height < 0.01) {
    // 深海 - 深蓝色
    return { r: 25, g: 25, b: 112 };
  } else if (height < 0.02) {
    // 浅海 - 蓝色
    return { r: 64, g: 164, b: 223 };
  } else if (height < 0.05) {
    // 海滩 - 沙色
    return { r: 255, g: 248, b: 220 };
  } else if (height < 0.1) {
    // 海岸平原 - 浅绿色
    return { r: 152, g: 251, b: 152 };
  } else if (height < 0.3) {
    // 平原 - 绿色
    return { r: 34, g: 139, b: 34 };
  } else if (height < 0.5) {
    // 丘陵 - 黄绿色
    return { r: 107, g: 142, b: 35 };
  } else if (height < 0.7) {
    // 山地 - 棕色
    return { r: 139, g: 69, b: 19 };
  } else if (height < 0.9) {
    // 高山 - 灰棕色
    return { r: 160, g: 82, b: 45 };
  } else {
    // 山顶 - 灰白色
    return { r: 220, g: 220, b: 220 };
  }
}

/**
 * 创建法线贴图
 */
function createNormalMap(terrainData, resolution) {
  const canvas = document.createElement('canvas');
  canvas.width = resolution;
  canvas.height = resolution;
  const ctx = canvas.getContext('2d');
  
  const imageData = ctx.createImageData(resolution, resolution);
  
  for (let y = 0; y < resolution; y++) {
    for (let x = 0; x < resolution; x++) {
      const index = y * resolution + x;
      
      // 计算梯度
      const left = x > 0 ? terrainData[index - 1] : terrainData[index];
      const right = x < resolution - 1 ? terrainData[index + 1] : terrainData[index];
      const up = y > 0 ? terrainData[index - resolution] : terrainData[index];
      const down = y < resolution - 1 ? terrainData[index + resolution] : terrainData[index];
      
      const dx = (right - left) * 255;
      const dy = (down - up) * 255;
      
      // 转换为法线向量
      const normal = new THREE.Vector3(-dx, -dy, 1).normalize();
      
      const pixelIndex = index * 4;
      imageData.data[pixelIndex] = (normal.x + 1) * 127.5;     // R
      imageData.data[pixelIndex + 1] = (normal.y + 1) * 127.5; // G
      imageData.data[pixelIndex + 2] = (normal.z + 1) * 127.5; // B
      imageData.data[pixelIndex + 3] = 255;                    // A
    }
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.ClampToEdgeWrapping;
  texture.wrapT = THREE.ClampToEdgeWrapping;
  
  return texture;
}

/**
 * 创建水面效果
 */
function createWaterSurface(scale) {
  const waterGroup = new THREE.Group();
  waterGroup.userData.type = 'water';
  
  // 主要水面
  const waterGeometry = new THREE.PlaneGeometry(scale * 1.2, scale * 1.2, 100, 100);
  const waterMaterial = new THREE.MeshPhongMaterial({
    color: 0x006994,
    transparent: true,
    opacity: 0.8,
    shininess: 100,
    specular: 0x111111
  });
  
  const waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
  waterMesh.rotation.x = -Math.PI / 2;
  waterMesh.position.y = -1;
  waterGroup.add(waterMesh);
  
  // 添加水波动画
  waterMesh.userData.animate = (time) => {
    const vertices = waterGeometry.attributes.position.array;
    for (let i = 0; i < vertices.length; i += 3) {
      const x = vertices[i];
      const z = vertices[i + 1];
      vertices[i + 2] = Math.sin(time * 0.001 + x * 0.001) * 2 + 
                        Math.cos(time * 0.0015 + z * 0.001) * 1.5;
    }
    waterGeometry.attributes.position.needsUpdate = true;
  };
  
  return waterGroup;
}

/**
 * 创建植被
 */
function createVegetation(terrainData, resolution, scale) {
  const vegetationGroup = new THREE.Group();
  vegetationGroup.userData.type = 'vegetation';
  
  // 树木实例化几何体
  const treeGeometry = new THREE.ConeGeometry(5, 20, 8);
  const treeMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
  
  // 草地实例化几何体
  const grassGeometry = new THREE.PlaneGeometry(2, 3);
  const grassMaterial = new THREE.MeshLambertMaterial({ 
    color: 0x90EE90,
    transparent: true,
    opacity: 0.8
  });
  
  // 在合适的地形上放置植被
  for (let i = 0; i < 1000; i++) {
    const x = (Math.random() - 0.5) * scale * 0.8;
    const z = (Math.random() - 0.5) * scale * 0.8;
    
    // 获取该位置的地形高度
    const gridX = Math.floor((x / scale + 0.5) * resolution);
    const gridZ = Math.floor((z / scale + 0.5) * resolution);
    const heightIndex = gridZ * resolution + gridX;
    
    if (heightIndex >= 0 && heightIndex < terrainData.length) {
      const height = terrainData[heightIndex] * 600;
      
      // 在合适的高度范围内放置植被
      if (height > 5 && height < 300) {
        if (Math.random() < 0.3) {
          // 放置树木
          const tree = new THREE.Mesh(treeGeometry, treeMaterial);
          tree.position.set(x, height + 10, z);
          tree.castShadow = true;
          vegetationGroup.add(tree);
        } else {
          // 放置草地
          const grass = new THREE.Mesh(grassGeometry, grassMaterial);
          grass.position.set(x, height + 1, z);
          grass.rotation.x = -Math.PI / 2;
          grass.rotation.z = Math.random() * Math.PI;
          vegetationGroup.add(grass);
        }
      }
    }
  }
  
  return vegetationGroup;
}

/**
 * 创建建筑物
 */
function createBuildings(scale) {
  const buildingsGroup = new THREE.Group();
  buildingsGroup.userData.type = 'buildings';
  
  // 在主要城区添加建筑物
  const cityAreas = [
    { x: 0, z: 0, density: 0.8, name: '香洲区' },
    { x: -2000, z: -1000, density: 0.6, name: '金湾区' },
    { x: -3000, z: 1000, density: 0.4, name: '斗门区' },
    { x: 1000, z: -2000, density: 0.7, name: '横琴新区' }
  ];
  
  cityAreas.forEach(area => {
    for (let i = 0; i < area.density * 100; i++) {
      const x = area.x + (Math.random() - 0.5) * 1000;
      const z = area.z + (Math.random() - 0.5) * 1000;
      
      // 创建建筑物
      const width = 10 + Math.random() * 20;
      const depth = 10 + Math.random() * 20;
      const height = 20 + Math.random() * 100;
      
      const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
      const buildingMaterial = new THREE.MeshLambertMaterial({
        color: new THREE.Color().setHSL(0.1, 0.2, 0.5 + Math.random() * 0.3)
      });
      
      const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
      building.position.set(x, height / 2 + 5, z);
      building.castShadow = true;
      building.receiveShadow = true;
      
      buildingsGroup.add(building);
    }
  });
  
  return buildingsGroup;
}

/**
 * 创建道路网络
 */
function createRoadNetwork(scale) {
  const roadsGroup = new THREE.Group();
  roadsGroup.userData.type = 'roads';
  
  const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
  
  // 主要道路
  const mainRoads = [
    { start: { x: -scale/2, z: 0 }, end: { x: scale/2, z: 0 }, width: 20 },
    { start: { x: 0, z: -scale/2 }, end: { x: 0, z: scale/2 }, width: 20 },
    { start: { x: -scale/3, z: -scale/3 }, end: { x: scale/3, z: scale/3 }, width: 15 },
    { start: { x: -scale/3, z: scale/3 }, end: { x: scale/3, z: -scale/3 }, width: 15 }
  ];
  
  mainRoads.forEach(road => {
    const length = Math.sqrt(
      Math.pow(road.end.x - road.start.x, 2) + 
      Math.pow(road.end.z - road.start.z, 2)
    );
    
    const roadGeometry = new THREE.PlaneGeometry(length, road.width);
    const roadMesh = new THREE.Mesh(roadGeometry, roadMaterial);
    
    // 定位和旋转道路
    roadMesh.position.set(
      (road.start.x + road.end.x) / 2,
      2,
      (road.start.z + road.end.z) / 2
    );
    
    const angle = Math.atan2(road.end.z - road.start.z, road.end.x - road.start.x);
    roadMesh.rotation.x = -Math.PI / 2;
    roadMesh.rotation.z = angle;
    
    roadsGroup.add(roadMesh);
  });
  
  return roadsGroup;
}

/**
 * 更新水面动画
 */
export function updateWaterAnimation(terrainObject, time) {
  if (terrainObject.water) {
    const waterMesh = terrainObject.water.children[0];
    if (waterMesh && waterMesh.userData.animate) {
      waterMesh.userData.animate(time);
    }
  }
}
