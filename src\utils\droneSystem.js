/**
 * 无人机巡航系统
 * 包含无人机模型、路径规划、状态监控等功能
 */

import * as THREE from 'three';
import { ZHUHAI_LANDMARKS, latLngToWorldCoords, ZHUHAI_BOUNDS } from './zhuhaiTerrain.js';

// 无人机类型配置
export const DRONE_TYPES = {
  patrol: {
    name: '巡逻无人机',
    maxSpeed: 15,        // m/s
    maxAltitude: 500,    // m
    batteryLife: 3600,   // 秒
    windResistance: 12,  // m/s 最大抗风能力
    color: 0x00ff00,
    size: 2
  },
  weather: {
    name: '气象监测无人机',
    maxSpeed: 10,
    maxAltitude: 1000,
    batteryLife: 7200,
    windResistance: 15,
    color: 0x0088ff,
    size: 3
  },
  emergency: {
    name: '应急救援无人机',
    maxSpeed: 20,
    maxAltitude: 300,
    batteryLife: 1800,
    windResistance: 10,
    color: 0xff0000,
    size: 2.5
  }
};

// 预设巡航路径
export const PATROL_ROUTES = {
  coastal: {
    name: '海岸线巡逻',
    points: [
      { lat: 22.2769, lng: 113.5678, altitude: 100 }, // 香洲
      { lat: 22.2500, lng: 113.6000, altitude: 120 },
      { lat: 22.2000, lng: 113.6200, altitude: 150 },
      { lat: 22.1500, lng: 113.6000, altitude: 130 },
      { lat: 22.1000, lng: 113.5500, altitude: 110 },
      { lat: 22.2769, lng: 113.5678, altitude: 100 }
    ],
    type: 'loop',
    priority: 'normal'
  },
  airport: {
    name: '机场周边监控',
    points: [
      { lat: 22.0064, lng: 113.3761, altitude: 80 },  // 机场
      { lat: 22.0200, lng: 113.3900, altitude: 100 },
      { lat: 22.0300, lng: 113.3800, altitude: 120 },
      { lat: 22.0200, lng: 113.3600, altitude: 100 },
      { lat: 22.0064, lng: 113.3761, altitude: 80 }
    ],
    type: 'loop',
    priority: 'high'
  },
  mountain: {
    name: '山区气象监测',
    points: [
      { lat: 22.3167, lng: 113.4167, altitude: 500 }, // 凤凰山
      { lat: 22.4167, lng: 113.3833, altitude: 600 }, // 黄杨山
      { lat: 22.3500, lng: 113.3500, altitude: 400 },
      { lat: 22.3167, lng: 113.4167, altitude: 500 }
    ],
    type: 'loop',
    priority: 'normal'
  },
  bridge: {
    name: '港珠澳大桥监控',
    points: [
      { lat: 22.2133, lng: 113.5822, altitude: 150 }, // 大桥
      { lat: 22.2200, lng: 113.6000, altitude: 160 },
      { lat: 22.2000, lng: 113.6200, altitude: 170 },
      { lat: 22.1900, lng: 113.6000, altitude: 160 },
      { lat: 22.2133, lng: 113.5822, altitude: 150 }
    ],
    type: 'loop',
    priority: 'high'
  }
};

/**
 * 无人机类
 */
export class Drone {
  constructor(id, type, position, route) {
    this.id = id;
    this.type = DRONE_TYPES[type] || DRONE_TYPES.patrol;
    this.position = position;
    this.route = route;
    this.currentRouteIndex = 0;
    this.status = 'idle'; // idle, flying, returning, emergency, maintenance
    this.battery = 100; // 电池百分比
    this.speed = 0;
    this.altitude = position.y;
    this.heading = 0; // 航向角（弧度）
    this.lastUpdate = Date.now();
    
    // 创建3D模型
    this.mesh = this.createDroneMesh();
    this.trail = this.createTrail();
    
    // 状态数据
    this.flightTime = 0;
    this.totalDistance = 0;
    this.weatherConditions = null;
    this.warnings = [];
  }

  /**
   * 创建无人机3D模型
   */
  createDroneMesh() {
    const group = new THREE.Group();
    
    // 机身
    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.5, 1.5, 8);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: this.type.color });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    group.add(body);
    
    // 螺旋桨臂
    const armGeometry = new THREE.BoxGeometry(3, 0.1, 0.1);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    
    const arm1 = new THREE.Mesh(armGeometry, armMaterial);
    arm1.rotation.y = Math.PI / 4;
    group.add(arm1);
    
    const arm2 = new THREE.Mesh(armGeometry, armMaterial);
    arm2.rotation.y = -Math.PI / 4;
    group.add(arm2);
    
    // 螺旋桨
    const propGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.05, 16);
    const propMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x666666, 
      transparent: true, 
      opacity: 0.7 
    });
    
    const positions = [
      { x: 1.2, y: 0.3, z: 1.2 },
      { x: -1.2, y: 0.3, z: 1.2 },
      { x: 1.2, y: 0.3, z: -1.2 },
      { x: -1.2, y: 0.3, z: -1.2 }
    ];
    
    this.propellers = [];
    positions.forEach(pos => {
      const prop = new THREE.Mesh(propGeometry, propMaterial);
      prop.position.set(pos.x, pos.y, pos.z);
      group.add(prop);
      this.propellers.push(prop);
    });
    
    // 状态指示灯
    const lightGeometry = new THREE.SphereGeometry(0.1, 8, 8);
    const lightMaterial = new THREE.MeshBasicMaterial({ 
      color: this.getStatusColor(),
      emissive: this.getStatusColor(),
      emissiveIntensity: 0.5
    });
    const statusLight = new THREE.Mesh(lightGeometry, lightMaterial);
    statusLight.position.set(0, 1, 0);
    group.add(statusLight);
    this.statusLight = statusLight;
    
    // 设置初始位置和缩放
    group.position.set(this.position.x, this.position.y, this.position.z);
    group.scale.setScalar(this.type.size);
    
    return group;
  }

  /**
   * 创建飞行轨迹
   */
  createTrail() {
    const points = [];
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    const material = new THREE.LineBasicMaterial({ 
      color: this.type.color,
      transparent: true,
      opacity: 0.6
    });
    return new THREE.Line(geometry, material);
  }

  /**
   * 获取状态颜色
   */
  getStatusColor() {
    switch (this.status) {
      case 'flying': return 0x00ff00;
      case 'returning': return 0xffff00;
      case 'emergency': return 0xff0000;
      case 'maintenance': return 0x888888;
      default: return 0x0088ff;
    }
  }

  /**
   * 更新无人机状态
   */
  update(deltaTime, weatherData) {
    const now = Date.now();
    deltaTime = deltaTime || (now - this.lastUpdate) / 1000;
    this.lastUpdate = now;
    
    // 更新飞行时间
    if (this.status === 'flying') {
      this.flightTime += deltaTime;
    }
    
    // 更新电池
    this.updateBattery(deltaTime);
    
    // 更新天气条件
    this.updateWeatherConditions(weatherData);
    
    // 检查预警条件
    this.checkWarnings();
    
    // 更新位置
    this.updatePosition(deltaTime);
    
    // 更新3D模型
    this.updateMesh();
    
    // 更新轨迹
    this.updateTrail();
  }

  /**
   * 更新电池状态
   */
  updateBattery(deltaTime) {
    if (this.status === 'flying') {
      const consumption = this.speed > 0 ? 0.01 : 0.005; // 飞行时消耗更快
      this.battery = Math.max(0, this.battery - consumption * deltaTime);
      
      // 低电量警告
      if (this.battery < 20 && this.status !== 'returning') {
        this.status = 'returning';
        this.addWarning('低电量警告', 'warning');
      }
    }
  }

  /**
   * 更新天气条件
   */
  updateWeatherConditions(weatherData) {
    if (!weatherData) return;
    
    // 找到最近的天气数据点
    let nearestWeather = null;
    let minDistance = Infinity;
    
    weatherData.wind?.forEach(point => {
      const distance = Math.sqrt(
        Math.pow(point.x - this.position.x, 2) +
        Math.pow(point.z - this.position.z, 2)
      );
      if (distance < minDistance) {
        minDistance = distance;
        nearestWeather = point;
      }
    });
    
    this.weatherConditions = nearestWeather;
  }

  /**
   * 检查预警条件
   */
  checkWarnings() {
    this.warnings = [];
    
    // 检查风速
    if (this.weatherConditions && this.weatherConditions.speed > this.type.windResistance) {
      this.addWarning(`风速过大: ${this.weatherConditions.speed.toFixed(1)}m/s`, 'danger');
      if (this.status === 'flying') {
        this.status = 'emergency';
      }
    }
    
    // 检查电池
    if (this.battery < 10) {
      this.addWarning('电池严重不足', 'danger');
      this.status = 'emergency';
    }
    
    // 检查高度
    if (this.altitude > this.type.maxAltitude) {
      this.addWarning('超出最大飞行高度', 'warning');
    }
  }

  /**
   * 添加警告
   */
  addWarning(message, level) {
    this.warnings.push({
      message,
      level,
      time: new Date(),
      id: `${this.id}_${Date.now()}`
    });
  }

  /**
   * 更新位置
   */
  updatePosition(deltaTime) {
    if (this.status !== 'flying' || !this.route) return;
    
    const currentTarget = this.route.points[this.currentRouteIndex];
    if (!currentTarget) return;
    
    // 转换目标坐标
    const targetWorld = latLngToWorldCoords(
      currentTarget.lat, 
      currentTarget.lng, 
      ZHUHAI_BOUNDS, 
      10000
    );
    const targetPos = {
      x: targetWorld.x,
      y: currentTarget.altitude,
      z: targetWorld.z
    };
    
    // 计算到目标的距离
    const distance = Math.sqrt(
      Math.pow(targetPos.x - this.position.x, 2) +
      Math.pow(targetPos.y - this.position.y, 2) +
      Math.pow(targetPos.z - this.position.z, 2)
    );
    
    // 如果接近目标，切换到下一个点
    if (distance < 10) {
      this.currentRouteIndex = (this.currentRouteIndex + 1) % this.route.points.length;
      return;
    }
    
    // 计算移动方向
    const direction = {
      x: (targetPos.x - this.position.x) / distance,
      y: (targetPos.y - this.position.y) / distance,
      z: (targetPos.z - this.position.z) / distance
    };
    
    // 计算速度（考虑天气影响）
    let currentSpeed = this.type.maxSpeed;
    if (this.weatherConditions) {
      const windEffect = Math.min(this.weatherConditions.speed / this.type.windResistance, 0.5);
      currentSpeed *= (1 - windEffect);
    }
    
    this.speed = currentSpeed;
    
    // 更新位置
    this.position.x += direction.x * currentSpeed * deltaTime;
    this.position.y += direction.y * currentSpeed * deltaTime;
    this.position.z += direction.z * currentSpeed * deltaTime;
    
    this.altitude = this.position.y;
    
    // 更新航向
    this.heading = Math.atan2(direction.z, direction.x);
    
    // 更新总距离
    this.totalDistance += currentSpeed * deltaTime;
  }

  /**
   * 更新3D模型
   */
  updateMesh() {
    if (!this.mesh) return;
    
    // 更新位置
    this.mesh.position.set(this.position.x, this.position.y, this.position.z);
    
    // 更新旋转
    this.mesh.rotation.y = this.heading;
    
    // 旋转螺旋桨
    if (this.propellers && this.status === 'flying') {
      this.propellers.forEach(prop => {
        prop.rotation.y += 0.5;
      });
    }
    
    // 更新状态灯颜色
    if (this.statusLight) {
      const color = this.getStatusColor();
      this.statusLight.material.color.setHex(color);
      this.statusLight.material.emissive.setHex(color);
    }
  }

  /**
   * 更新飞行轨迹
   */
  updateTrail() {
    if (!this.trail) return;
    
    const positions = this.trail.geometry.attributes.position;
    if (!positions) {
      // 初始化轨迹点
      const points = [new THREE.Vector3(this.position.x, this.position.y, this.position.z)];
      this.trail.geometry.setFromPoints(points);
      this.trailPoints = points;
    } else {
      // 添加新点
      this.trailPoints.push(new THREE.Vector3(this.position.x, this.position.y, this.position.z));
      
      // 限制轨迹长度
      if (this.trailPoints.length > 100) {
        this.trailPoints.shift();
      }
      
      this.trail.geometry.setFromPoints(this.trailPoints);
    }
  }

  /**
   * 开始巡航
   */
  startPatrol() {
    this.status = 'flying';
    this.currentRouteIndex = 0;
  }

  /**
   * 返回基地
   */
  returnToBase() {
    this.status = 'returning';
    // 这里可以设置返回基地的逻辑
  }

  /**
   * 紧急降落
   */
  emergencyLanding() {
    this.status = 'emergency';
    this.speed = 0;
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      id: this.id,
      type: this.type.name,
      status: this.status,
      battery: this.battery,
      altitude: this.altitude,
      speed: this.speed,
      flightTime: this.flightTime,
      totalDistance: this.totalDistance,
      warnings: this.warnings,
      weatherConditions: this.weatherConditions,
      position: this.position
    };
  }
}

/**
 * 无人机管理器
 */
export class DroneManager {
  constructor(scene) {
    this.scene = scene;
    this.drones = new Map();
    this.droneGroup = new THREE.Group();
    this.scene.add(this.droneGroup);
  }

  /**
   * 添加无人机
   */
  addDrone(id, type, startPosition, route) {
    const drone = new Drone(id, type, startPosition, PATROL_ROUTES[route]);
    this.drones.set(id, drone);
    
    this.droneGroup.add(drone.mesh);
    this.droneGroup.add(drone.trail);
    
    return drone;
  }

  /**
   * 移除无人机
   */
  removeDrone(id) {
    const drone = this.drones.get(id);
    if (drone) {
      this.droneGroup.remove(drone.mesh);
      this.droneGroup.remove(drone.trail);
      this.drones.delete(id);
    }
  }

  /**
   * 更新所有无人机
   */
  update(deltaTime, weatherData) {
    this.drones.forEach(drone => {
      drone.update(deltaTime, weatherData);
    });
  }

  /**
   * 获取所有无人机状态
   */
  getAllStatus() {
    const status = [];
    this.drones.forEach(drone => {
      status.push(drone.getStatus());
    });
    return status;
  }

  /**
   * 启动所有无人机巡航
   */
  startAllPatrols() {
    this.drones.forEach(drone => {
      drone.startPatrol();
    });
  }

  /**
   * 召回所有无人机
   */
  recallAllDrones() {
    this.drones.forEach(drone => {
      drone.returnToBase();
    });
  }

  /**
   * 紧急停止所有无人机
   */
  emergencyStopAll() {
    this.drones.forEach(drone => {
      drone.emergencyLanding();
    });
  }
}
