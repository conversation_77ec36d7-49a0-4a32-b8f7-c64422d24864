<template>
  <div class="test-cesium">
    <div ref="cesiumContainer" class="cesium-container"></div>
    <div class="status-panel">
      <h3>🧪 CesiumJS 测试</h3>
      <div class="status">{{ status }}</div>
      <button @click="initTest" class="test-btn">🔄 重新测试</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import * as Cesium from 'cesium';

const cesiumContainer = ref(null);
const status = ref('准备测试...');
let viewer = null;

onMounted(async () => {
  await initTest();
});

async function initTest() {
  try {
    status.value = '🔄 开始测试...';
    await nextTick();

    // 检查容器
    if (!cesiumContainer.value) {
      throw new Error('容器未找到');
    }

    status.value = '📦 检查Cesium模块...';
    console.log('Cesium对象:', Cesium);
    console.log('Cesium.Viewer:', Cesium.Viewer);

    if (!Cesium) {
      throw new Error('Cesium模块未加载');
    }

    if (!Cesium.Viewer) {
      throw new Error('Cesium.Viewer未找到');
    }

    status.value = '🌍 创建地球...';

    // 清理旧的viewer
    if (viewer) {
      try {
        viewer.destroy();
      } catch (e) {
        console.warn('清理旧viewer失败:', e);
      }
      viewer = null;
    }

    // 设置基本的Cesium配置
    window.CESIUM_BASE_URL = '/node_modules/cesium/Build/Cesium/';

    // 创建最简单的viewer
    viewer = new Cesium.Viewer(cesiumContainer.value);

    status.value = '🎯 设置视图...';

    // 等待viewer完全初始化
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 设置视图到珠海
    if (viewer && viewer.camera) {
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 1000000),
        orientation: {
          heading: 0,
          pitch: Cesium.Math.toRadians(-45),
          roll: 0
        }
      });
    }

    status.value = '📍 添加标记...';

    // 添加一个简单的标记
    if (viewer && viewer.entities) {
      viewer.entities.add({
        name: '珠海',
        position: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 0),
        point: {
          pixelSize: 20,
          color: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2
        },
        label: {
          text: '珠海市',
          font: '14pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -50)
        }
      });
    }

    status.value = '✅ 测试成功！CesiumJS正常工作';

  } catch (error) {
    console.error('测试失败:', error);
    console.error('错误堆栈:', error.stack);
    status.value = `❌ 测试失败: ${error.message}`;
  }
}
</script>

<style scoped>
.test-cesium {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.status-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.status-panel h3 {
  margin: 0 0 15px 0;
  color: #4CAF50;
  text-align: center;
}

.status {
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.4;
}

.test-btn {
  width: 100%;
  padding: 10px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
}

.test-btn:hover {
  background: #45a049;
}
</style>
