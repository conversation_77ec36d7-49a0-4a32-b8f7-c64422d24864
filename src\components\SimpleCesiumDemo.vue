<template>
  <div class="cesium-demo">
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <div class="info-panel">
      <h3>🌍 珠海市三维气象预警系统</h3>
      <p>基于CesiumJS的三维地球展示</p>
      
      <div class="controls">
        <button @click="flyToZhuhai" class="btn">飞行到珠海</button>
        <button @click="addWeatherPoint" class="btn">添加预警点</button>
        <button @click="toggleBuildings" class="btn">切换建筑</button>
      </div>
      
      <div class="status">
        <p>状态: {{ status }}</p>
        <p>预警点: {{ weatherPoints.length }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as Cesium from 'cesium';

// 响应式数据
const cesiumContainer = ref(null);
const status = ref('初始化中...');
const weatherPoints = ref([]);

// Cesium变量
let viewer = null;
let buildingsEnabled = true;

// 珠海坐标
const ZHUHAI_POSITION = {
  longitude: 113.5767,
  latitude: 22.2711,
  height: 30000
};

onMounted(() => {
  initCesium();
});

onUnmounted(() => {
  if (viewer) {
    viewer.destroy();
  }
});

function initCesium() {
  try {
    // 设置访问令牌
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
    
    // 创建Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false
    });

    // 飞行到珠海
    flyToZhuhai();
    
    // 添加建筑物
    viewer.scene.primitives.add(Cesium.createOsmBuildings());
    
    status.value = '系统就绪';
    
    console.log('Cesium初始化成功');
  } catch (error) {
    console.error('Cesium初始化失败:', error);
    status.value = '初始化失败';
  }
}

function flyToZhuhai() {
  if (!viewer) return;
  
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      ZHUHAI_POSITION.longitude,
      ZHUHAI_POSITION.latitude,
      ZHUHAI_POSITION.height
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3.0
  });
}

function addWeatherPoint() {
  if (!viewer) return;
  
  // 在珠海周围随机添加预警点
  const longitude = ZHUHAI_POSITION.longitude + (Math.random() - 0.5) * 0.2;
  const latitude = ZHUHAI_POSITION.latitude + (Math.random() - 0.5) * 0.2;
  const height = 1000 + Math.random() * 2000;
  
  const colors = [
    Cesium.Color.RED,
    Cesium.Color.ORANGE,
    Cesium.Color.YELLOW,
    Cesium.Color.BLUE
  ];
  
  const color = colors[Math.floor(Math.random() * colors.length)];
  
  const entity = viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
    ellipsoid: {
      radii: new Cesium.Cartesian3(1000, 1000, 1500),
      material: color.withAlpha(0.7),
      outline: true,
      outlineColor: color
    },
    label: {
      text: '气象预警',
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -50)
    }
  });
  
  weatherPoints.value.push({
    id: weatherPoints.value.length,
    longitude,
    latitude,
    height,
    entity
  });
}

function toggleBuildings() {
  if (!viewer) return;
  
  const primitives = viewer.scene.primitives;
  const osmBuildings = primitives._primitives.find(p => p._url && p._url.includes('buildings'));
  
  if (buildingsEnabled && osmBuildings) {
    primitives.remove(osmBuildings);
    buildingsEnabled = false;
  } else if (!buildingsEnabled) {
    primitives.add(Cesium.createOsmBuildings());
    buildingsEnabled = true;
  }
}
</script>

<style scoped>
.cesium-demo {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.info-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  min-width: 250px;
}

.info-panel h3 {
  margin: 0 0 10px 0;
  color: #FFD700;
  font-size: 16px;
}

.info-panel p {
  margin: 0 0 15px 0;
  font-size: 12px;
  color: #ccc;
}

.controls {
  margin-bottom: 15px;
}

.btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  display: block;
  width: 100%;
  margin-bottom: 5px;
}

.btn:hover {
  background: #45a049;
}

.status p {
  margin: 5px 0;
  font-size: 11px;
  color: #90EE90;
}
</style>
