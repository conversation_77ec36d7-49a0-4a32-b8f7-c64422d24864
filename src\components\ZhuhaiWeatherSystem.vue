<template>
  <div class="zhuhai-weather-system">
    <!-- Cesium地图容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 预警系统控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市低空三维空域动态气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">地图状态:</span>
          <span class="status-value" :class="mapStatus.class">{{ mapStatus.text }}</span>
        </div>
      </div>
      
      <!-- 实时预警信息 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 5)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="flyToWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          ➕ 生成新预警
        </button>
        <button @click="generateSevereWarning" class="control-btn severe" :disabled="!isSystemReady">
          🚨 生成严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }" :disabled="!isSystemReady">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更新' }}
        </button>
        <button @click="exportWarningData" class="control-btn export" :disabled="activeWarnings.length === 0">
          📊 导出预警数据
        </button>
      </div>

      <!-- 图层控制 -->
      <div class="layer-control">
        <h4>📊 图层控制</h4>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.buildings" @change="toggleBuildings" :disabled="!isSystemReady">
            🏢 建筑物
          </label>
        </div>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.weather" @change="toggleWeather">
            ☁️ 气象预警
          </label>
        </div>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.airspace" @change="toggleAirspace">
            ✈️ 低空空域
          </label>
        </div>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.labels" @change="toggleLabels">
            🏷️ 区域标签
          </label>
        </div>
      </div>
    </div>

    <!-- 珠海区域信息面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions-panel">
      <h4>⚡ 快速操作</h4>
      <div class="quick-actions-grid">
        <button @click="flyToOverview" class="quick-btn overview" :disabled="!isSystemReady">
          🌍 总览
        </button>
        <button @click="focusOnSevereWarnings" class="quick-btn severe" :disabled="!hasSevereWarnings">
          🚨 严重预警
        </button>
        <button @click="toggleAllLayers" class="quick-btn layers">
          👁️ {{ allLayersVisible ? '隐藏图层' : '显示图层' }}
        </button>
        <button @click="resetView" class="quick-btn reset" :disabled="!isSystemReady">
          🔄 重置视图
        </button>
      </div>
    </div>

    <!-- 系统状态面板 -->
    <div class="status-panel">
      <div class="status-item">
        <span class="status-label">更新时间:</span>
        <span class="status-value">{{ lastUpdateTime }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">活跃预警:</span>
        <span class="status-value">{{ activeWarnings.length }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">严重预警:</span>
        <span class="status-value severe-count">{{ severeWarningsCount }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">覆盖区域:</span>
        <span class="status-value">珠海市全域</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, computed } from 'vue';

// 动态导入Cesium
let Cesium = null;
let viewer = null;

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const lastUpdateTime = ref('');
const autoUpdate = ref(false);

// 系统状态
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const mapStatus = ref({ text: '加载中...', class: 'loading' });
const isSystemReady = computed(() => systemStatus.value.class === 'online' && mapStatus.value.class === 'online');

// 计算属性
const severeWarningsCount = computed(() =>
  activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange').length
);

const hasSevereWarnings = computed(() => severeWarningsCount.value > 0);

const allLayersVisible = computed(() =>
  layers.buildings && layers.weather && layers.airspace && layers.labels
);

// 图层控制
const layers = reactive({
  buildings: true,
  weather: true,
  airspace: true,
  labels: true
});

// 系统变量
let weatherEntities = [];
let airspaceEntities = [];
let labelEntities = [];
let updateInterval = null;
let warningIdCounter = 0;

// 珠海市地理坐标
const ZHUHAI_BOUNDS = {
  west: 113.0354,
  south: 21.7098,
  east: 113.7759,
  north: 22.5159,
  center: {
    longitude: 113.5767,
    latitude: 22.2711,
    height: 25000
  }
};

// 珠海市重要区域
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2711,
    description: '珠海市中心区域'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.5200,
    latitude: 22.1300,
    description: '珠海经济特区'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3761,
    latitude: 22.0064,
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    longitude: 113.2500,
    latitude: 22.2000,
    description: '珠海农业区'
  },
  {
    name: '港珠澳大桥',
    icon: '🌉',
    longitude: 113.5400,
    latitude: 22.2100,
    description: '连接港澳的跨海大桥'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化珠海气象预警系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem() {
  try {
    // 步骤1: 加载Cesium模块
    systemStatus.value = { text: '加载Cesium模块...', class: 'loading' };
    await loadCesiumModule();
    
    // 步骤2: 初始化地球
    systemStatus.value = { text: '初始化地球...', class: 'loading' };
    await initializeCesiumViewer();
    
    // 步骤3: 设置珠海地图
    mapStatus.value = { text: '加载珠海地图...', class: 'loading' };
    await setupZhuhaiMap();
    
    // 步骤4: 初始化预警系统
    systemStatus.value = { text: '初始化预警系统...', class: 'loading' };
    await initializeWeatherSystem();
    
    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };
    mapStatus.value = { text: '🟢 地图就绪', class: 'online' };
    
    console.log('✅ 珠海气象预警系统初始化完成');
    
    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 2000);
    
  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    mapStatus.value = { text: '❌ 加载失败', class: 'error' };
  }
}

// 加载Cesium模块
async function loadCesiumModule() {
  try {
    console.log('开始导入Cesium模块...');
    Cesium = await import('cesium');
    console.log('Cesium模块导入成功，版本:', Cesium.VERSION);

    // 设置访问令牌
    if (Cesium.Ion) {
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
      console.log('Cesium Ion令牌设置成功');
    } else {
      console.warn('Cesium.Ion不可用，跳过令牌设置');
    }

    console.log('✅ Cesium模块加载成功');
  } catch (error) {
    console.error('Cesium模块加载详细错误:', error);
    throw new Error(`Cesium模块加载失败: ${error.message}`);
  }
}

// 初始化Cesium Viewer
async function initializeCesiumViewer() {
  try {
    // 使用更简单的配置，避免网络依赖问题
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,

      // 使用默认影像提供者，更稳定
      terrainProvider: Cesium.createWorldTerrain({
        requestWaterMask: false,
        requestVertexNormals: false
      })
    });

    // 等待地球加载完成
    await new Promise((resolve) => {
      const removeListener = viewer.scene.globe.tileLoadProgressEvent.addEventListener((queuedTileCount) => {
        if (queuedTileCount === 0) {
          removeListener();
          resolve();
        }
      });

      // 设置超时，避免无限等待
      setTimeout(() => {
        removeListener();
        resolve();
      }, 5000);
    });

    // 设置相机限制
    viewer.scene.screenSpaceCameraController.minimumZoomDistance = 5000;
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = 100000;

    // 启用深度测试
    viewer.scene.globe.depthTestAgainstTerrain = false; // 暂时禁用以提高兼容性

    console.log('✅ Cesium Viewer创建成功');
  } catch (error) {
    console.error('Cesium Viewer创建详细错误:', error);
    throw new Error(`Cesium Viewer创建失败: ${error.message}`);
  }
}

// 设置珠海地图
async function setupZhuhaiMap() {
  try {
    // 直接设置相机位置，不使用动画
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_BOUNDS.center.longitude,
        ZHUHAI_BOUNDS.center.latitude,
        ZHUHAI_BOUNDS.center.height
      ),
      orientation: {
        heading: 0.0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });

    console.log('✅ 珠海地图设置完成');
  } catch (error) {
    console.error('珠海地图设置详细错误:', error);
    throw new Error(`珠海地图设置失败: ${error.message}`);
  }
}

// 初始化预警系统
async function initializeWeatherSystem() {
  try {
    // 先只创建基础功能，避免复杂操作
    console.log('开始创建区域标签...');
    createAreaLabels();

    console.log('开始创建低空空域...');
    createAirspaceZones();

    // 暂时跳过建筑物图层，避免网络问题
    // if (layers.buildings) {
    //   viewer.scene.primitives.add(Cesium.createOsmBuildings());
    // }

    // 更新时间
    updateLastUpdateTime();

    console.log('✅ 预警系统初始化完成');
  } catch (error) {
    console.error('预警系统初始化详细错误:', error);
    throw new Error(`预警系统初始化失败: ${error.message}`);
  }
}

// 创建低空空域
function createAirspaceZones() {
  const airspaceData = [
    {
      name: '珠海机场管制区',
      coordinates: [
        [113.3500, 22.0200],
        [113.4000, 22.0200],
        [113.4000, 21.9800],
        [113.3500, 21.9800]
      ],
      height: 3000,
      color: Cesium.Color.CYAN.withAlpha(0.3)
    },
    {
      name: '港珠澳大桥空域',
      coordinates: [
        [113.5200, 22.2300],
        [113.5600, 22.2300],
        [113.5600, 22.1900],
        [113.5200, 22.1900]
      ],
      height: 1500,
      color: Cesium.Color.YELLOW.withAlpha(0.3)
    },
    {
      name: '横琴新区限制区',
      coordinates: [
        [113.5000, 22.1500],
        [113.5400, 22.1500],
        [113.5400, 22.1100],
        [113.5000, 22.1100]
      ],
      height: 2000,
      color: Cesium.Color.ORANGE.withAlpha(0.3)
    }
  ];

  airspaceData.forEach(zone => {
    const entity = viewer.entities.add({
      name: zone.name,
      polygon: {
        hierarchy: zone.coordinates.map(coord =>
          Cesium.Cartesian3.fromDegrees(coord[0], coord[1])
        ),
        height: 0,
        extrudedHeight: zone.height,
        material: zone.color,
        outline: true,
        outlineColor: zone.color.withAlpha(0.8)
      },
      label: {
        text: zone.name,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    airspaceEntities.push(entity);
  });
}

// 创建区域标签
function createAreaLabels() {
  zhuhaiAreas.value.forEach(area => {
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(area.longitude, area.latitude, 1000),
      label: {
        text: `${area.icon} ${area.name}`,
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      point: {
        pixelSize: 15,
        color: Cesium.Color.LIGHTBLUE,
        outlineColor: Cesium.Color.DARKBLUE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    labelEntities.push(entity);
  });
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🌟 生成初始预警数据...');

  // 生成3-5个初始预警
  const initialCount = Math.floor(Math.random() * 3) + 3;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 1000);
  }
}

// 生成新预警
function generateNewWarning() {
  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷风'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  // 在选定区域附近生成随机坐标
  const offsetLng = (Math.random() - 0.5) * 0.02; // ±0.01度
  const offsetLat = (Math.random() - 0.5) * 0.02;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLng,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 3) + 1, // 1-3级强度
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherEntity(warning);
  updateLastUpdateTime();

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);

  // 自动清理旧预警（保持最多10个）
  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherEntity(oldWarning.id);
  }
}

// 生成严重预警（红色或橙色）
function generateSevereWarning() {
  const warningTypes = ['龙卷风', '冰雹', '暴雨', '大风'];
  const warningLevels = ['red', 'orange']; // 只生成严重等级
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  // 在选定区域附近生成随机坐标
  const offsetLng = (Math.random() - 0.5) * 0.02;
  const offsetLat = (Math.random() - 0.5) * 0.02;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLng,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 2) + 2, // 2-3级强度（更严重）
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherEntity(warning);
  updateLastUpdateTime();

  console.log(`🚨 新增严重${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);

  // 自动飞行到严重预警位置
  setTimeout(() => {
    flyToWarning(warning);
  }, 500);

  // 自动清理旧预警
  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherEntity(oldWarning.id);
  }
}

// 导出预警数据
function exportWarningData() {
  const exportData = {
    exportTime: new Date().toISOString(),
    totalWarnings: activeWarnings.value.length,
    warningsByLevel: {
      red: getWarningCountByLevel('red'),
      orange: getWarningCountByLevel('orange'),
      yellow: getWarningCountByLevel('yellow'),
      blue: getWarningCountByLevel('blue')
    },
    warningsByArea: zhuhaiAreas.value.map(area => ({
      area: area.name,
      count: getAreaWarningCount(area),
      warnings: activeWarnings.value.filter(w => w.location === area.name)
    })),
    allWarnings: activeWarnings.value.map(warning => ({
      id: warning.id,
      type: warning.type,
      level: warning.level,
      location: warning.location,
      coordinates: {
        longitude: warning.longitude,
        latitude: warning.latitude
      },
      intensity: warning.intensity,
      time: warning.time,
      timestamp: warning.timestamp
    }))
  };

  // 创建下载链接
  const dataStr = JSON.stringify(exportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `珠海气象预警数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  console.log('📊 预警数据导出完成');
  addSystemNotification('📊 预警数据已导出到下载文件夹');
}

// 添加系统通知功能
function addSystemNotification(message) {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = 'system-notification';
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    z-index: 10000;
    font-family: 'Microsoft YaHei', sans-serif;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
  `;

  document.body.appendChild(notification);

  // 动画显示
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);

  // 3秒后自动消失
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        document.body.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// 快速操作方法
function flyToOverview() {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      ZHUHAI_BOUNDS.center.longitude,
      ZHUHAI_BOUNDS.center.latitude,
      ZHUHAI_BOUNDS.center.height
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2.0
  });

  console.log('🌍 飞行到珠海市总览');
  addSystemNotification('🌍 已切换到珠海市总览视图');
}

function focusOnSevereWarnings() {
  const severeWarnings = activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange');

  if (severeWarnings.length === 0) return;

  if (severeWarnings.length === 1) {
    // 只有一个严重预警，直接飞行过去
    flyToWarning(severeWarnings[0]);
  } else {
    // 多个严重预警，计算中心点
    const avgLng = severeWarnings.reduce((sum, w) => sum + w.longitude, 0) / severeWarnings.length;
    const avgLat = severeWarnings.reduce((sum, w) => sum + w.latitude, 0) / severeWarnings.length;

    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(avgLng, avgLat, 20000),
      duration: 2.0
    });
  }

  console.log(`🚨 聚焦到 ${severeWarnings.length} 个严重预警`);
  addSystemNotification(`🚨 已聚焦到 ${severeWarnings.length} 个严重预警`);
}

function toggleAllLayers() {
  const newState = !allLayersVisible.value;

  layers.buildings = newState;
  layers.weather = newState;
  layers.airspace = newState;
  layers.labels = newState;

  // 应用图层变化
  toggleBuildings();
  toggleWeather();
  toggleAirspace();
  toggleLabels();

  console.log(`👁️ ${newState ? '显示' : '隐藏'}所有图层`);
  addSystemNotification(`👁️ 已${newState ? '显示' : '隐藏'}所有图层`);
}

function resetView() {
  if (!viewer) return;

  // 重置到初始视图
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      ZHUHAI_BOUNDS.center.longitude,
      ZHUHAI_BOUNDS.center.latitude,
      ZHUHAI_BOUNDS.center.height
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    }
  });

  // 重置所有图层为可见
  layers.buildings = true;
  layers.weather = true;
  layers.airspace = true;
  layers.labels = true;

  // 应用图层设置
  toggleBuildings();
  toggleWeather();
  toggleAirspace();
  toggleLabels();

  console.log('🔄 视图已重置');
  addSystemNotification('🔄 视图和图层已重置到初始状态');
}

// 创建气象预警实体
function createWeatherEntity(warning) {
  const color = getWarningColor(warning.level);
  const radius = 1000 + warning.intensity * 2000; // 1-7公里半径
  const height = 300 + warning.intensity * 1200;  // 300-3900米高度

  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    position: Cesium.Cartesian3.fromDegrees(warning.longitude, warning.latitude, height / 2),
    ellipsoid: {
      radii: new Cesium.Cartesian3(radius, radius, height),
      material: color.withAlpha(0.6),
      outline: true,
      outlineColor: color
    },
    label: {
      text: `${warning.type}预警\n${getWarningLevelText(warning.level)}\n${warning.location}`,
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -100),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }
  });

  weatherEntities.push(entity);
}

// 移除气象预警实体
function removeWeatherEntity(warningId) {
  const entityId = `warning-${warningId}`;
  const entity = viewer.entities.getById(entityId);
  if (entity) {
    viewer.entities.remove(entity);
    weatherEntities = weatherEntities.filter(e => e.id !== entityId);
  }
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];

  // 清除所有预警实体
  weatherEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  weatherEntities = [];

  updateLastUpdateTime();
  console.log('🗑️ 已清除所有预警');
}

// 飞行到预警位置
function flyToWarning(warning) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      15000
    ),
    duration: 2.0
  });

  console.log(`🎯 飞行到预警: ${warning.type} - ${warning.location}`);
}

// 飞行到区域
function flyToArea(area) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      20000
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-50),
      roll: 0.0
    },
    duration: 2.5
  });

  console.log(`🎯 飞行到区域: ${area.name}`);
}

// 自动更新控制
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (Math.random() < 0.3) { // 30%概率生成新预警
        generateNewWarning();
      }
    }, 10000); // 每10秒检查一次

    console.log('▶️ 开启自动更新');
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
    console.log('⏸️ 暂停自动更新');
  }
}

// 更新最后更新时间
function updateLastUpdateTime() {
  lastUpdateTime.value = new Date().toLocaleTimeString();
}

// 获取预警颜色
function getWarningColor(level) {
  switch (level) {
    case 'red': return Cesium.Color.RED;
    case 'orange': return Cesium.Color.ORANGE;
    case 'yellow': return Cesium.Color.YELLOW;
    case 'blue': return Cesium.Color.BLUE;
    default: return Cesium.Color.GRAY;
  }
}

// 获取预警等级文本
function getWarningLevelText(level) {
  const levelMap = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return levelMap[level] || '未知预警';
}

// 获取预警图标
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊',
    '龙卷风': '🌪️'
  };
  return icons[type] || '⚠️';
}

// 统计函数
function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(warning => warning.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(warning => warning.location === area.name).length;
}

// 图层控制函数
function toggleBuildings() {
  const primitives = viewer.scene.primitives;
  const osmBuildings = primitives._primitives.find(p => p._url && p._url.includes('buildings'));

  if (layers.buildings && !osmBuildings) {
    primitives.add(Cesium.createOsmBuildings());
  } else if (!layers.buildings && osmBuildings) {
    primitives.remove(osmBuildings);
  }

  console.log(`🏢 建筑物图层: ${layers.buildings ? '开启' : '关闭'}`);
}

function toggleWeather() {
  weatherEntities.forEach(entity => {
    entity.show = layers.weather;
  });

  console.log(`☁️ 气象预警图层: ${layers.weather ? '开启' : '关闭'}`);
}

function toggleAirspace() {
  airspaceEntities.forEach(entity => {
    entity.show = layers.airspace;
  });

  console.log(`✈️ 低空空域图层: ${layers.airspace ? '开启' : '关闭'}`);
}

function toggleLabels() {
  labelEntities.forEach(entity => {
    entity.show = layers.labels;
  });

  console.log(`🏷️ 区域标签图层: ${layers.labels ? '开启' : '关闭'}`);
}

// 清理函数
function cleanup() {
  if (updateInterval) {
    clearInterval(updateInterval);
  }

  if (viewer) {
    viewer.destroy();
  }

  console.log('🧹 系统清理完成');
}
</script>

<style scoped>
.zhuhai-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
}

.cesium-container {
  width: 100%;
  height: 100%;
  background: #000;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.severe-count {
  color: #FF6B6B;
  font-weight: bold;
}

.status-value.error {
  color: #FF6B6B;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red { 
  border-left: 4px solid #ff0000; 
  background: rgba(255, 0, 0, 0.1);
}
.level-orange { 
  border-left: 4px solid #ff8800; 
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow { 
  border-left: 4px solid #ffff00; 
  background: rgba(255, 255, 0, 0.1);
}
.level-blue { 
  border-left: 4px solid #0088ff; 
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 24px;
  min-width: 30px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 10px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 9px;
  color: #666;
}

.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

.warning-control {
  margin-bottom: 20px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.severe {
  background: #FF6B35;
  color: white;
}

.control-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.export {
  background: #2196F3;
  color: white;
}

.control-btn.export:hover:not(:disabled) {
  background: #1976D2;
}

.control-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active) {
  background: #666;
  color: white;
}

.layer-control {
  margin-bottom: 15px;
}

.layer-item {
  margin-bottom: 8px;
}

.layer-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.layer-item label:hover {
  background: rgba(255, 255, 255, 0.1);
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  max-width: 250px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
}

.quick-actions-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.quick-actions-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-btn {
  padding: 8px 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-btn.overview {
  background: #4CAF50;
  color: white;
}

.quick-btn.overview:hover:not(:disabled) {
  background: #45a049;
}

.quick-btn.severe {
  background: #FF6B35;
  color: white;
}

.quick-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.quick-btn.layers {
  background: #2196F3;
  color: white;
}

.quick-btn.layers:hover:not(:disabled) {
  background: #1976D2;
}

.quick-btn.reset {
  background: #9C27B0;
  color: white;
}

.quick-btn.reset:hover:not(:disabled) {
  background: #7B1FA2;
}

.status-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
