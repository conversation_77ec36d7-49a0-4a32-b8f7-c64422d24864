<template>
  <div class="enhanced-3d-map-system">
    <!-- Canvas容器 -->
    <canvas ref="mapCanvas" class="map-canvas"></canvas>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🗺️ 珠海市增强3D地图气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value online">🟢 系统就绪</span>
        </div>
        <div class="status-item">
          <span class="status-label">渲染引擎:</span>
          <span class="status-value online">Canvas 2D + 3D效果</span>
        </div>
        <div class="status-item">
          <span class="status-label">地图模式:</span>
          <span class="status-value online">增强3D视觉</span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 4)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="focusOnWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate">
          ➕ 生成新预警
        </button>
        <button @click="generateSevereWarning" class="control-btn severe">
          🚨 生成严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更新' }}
        </button>
      </div>

      <!-- 视图控制 -->
      <div class="view-control">
        <h4>👁️ 视图控制</h4>
        <div class="view-buttons">
          <button @click="resetView" class="view-btn">🌍 总览</button>
          <button @click="toggleLabels" class="view-btn" :class="{ active: showLabels }">🏷️ 标签</button>
          <button @click="toggle3DEffect" class="view-btn" :class="{ active: enable3D }">🎯 3D效果</button>
          <button @click="toggleAnimation" class="view-btn" :class="{ active: enableAnimation }">✨ 动画</button>
        </div>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="focusOnArea(area)"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
            <div class="area-description">{{ area.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 地图图例 -->
    <div class="map-legend">
      <h4>🗺️ 地图图例</h4>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color" style="background: #16a34a;"></div>
          <span>陆地区域</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #1e40af;"></div>
          <span>海洋区域</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #fbbf24;"></div>
          <span>海岸线</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #4a5568;"></div>
          <span>建筑群</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #f1f5f9;"></div>
          <span>道路网络</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: rgba(255, 0, 0, 0.5);"></div>
          <span>预警区域</span>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';

// 响应式数据
const mapCanvas = ref(null);
const activeWarnings = ref([]);
const notification = ref(null);
const autoUpdate = ref(false);
const showLabels = ref(true);
const enable3D = ref(true);
const enableAnimation = ref(true);

// Canvas相关变量
let ctx = null;
let animationId = null;
let updateInterval = null;
let warningIdCounter = 0;

// 地图参数
const mapWidth = 800;
const mapHeight = 600;
const centerX = mapWidth / 2;
const centerY = mapHeight / 2;

// 珠海市重要区域
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    x: 420,
    y: 280,
    description: '珠海市中心区域，商务繁华'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    x: 380,
    y: 320,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    x: 320,
    y: 420,
    description: '珠海经济特区，发展迅速'
  },
  {
    name: '金湾区',
    icon: '✈️',
    x: 200,
    y: 440,
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    x: 170,
    y: 310,
    description: '珠海农业区，生态优美'
  },
  {
    name: '港珠澳大桥',
    icon: '🌉',
    x: 450,
    y: 300,
    description: '连接港澳的跨海大桥'
  }
]);

// 计算属性
const severeWarningsCount = computed(() => 
  activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange').length
);

onMounted(async () => {
  console.log('🚀 开始初始化增强3D地图系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem() {
  try {
    // 初始化Canvas
    await initializeCanvas();
    
    // 开始渲染循环
    startRenderLoop();
    
    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 1000);
    
    console.log('✅ 增强3D地图系统初始化完成');
    showNotification('✅ 增强3D地图系统启动成功', 'success');
    
  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    showNotification(`❌ 初始化失败: ${error.message}`, 'error');
  }
}

// 初始化Canvas
async function initializeCanvas() {
  await nextTick();
  
  if (!mapCanvas.value) {
    throw new Error('Canvas元素未找到');
  }
  
  // 设置Canvas尺寸
  mapCanvas.value.width = mapWidth;
  mapCanvas.value.height = mapHeight;
  
  // 获取2D上下文
  ctx = mapCanvas.value.getContext('2d');
  if (!ctx) {
    throw new Error('无法获取Canvas 2D上下文');
  }
  
  // 设置高质量渲染
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  console.log('✅ Canvas初始化完成');
}

// 开始渲染循环
function startRenderLoop() {
  function render() {
    drawMap();
    
    if (enableAnimation.value) {
      animationId = requestAnimationFrame(render);
    } else {
      // 如果动画关闭，只渲染一次
      setTimeout(() => {
        if (!enableAnimation.value) {
          animationId = requestAnimationFrame(render);
        }
      }, 100);
    }
  }
  
  render();
}

// 绘制地图
function drawMap() {
  if (!ctx) return;
  
  // 清空画布
  ctx.clearRect(0, 0, mapWidth, mapHeight);
  
  // 绘制海洋背景（带3D渐变效果）
  drawOceanBackground();
  
  // 绘制珠海市陆地
  drawZhuhaiLand();
  
  // 绘制建筑群（3D效果）
  drawBuildingClusters();
  
  // 绘制道路网络
  drawRoadNetwork();
  
  // 绘制地标建筑
  drawLandmarks();
  
  // 绘制空域边界
  drawAirspaceZones();
  
  // 绘制预警区域
  drawWarningZones();
  
  // 绘制区域标记
  drawAreaMarkers();
}

// 绘制海洋背景（3D渐变效果）
function drawOceanBackground() {
  const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, Math.max(mapWidth, mapHeight));
  gradient.addColorStop(0, '#3b82f6');
  gradient.addColorStop(0.5, '#1e40af');
  gradient.addColorStop(1, '#1e3a8a');

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, mapWidth, mapHeight);

  // 添加水波纹效果
  if (enableAnimation.value) {
    const time = Date.now() * 0.001;
    for (let i = 0; i < 5; i++) {
      const radius = 50 + i * 30 + Math.sin(time + i) * 10;
      const alpha = 0.1 - i * 0.02;

      ctx.strokeStyle = `rgba(255, 255, 255, ${alpha})`;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.arc(centerX + Math.sin(time * 0.5) * 50, centerY + Math.cos(time * 0.3) * 30, radius, 0, 2 * Math.PI);
      ctx.stroke();
    }
  }
}

// 绘制珠海市陆地
function drawZhuhaiLand() {
  // 香洲区主体（详细轮廓）
  ctx.fillStyle = '#16a34a';
  ctx.beginPath();
  ctx.moveTo(300, 180);
  ctx.lineTo(550, 180);
  ctx.lineTo(580, 220);
  ctx.lineTo(600, 280);
  ctx.lineTo(580, 350);
  ctx.lineTo(520, 380);
  ctx.lineTo(450, 390);
  ctx.lineTo(350, 380);
  ctx.lineTo(280, 350);
  ctx.lineTo(260, 280);
  ctx.lineTo(280, 220);
  ctx.closePath();
  ctx.fill();

  // 3D效果 - 添加阴影
  if (enable3D.value) {
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 10;
    ctx.shadowOffsetX = 5;
    ctx.shadowOffsetY = 5;
    ctx.fill();
    ctx.shadowColor = 'transparent';
  }

  // 海岸线
  ctx.strokeStyle = '#fbbf24';
  ctx.lineWidth = 4;
  ctx.stroke();

  // 金湾区
  ctx.fillStyle = '#15803d';
  ctx.beginPath();
  ctx.moveTo(120, 380);
  ctx.lineTo(280, 380);
  ctx.lineTo(300, 420);
  ctx.lineTo(280, 480);
  ctx.lineTo(200, 500);
  ctx.lineTo(120, 480);
  ctx.lineTo(100, 440);
  ctx.closePath();
  ctx.fill();

  if (enable3D.value) {
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 4;
    ctx.shadowOffsetY = 4;
    ctx.fill();
    ctx.shadowColor = 'transparent';
  }

  ctx.strokeStyle = '#fbbf24';
  ctx.lineWidth = 3;
  ctx.stroke();

  // 横琴岛
  ctx.fillStyle = '#22c55e';
  ctx.beginPath();
  ctx.ellipse(320, 420, 60, 40, 0, 0, 2 * Math.PI);
  ctx.fill();

  if (enable3D.value) {
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
    ctx.shadowBlur = 6;
    ctx.shadowOffsetX = 3;
    ctx.shadowOffsetY = 3;
    ctx.fill();
    ctx.shadowColor = 'transparent';
  }

  ctx.strokeStyle = '#fbbf24';
  ctx.lineWidth = 2;
  ctx.stroke();
}

// 绘制建筑群（3D效果）
function drawBuildingClusters() {
  const buildingAreas = [
    { name: '香洲商务区', x: 420, y: 280, count: 15, color: '#4a5568', height: 25 },
    { name: '拱北商圈', x: 380, y: 320, count: 10, color: '#2d3748', height: 20 },
    { name: '横琴新区', x: 320, y: 420, count: 12, color: '#1a202c', height: 30 },
    { name: '金湾中心', x: 200, y: 440, count: 8, color: '#2d3748', height: 15 },
    { name: '斗门区', x: 170, y: 310, count: 6, color: '#4a5568', height: 12 }
  ];

  buildingAreas.forEach(area => {
    for (let i = 0; i < area.count; i++) {
      const angle = (i / area.count) * 2 * Math.PI;
      const radius = 20 + Math.random() * 40;
      const x = area.x + Math.cos(angle) * radius;
      const y = area.y + Math.sin(angle) * radius;
      const width = 4 + Math.random() * 8;
      const height = area.height + Math.random() * 15;

      // 3D建筑效果
      if (enable3D.value) {
        // 建筑顶面
        ctx.fillStyle = area.color;
        ctx.fillRect(x - width/2, y - height - width/2, width, width);

        // 建筑正面
        const gradient = ctx.createLinearGradient(x - width/2, y - height, x + width/2, y);
        gradient.addColorStop(0, area.color);
        gradient.addColorStop(1, darkenColor(area.color, 0.3));
        ctx.fillStyle = gradient;
        ctx.fillRect(x - width/2, y - height, width, height);

        // 建筑侧面
        ctx.fillStyle = darkenColor(area.color, 0.5);
        ctx.beginPath();
        ctx.moveTo(x + width/2, y - height);
        ctx.lineTo(x + width/2 + width/3, y - height - width/3);
        ctx.lineTo(x + width/2 + width/3, y - width/3);
        ctx.lineTo(x + width/2, y);
        ctx.closePath();
        ctx.fill();
      } else {
        // 2D建筑
        ctx.fillStyle = area.color;
        ctx.fillRect(x - width/2, y - height, width, height);
      }

      // 建筑高光
      ctx.fillStyle = '#e2e8f0';
      ctx.fillRect(x - width/2, y - height, width, 2);
    }
  });
}

// 绘制道路网络
function drawRoadNetwork() {
  ctx.strokeStyle = '#f1f5f9';
  ctx.lineWidth = 4;
  ctx.setLineDash([]);

  // 主要道路
  const majorRoads = [
    [[150, 300], [500, 300]], // 珠海大道
    [[300, 180], [580, 350]], // 情侣路
    [[380, 320], [450, 280]], // 港珠澳大桥连接线
    [[280, 380], [320, 420]], // 横琴大桥
    [[120, 440], [280, 440]]  // 金湾大道
  ];

  majorRoads.forEach(road => {
    // 3D道路效果
    if (enable3D.value) {
      // 道路阴影
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
      ctx.lineWidth = 6;
      ctx.beginPath();
      ctx.moveTo(road[0][0] + 2, road[0][1] + 2);
      ctx.lineTo(road[1][0] + 2, road[1][1] + 2);
      ctx.stroke();
    }

    // 道路主体
    ctx.strokeStyle = '#f1f5f9';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(road[0][0], road[0][1]);
    ctx.lineTo(road[1][0], road[1][1]);
    ctx.stroke();

    // 道路中线
    ctx.strokeStyle = '#fbbf24';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.stroke();
    ctx.setLineDash([]);
  });
}

// 绘制地标建筑
function drawLandmarks() {
  const landmarks = [
    { name: '珠海大剧院', x: 450, y: 300, icon: '🎭', color: '#8b5cf6', size: 12 },
    { name: '珠海渔女', x: 500, y: 340, icon: '🧜‍♀️', color: '#06b6d4', size: 8 },
    { name: '圆明新园', x: 340, y: 390, icon: '🏛️', color: '#f59e0b', size: 15 },
    { name: '长隆海洋王国', x: 330, y: 440, icon: '🐋', color: '#ec4899', size: 18 },
    { name: '港珠澳大桥', x: 450, y: 280, icon: '🌉', color: '#10b981', size: 20 }
  ];

  landmarks.forEach(landmark => {
    // 3D地标效果
    if (enable3D.value) {
      // 地标阴影
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.beginPath();
      ctx.arc(landmark.x + 3, landmark.y + 3, landmark.size, 0, 2 * Math.PI);
      ctx.fill();
    }

    // 地标主体
    ctx.fillStyle = landmark.color;
    ctx.beginPath();
    ctx.arc(landmark.x, landmark.y, landmark.size, 0, 2 * Math.PI);
    ctx.fill();

    // 地标边框
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    ctx.stroke();

    // 地标标签
    if (showLabels.value) {
      ctx.fillStyle = 'white';
      ctx.font = '10px Microsoft YaHei';
      ctx.textAlign = 'center';
      ctx.fillText(`${landmark.icon} ${landmark.name}`, landmark.x, landmark.y - landmark.size - 8);
    }
  });
}

// 绘制空域边界
function drawAirspaceZones() {
  const airspaceZones = [
    { name: '机场管制区', x: 200, y: 440, width: 120, height: 80, color: '#06b6d4' },
    { name: '大桥空域', x: 420, y: 280, width: 80, height: 60, color: '#fbbf24' },
    { name: '横琴限制区', x: 320, y: 420, width: 100, height: 70, color: '#f97316' }
  ];

  airspaceZones.forEach(zone => {
    // 3D空域效果
    if (enable3D.value) {
      // 空域阴影
      ctx.fillStyle = `${zone.color}20`;
      ctx.fillRect(zone.x - zone.width/2 + 3, zone.y - zone.height/2 + 3, zone.width, zone.height);
    }

    // 空域边界
    ctx.strokeStyle = zone.color;
    ctx.setLineDash([8, 4]);
    ctx.lineWidth = 3;
    ctx.strokeRect(zone.x - zone.width/2, zone.y - zone.height/2, zone.width, zone.height);
    ctx.setLineDash([]);

    // 空域填充
    ctx.fillStyle = `${zone.color}30`;
    ctx.fillRect(zone.x - zone.width/2, zone.y - zone.height/2, zone.width, zone.height);

    // 空域标签
    if (showLabels.value) {
      ctx.fillStyle = zone.color;
      ctx.font = 'bold 10px Microsoft YaHei';
      ctx.textAlign = 'center';
      ctx.fillText(`✈️ ${zone.name}`, zone.x, zone.y);
    }
  });
}

// 绘制预警区域
function drawWarningZones() {
  activeWarnings.value.forEach(warning => {
    const area = zhuhaiAreas.value.find(a => a.name === warning.location);
    if (!area) return;

    const radius = 30 + warning.intensity * 15;
    const color = getWarningColor(warning.level);

    // 3D预警效果
    if (enable3D.value) {
      // 预警阴影
      ctx.fillStyle = `${color}40`;
      ctx.beginPath();
      ctx.arc(area.x + 5, area.y + 5, radius, 0, 2 * Math.PI);
      ctx.fill();
    }

    // 预警区域动画
    if (enableAnimation.value) {
      const time = Date.now() * 0.003;
      const pulseRadius = radius + Math.sin(time + warning.id) * 10;
      const alpha = 0.3 + Math.sin(time + warning.id) * 0.2;

      ctx.fillStyle = `${color}${Math.floor(alpha * 255).toString(16).padStart(2, '0')}`;
      ctx.beginPath();
      ctx.arc(area.x, area.y, pulseRadius, 0, 2 * Math.PI);
      ctx.fill();
    } else {
      ctx.fillStyle = `${color}50`;
      ctx.beginPath();
      ctx.arc(area.x, area.y, radius, 0, 2 * Math.PI);
      ctx.fill();
    }

    // 预警边界
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.setLineDash([5, 5]);
    ctx.stroke();
    ctx.setLineDash([]);

    // 预警图标
    ctx.fillStyle = 'white';
    ctx.font = '20px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText(getWarningIcon(warning.type), area.x, area.y + 7);
  });
}

// 绘制区域标记
function drawAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    // 3D区域标记效果
    if (enable3D.value) {
      // 标记阴影
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.beginPath();
      ctx.arc(area.x + 2, area.y + 2, 12, 0, 2 * Math.PI);
      ctx.fill();
    }

    // 区域背景圆
    ctx.fillStyle = 'rgba(6, 182, 212, 0.3)';
    ctx.beginPath();
    ctx.arc(area.x, area.y, 15, 0, 2 * Math.PI);
    ctx.fill();

    // 区域圆点
    ctx.fillStyle = '#06b6d4';
    ctx.beginPath();
    ctx.arc(area.x, area.y, 10, 0, 2 * Math.PI);
    ctx.fill();

    // 区域边框
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    ctx.stroke();

    // 区域标签
    if (showLabels.value) {
      // 标签背景
      const text = `${area.icon} ${area.name}`;
      const textWidth = ctx.measureText(text).width;

      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(area.x - textWidth/2 - 5, area.y - 35, textWidth + 10, 20);

      // 标签文字
      ctx.fillStyle = 'white';
      ctx.font = 'bold 12px Microsoft YaHei';
      ctx.textAlign = 'center';
      ctx.fillText(text, area.x, area.y - 20);

      // 预警数量
      const warningCount = getAreaWarningCount(area);
      if (warningCount > 0) {
        ctx.fillStyle = '#ef4444';
        ctx.beginPath();
        ctx.arc(area.x + 12, area.y - 8, 8, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = 'white';
        ctx.font = 'bold 10px Microsoft YaHei';
        ctx.fillText(warningCount.toString(), area.x + 12, area.y - 4);
      }
    }
  });
}

// 工具函数
function darkenColor(color, factor) {
  // 简单的颜色变暗函数
  const hex = color.replace('#', '');
  const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
  const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
  const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
  return `rgb(${r}, ${g}, ${b})`;
}

function getWarningColor(level) {
  const colors = {
    'red': '#ff0000',
    'orange': '#ff8800',
    'yellow': '#ffff00',
    'blue': '#0088ff'
  };
  return colors[level] || '#888888';
}

function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊',
    '龙卷风': '🌪️'
  };
  return icons[type] || '⚠️';
}

function getWarningLevelText(level) {
  const texts = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(w => w.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 3;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 800);
  }
}

// 生成新预警
function generateNewWarning() {
  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷风'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 10) {
    activeWarnings.value.shift();
  }
}

// 生成严重预警
function generateSevereWarning() {
  const warningTypes = ['龙卷风', '冰雹', '暴雨', '大风'];
  const warningLevels = ['red', 'orange'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    intensity: Math.floor(Math.random() * 2) + 2,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);

  console.log(`🚨 新增严重${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 严重${warning.type}预警 - ${warning.location}`, 'error');

  setTimeout(() => {
    focusOnWarning(warning);
  }, 500);

  if (activeWarnings.value.length > 10) {
    activeWarnings.value.shift();
  }
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];
  showNotification('🗑️ 已清除所有预警', 'info');
}

// 聚焦预警
function focusOnWarning(warning) {
  const area = zhuhaiAreas.value.find(a => a.name === warning.location);
  if (area) {
    // 这里可以添加聚焦动画效果
    showNotification(`📍 聚焦到 ${warning.location} ${warning.type}预警`, 'info');
  }
}

// 聚焦区域
function focusOnArea(area) {
  // 这里可以添加聚焦动画效果
  showNotification(`📍 聚焦到 ${area.name}`, 'info');
}

// 重置视图
function resetView() {
  showNotification('🌍 返回总览视图', 'info');
}

// 切换标签显示
function toggleLabels() {
  showLabels.value = !showLabels.value;
  showNotification(`🏷️ 标签${showLabels.value ? '显示' : '隐藏'}`, 'info');
}

// 切换3D效果
function toggle3DEffect() {
  enable3D.value = !enable3D.value;
  showNotification(`🎯 3D效果${enable3D.value ? '开启' : '关闭'}`, 'info');
}

// 切换动画
function toggleAnimation() {
  enableAnimation.value = !enableAnimation.value;
  if (enableAnimation.value) {
    startRenderLoop();
  }
  showNotification(`✨ 动画${enableAnimation.value ? '开启' : '关闭'}`, 'info');
}

// 自动更新控制
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (Math.random() < 0.3) {
        generateNewWarning();
      }
    }, 8000);
    showNotification('▶️ 自动更新已开启', 'info');
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
    showNotification('⏸️ 自动更新已暂停', 'info');
  }
}

// 通知系统
function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理系统资源...');

  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }

  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }

  console.log('✅ 系统资源清理完成');
}
</script>

<style scoped>
.enhanced-3d-map-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  overflow: hidden;
}

.map-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  background: #1e40af;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.online {
  color: #90EE90;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red { 
  border-left: 4px solid #ff0000; 
  background: rgba(255, 0, 0, 0.1);
}
.level-orange { 
  border-left: 4px solid #ff8800; 
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow { 
  border-left: 4px solid #ffff00; 
  background: rgba(255, 255, 0, 0.1);
}
.level-blue { 
  border-left: 4px solid #0088ff; 
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 20px;
  min-width: 25px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 9px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 8px;
  color: #666;
}

.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

.warning-control, .view-control {
  margin-bottom: 20px;
}

.control-btn, .view-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.severe {
  background: #FF6B35;
  color: white;
}

.control-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active) {
  background: #666;
  color: white;
}

.view-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.view-btn {
  padding: 6px 4px;
  font-size: 9px;
  background: #4a5568;
  color: white;
}

.view-btn:hover {
  background: #2d3748;
}

.view-btn.active {
  background: #3182ce;
  color: white;
  box-shadow: 0 0 8px rgba(49, 130, 206, 0.5);
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  max-width: 280px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 350px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.area-description {
  font-size: 9px;
  color: #999;
}

.map-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 180px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.map-legend h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 11px;
}

.legend-color {
  width: 20px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
