/**
 * 真实地形系统
 * 基于真实地理数据创建逼真的珠海地形
 */

import * as THREE from 'three';
import { ZHUHAI_BOUNDS, ZHUHAI_LANDMARKS } from './zhuhaiTerrain.js';

/**
 * 创建真实的珠海地形
 */
export function createRealisticZhuhaiTerrain(scene, options = {}) {
  const {
    resolution = 512,
    scale = 15000,
    enableDetailedTextures = true,
    enableRealisticWater = true
  } = options;

  const terrainGroup = new THREE.Group();
  
  // 创建主地形
  const mainTerrain = createDetailedTerrain(resolution, scale);
  terrainGroup.add(mainTerrain);
  
  // 创建真实水面
  if (enableRealisticWater) {
    const waterSystem = createRealisticWater(scale);
    terrainGroup.add(waterSystem);
  }
  
  // 添加海岸线细节
  const coastlineDetails = createCoastlineDetails(scale);
  terrainGroup.add(coastlineDetails);
  
  // 添加地形细节
  const terrainDetails = createTerrainDetails(scale);
  terrainGroup.add(terrainDetails);
  
  scene.add(terrainGroup);
  
  return {
    group: terrainGroup,
    mainTerrain,
    updateWater: (time) => updateRealisticWater(waterSystem, time)
  };
}

/**
 * 创建详细地形
 */
function createDetailedTerrain(resolution, scale) {
  // 创建高分辨率地形网格
  const geometry = new THREE.PlaneGeometry(scale, scale, resolution - 1, resolution - 1);
  const vertices = geometry.attributes.position.array;
  
  // 生成真实的高程数据
  const heightData = generateRealisticHeightData(resolution, scale);
  
  // 应用高程数据
  for (let i = 0; i < heightData.length; i++) {
    vertices[i * 3 + 2] = heightData[i];
  }
  
  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();
  
  // 创建真实地形纹理
  const terrainTexture = createRealisticTerrainTexture(resolution, heightData);
  const normalMap = createDetailedNormalMap(resolution, heightData);
  const roughnessMap = createRoughnessMap(resolution, heightData);
  
  // 使用PBR材质
  const material = new THREE.MeshStandardMaterial({
    map: terrainTexture,
    normalMap: normalMap,
    roughnessMap: roughnessMap,
    metalness: 0.1,
    roughness: 0.8
  });
  
  const mesh = new THREE.Mesh(geometry, material);
  mesh.rotation.x = -Math.PI / 2;
  mesh.receiveShadow = true;
  mesh.userData.type = 'terrain';
  
  return mesh;
}

/**
 * 生成真实的高程数据
 */
function generateRealisticHeightData(resolution, scale) {
  const heightData = [];
  
  for (let y = 0; y < resolution; y++) {
    for (let x = 0; x < resolution; x++) {
      // 转换为世界坐标
      const worldX = (x / resolution - 0.5) * scale;
      const worldZ = (y / resolution - 0.5) * scale;
      
      // 转换为经纬度
      const lat = ZHUHAI_BOUNDS.south + (ZHUHAI_BOUNDS.north - ZHUHAI_BOUNDS.south) * (0.5 - worldZ / scale);
      const lng = ZHUHAI_BOUNDS.west + (ZHUHAI_BOUNDS.east - ZHUHAI_BOUNDS.west) * (worldX / scale + 0.5);
      
      // 计算真实高程
      let elevation = calculateRealisticElevation(lat, lng, worldX, worldZ);
      
      heightData.push(elevation);
    }
  }
  
  return heightData;
}

/**
 * 计算真实高程
 */
function calculateRealisticElevation(lat, lng, worldX, worldZ) {
  let elevation = 0;
  
  // 基础地形 - 使用多层噪声
  const baseNoise = 
    Math.sin(lat * 200) * Math.cos(lng * 200) * 10 +
    Math.sin(lat * 100) * Math.cos(lng * 100) * 20 +
    Math.sin(lat * 50) * Math.cos(lng * 50) * 5;
  
  elevation += baseNoise;
  
  // 山脉系统 - 凤凰山和黄杨山
  const mountains = [
    { lat: 22.3167, lng: 113.4167, height: 437, radius: 3000 }, // 凤凰山
    { lat: 22.4167, lng: 113.3833, height: 583, radius: 4000 }  // 黄杨山
  ];
  
  mountains.forEach(mountain => {
    const mountainWorldX = (mountain.lng - ZHUHAI_BOUNDS.west) / (ZHUHAI_BOUNDS.east - ZHUHAI_BOUNDS.west) * 15000 - 7500;
    const mountainWorldZ = -(mountain.lat - ZHUHAI_BOUNDS.south) / (ZHUHAI_BOUNDS.north - ZHUHAI_BOUNDS.south) * 15000 + 7500;
    
    const distance = Math.sqrt(
      Math.pow(worldX - mountainWorldX, 2) + 
      Math.pow(worldZ - mountainWorldZ, 2)
    );
    
    if (distance < mountain.radius) {
      const influence = Math.pow(1 - distance / mountain.radius, 2);
      elevation += mountain.height * influence;
    }
  });
  
  // 海岸线处理
  const coastDistance = getDistanceToCoast(lat, lng);
  if (coastDistance < 500) {
    elevation = Math.max(-5, elevation * (coastDistance / 500));
  }
  
  // 河流和水系
  const riverEffect = getRiverEffect(lat, lng);
  elevation += riverEffect;
  
  return Math.max(-10, elevation);
}

/**
 * 获取到海岸线的距离
 */
function getDistanceToCoast(lat, lng) {
  // 简化的海岸线距离计算
  const coastPoints = [
    { lat: 22.2769, lng: 113.5678 },
    { lat: 22.2500, lng: 113.6000 },
    { lat: 22.2000, lng: 113.6200 },
    { lat: 22.1500, lng: 113.6000 },
    { lat: 22.1000, lng: 113.5500 }
  ];
  
  let minDistance = Infinity;
  coastPoints.forEach(point => {
    const distance = Math.sqrt(
      Math.pow((lat - point.lat) * 111000, 2) + 
      Math.pow((lng - point.lng) * 111000 * Math.cos(lat * Math.PI / 180), 2)
    );
    minDistance = Math.min(minDistance, distance);
  });
  
  return minDistance;
}

/**
 * 获取河流效果
 */
function getRiverEffect(lat, lng) {
  // 珠江口水系
  const riverCenterLat = 22.2;
  const riverCenterLng = 113.5;
  
  const riverDistance = Math.sqrt(
    Math.pow((lat - riverCenterLat) * 111000, 2) + 
    Math.pow((lng - riverCenterLng) * 111000 * Math.cos(lat * Math.PI / 180), 2)
  );
  
  if (riverDistance < 2000) {
    return -riverDistance / 200; // 河流低洼
  }
  
  return 0;
}

/**
 * 创建真实地形纹理
 */
function createRealisticTerrainTexture(resolution, heightData) {
  const canvas = document.createElement('canvas');
  canvas.width = resolution;
  canvas.height = resolution;
  const ctx = canvas.getContext('2d');
  
  const imageData = ctx.createImageData(resolution, resolution);
  const maxHeight = Math.max(...heightData);
  const minHeight = Math.min(...heightData);
  
  for (let i = 0; i < heightData.length; i++) {
    const height = heightData[i];
    const normalizedHeight = (height - minHeight) / (maxHeight - minHeight);
    
    const color = getRealisticTerrainColor(height, normalizedHeight);
    
    const pixelIndex = i * 4;
    imageData.data[pixelIndex] = color.r;
    imageData.data[pixelIndex + 1] = color.g;
    imageData.data[pixelIndex + 2] = color.b;
    imageData.data[pixelIndex + 3] = 255;
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.ClampToEdgeWrapping;
  texture.wrapT = THREE.ClampToEdgeWrapping;
  texture.minFilter = THREE.LinearFilter;
  texture.magFilter = THREE.LinearFilter;
  
  return texture;
}

/**
 * 获取真实地形颜色
 */
function getRealisticTerrainColor(height, normalizedHeight) {
  if (height < -2) {
    // 深水区 - 深蓝色
    return { r: 15, g: 76, b: 129 };
  } else if (height < 0) {
    // 浅水区 - 蓝绿色
    return { r: 64, g: 164, b: 223 };
  } else if (height < 5) {
    // 海滩/湿地 - 沙黄色
    return { r: 238, g: 203, b: 173 };
  } else if (height < 20) {
    // 低地平原 - 深绿色
    return { r: 85, g: 139, b: 47 };
  } else if (height < 50) {
    // 农田/草地 - 绿色
    return { r: 124, g: 252, b: 0 };
  } else if (height < 100) {
    // 丘陵 - 橄榄绿
    return { r: 107, g: 142, b: 35 };
  } else if (height < 200) {
    // 低山 - 棕绿色
    return { r: 160, g: 82, b: 45 };
  } else if (height < 400) {
    // 中山 - 棕色
    return { r: 139, g: 69, b: 19 };
  } else {
    // 高山 - 灰色
    return { r: 169, g: 169, b: 169 };
  }
}

/**
 * 创建详细法线贴图
 */
function createDetailedNormalMap(resolution, heightData) {
  const canvas = document.createElement('canvas');
  canvas.width = resolution;
  canvas.height = resolution;
  const ctx = canvas.getContext('2d');
  
  const imageData = ctx.createImageData(resolution, resolution);
  
  for (let y = 0; y < resolution; y++) {
    for (let x = 0; x < resolution; x++) {
      const index = y * resolution + x;
      
      // 计算梯度
      const left = x > 0 ? heightData[index - 1] : heightData[index];
      const right = x < resolution - 1 ? heightData[index + 1] : heightData[index];
      const up = y > 0 ? heightData[index - resolution] : heightData[index];
      const down = y < resolution - 1 ? heightData[index + resolution] : heightData[index];
      
      const dx = (right - left) * 0.1;
      const dy = (down - up) * 0.1;
      
      // 计算法线
      const normal = new THREE.Vector3(-dx, 1, -dy).normalize();
      
      const pixelIndex = index * 4;
      imageData.data[pixelIndex] = (normal.x + 1) * 127.5;
      imageData.data[pixelIndex + 1] = (normal.y + 1) * 127.5;
      imageData.data[pixelIndex + 2] = (normal.z + 1) * 127.5;
      imageData.data[pixelIndex + 3] = 255;
    }
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.ClampToEdgeWrapping;
  texture.wrapT = THREE.ClampToEdgeWrapping;
  
  return texture;
}

/**
 * 创建粗糙度贴图
 */
function createRoughnessMap(resolution, heightData) {
  const canvas = document.createElement('canvas');
  canvas.width = resolution;
  canvas.height = resolution;
  const ctx = canvas.getContext('2d');
  
  const imageData = ctx.createImageData(resolution, resolution);
  
  for (let i = 0; i < heightData.length; i++) {
    const height = heightData[i];
    
    // 根据高度和地形类型设置粗糙度
    let roughness = 0.8; // 默认粗糙度
    
    if (height < 0) {
      roughness = 0.1; // 水面光滑
    } else if (height < 5) {
      roughness = 0.3; // 海滩较光滑
    } else if (height < 50) {
      roughness = 0.7; // 草地中等粗糙
    } else {
      roughness = 0.9; // 山地粗糙
    }
    
    const pixelIndex = i * 4;
    const value = roughness * 255;
    imageData.data[pixelIndex] = value;
    imageData.data[pixelIndex + 1] = value;
    imageData.data[pixelIndex + 2] = value;
    imageData.data[pixelIndex + 3] = 255;
  }
  
  ctx.putImageData(imageData, 0, 0);
  
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.ClampToEdgeWrapping;
  texture.wrapT = THREE.ClampToEdgeWrapping;
  
  return texture;
}

/**
 * 创建真实水面系统
 */
function createRealisticWater(scale) {
  const waterGroup = new THREE.Group();
  waterGroup.userData.type = 'water';
  
  // 主水面
  const waterGeometry = new THREE.PlaneGeometry(scale * 1.5, scale * 1.5, 200, 200);
  
  // 创建水面材质
  const waterMaterial = new THREE.MeshStandardMaterial({
    color: 0x006994,
    transparent: true,
    opacity: 0.7,
    roughness: 0.1,
    metalness: 0.0,
    envMapIntensity: 1.0
  });
  
  const waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
  waterMesh.rotation.x = -Math.PI / 2;
  waterMesh.position.y = -2;
  waterGroup.add(waterMesh);
  
  // 存储原始顶点位置
  const originalPositions = waterGeometry.attributes.position.array.slice();
  waterMesh.userData.originalPositions = originalPositions;
  waterMesh.userData.geometry = waterGeometry;
  
  return waterGroup;
}

/**
 * 更新真实水面动画
 */
export function updateRealisticWater(waterSystem, time) {
  if (!waterSystem || !waterSystem.children[0]) return;
  
  const waterMesh = waterSystem.children[0];
  const geometry = waterMesh.userData.geometry;
  const originalPositions = waterMesh.userData.originalPositions;
  
  if (!geometry || !originalPositions) return;
  
  const positions = geometry.attributes.position.array;
  const t = time * 0.001;
  
  for (let i = 0; i < positions.length; i += 3) {
    const x = originalPositions[i];
    const z = originalPositions[i + 1];
    
    // 多层波浪效果
    const wave1 = Math.sin(t * 0.5 + x * 0.01) * 0.5;
    const wave2 = Math.cos(t * 0.3 + z * 0.008) * 0.3;
    const wave3 = Math.sin(t * 0.7 + (x + z) * 0.005) * 0.2;
    
    positions[i + 2] = wave1 + wave2 + wave3;
  }
  
  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();
}

/**
 * 创建海岸线细节
 */
function createCoastlineDetails(scale) {
  const coastGroup = new THREE.Group();
  coastGroup.userData.type = 'coastline';
  
  // 海岸线岩石
  for (let i = 0; i < 50; i++) {
    const rock = createRock();
    const angle = (i / 50) * Math.PI * 2;
    const radius = scale * 0.4 + Math.random() * scale * 0.1;
    
    rock.position.x = Math.cos(angle) * radius;
    rock.position.z = Math.sin(angle) * radius;
    rock.position.y = Math.random() * 5 - 2;
    
    coastGroup.add(rock);
  }
  
  return coastGroup;
}

/**
 * 创建岩石
 */
function createRock() {
  const geometry = new THREE.DodecahedronGeometry(2 + Math.random() * 3, 0);
  const material = new THREE.MeshStandardMaterial({
    color: 0x8B7355,
    roughness: 0.9,
    metalness: 0.1
  });
  
  const rock = new THREE.Mesh(geometry, material);
  rock.scale.set(
    0.8 + Math.random() * 0.4,
    0.5 + Math.random() * 0.5,
    0.8 + Math.random() * 0.4
  );
  rock.rotation.set(
    Math.random() * Math.PI,
    Math.random() * Math.PI,
    Math.random() * Math.PI
  );
  
  return rock;
}

/**
 * 创建地形细节
 */
function createTerrainDetails(scale) {
  const detailGroup = new THREE.Group();
  detailGroup.userData.type = 'details';
  
  // 添加一些地形细节如小山丘、岩石群等
  for (let i = 0; i < 30; i++) {
    const detail = createTerrainDetail();
    detail.position.x = (Math.random() - 0.5) * scale * 0.8;
    detail.position.z = (Math.random() - 0.5) * scale * 0.8;
    detail.position.y = Math.random() * 10;
    
    detailGroup.add(detail);
  }
  
  return detailGroup;
}

/**
 * 创建地形细节
 */
function createTerrainDetail() {
  const geometry = new THREE.ConeGeometry(5 + Math.random() * 10, 3 + Math.random() * 7, 8);
  const material = new THREE.MeshStandardMaterial({
    color: 0x8B4513,
    roughness: 0.8,
    metalness: 0.1
  });
  
  const detail = new THREE.Mesh(geometry, material);
  detail.rotation.y = Math.random() * Math.PI * 2;
  
  return detail;
}
