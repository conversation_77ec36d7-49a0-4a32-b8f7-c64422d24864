<template>
  <div class="cesium-test">
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <div class="test-panel">
      <h3>🌍 CesiumJS 测试</h3>
      <p>状态: {{ status }}</p>
      <button @click="testCesium" class="test-btn">测试 Cesium</button>
      <button @click="flyToZhuhai" class="test-btn">飞行到珠海</button>
      <button @click="addTestPoint" class="test-btn">添加测试点</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 动态导入Cesium
let Cesium = null;
let viewer = null;

const cesiumContainer = ref(null);
const status = ref('初始化中...');

onMounted(async () => {
  await loadCesium();
});

onUnmounted(() => {
  if (viewer) {
    viewer.destroy();
  }
});

async function loadCesium() {
  try {
    // 动态导入Cesium
    Cesium = await import('cesium');
    console.log('Cesium模块加载成功:', Cesium);
    
    // 设置访问令牌
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
    
    status.value = 'Cesium加载成功';
  } catch (error) {
    console.error('Cesium加载失败:', error);
    status.value = `加载失败: ${error.message}`;
  }
}

function testCesium() {
  if (!Cesium) {
    status.value = 'Cesium未加载';
    return;
  }
  
  try {
    // 创建简单的Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false
    });
    
    status.value = 'Cesium Viewer创建成功';
    console.log('Cesium Viewer创建成功');
  } catch (error) {
    console.error('Cesium Viewer创建失败:', error);
    status.value = `Viewer创建失败: ${error.message}`;
  }
}

function flyToZhuhai() {
  if (!viewer || !Cesium) {
    status.value = 'Viewer未初始化';
    return;
  }
  
  try {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2711, 25000),
      duration: 3.0
    });
    
    status.value = '飞行到珠海成功';
  } catch (error) {
    console.error('飞行失败:', error);
    status.value = `飞行失败: ${error.message}`;
  }
}

function addTestPoint() {
  if (!viewer || !Cesium) {
    status.value = 'Viewer未初始化';
    return;
  }
  
  try {
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5767, 22.2711, 1000),
      point: {
        pixelSize: 20,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '珠海测试点',
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50)
      }
    });
    
    status.value = '测试点添加成功';
  } catch (error) {
    console.error('添加测试点失败:', error);
    status.value = `添加测试点失败: ${error.message}`;
  }
}
</script>

<style scoped>
.cesium-test {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
}

.cesium-container {
  width: 100%;
  height: 100%;
  background: #000;
}

.test-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  min-width: 250px;
}

.test-panel h3 {
  margin: 0 0 15px 0;
  color: #FFD700;
  font-size: 16px;
}

.test-panel p {
  margin: 0 0 15px 0;
  font-size: 12px;
  color: #90EE90;
}

.test-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  display: block;
  width: 100%;
  margin-bottom: 5px;
}

.test-btn:hover {
  background: #45a049;
}
</style>
