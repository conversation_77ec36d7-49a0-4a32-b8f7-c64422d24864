/**
 * 集成无人机控制系统
 * 提供完整的无人机控制、导航和任务管理功能
 */

import * as THREE from 'three';
import { Drone, DroneManager } from './droneSystem.js';
import { latLngToWorldCoords, ZHUHAI_BOUNDS } from './zhuhaiTerrain.js';

// 飞行控制模式
export const FLIGHT_MODES = {
  MANUAL: 'manual',           // 手动控制
  AUTO: 'auto',              // 自动巡航
  WAYPOINT: 'waypoint',      // 航点飞行
  RETURN_HOME: 'return_home', // 返航
  HOVER: 'hover',            // 悬停
  EMERGENCY: 'emergency'      // 紧急模式
};

// 控制指令类型
export const CONTROL_COMMANDS = {
  TAKEOFF: 'takeoff',
  LAND: 'land',
  MOVE_FORWARD: 'move_forward',
  MOVE_BACKWARD: 'move_backward',
  MOVE_LEFT: 'move_left',
  MOVE_RIGHT: 'move_right',
  MOVE_UP: 'move_up',
  MOVE_DOWN: 'move_down',
  ROTATE_LEFT: 'rotate_left',
  ROTATE_RIGHT: 'rotate_right',
  SET_SPEED: 'set_speed',
  SET_ALTITUDE: 'set_altitude'
};

/**
 * 增强的无人机类，支持完整的飞行控制
 */
export class ControlledDrone extends Drone {
  constructor(id, type, position, route) {
    super(id, type, position, route);
    
    // 飞行控制参数
    this.flightMode = FLIGHT_MODES.AUTO;
    this.targetPosition = { ...position };
    this.targetHeading = 0;
    this.targetSpeed = 0;
    this.targetAltitude = position.y;
    
    // 物理参数
    this.velocity = { x: 0, y: 0, z: 0 };
    this.acceleration = { x: 0, y: 0, z: 0 };
    this.angularVelocity = 0;
    this.maxAcceleration = 5; // m/s²
    this.maxAngularVelocity = Math.PI; // rad/s
    
    // 控制参数
    this.pidControllers = {
      position: new PIDController(2.0, 0.1, 0.5),
      altitude: new PIDController(3.0, 0.2, 0.8),
      heading: new PIDController(1.5, 0.05, 0.3)
    };
    
    // 任务系统
    this.currentMission = null;
    this.waypoints = [];
    this.currentWaypointIndex = 0;
    
    // 传感器数据
    this.sensors = {
      gps: { lat: 0, lng: 0, altitude: 0, accuracy: 1 },
      imu: { pitch: 0, roll: 0, yaw: 0 },
      barometer: { altitude: 0, pressure: 1013.25 },
      compass: { heading: 0, declination: 0 },
      lidar: { distance: 0, obstacles: [] }
    };
    
    // 通信系统
    this.communication = {
      signalStrength: 100,
      latency: 50, // ms
      dataRate: 1000, // kbps
      connected: true
    };
  }

  /**
   * 执行控制指令
   */
  executeCommand(command, parameters = {}) {
    switch (command) {
      case CONTROL_COMMANDS.TAKEOFF:
        this.takeoff(parameters.altitude || 50);
        break;
      case CONTROL_COMMANDS.LAND:
        this.land();
        break;
      case CONTROL_COMMANDS.MOVE_FORWARD:
        this.moveRelative(parameters.distance || 10, 0, 0);
        break;
      case CONTROL_COMMANDS.MOVE_BACKWARD:
        this.moveRelative(-(parameters.distance || 10), 0, 0);
        break;
      case CONTROL_COMMANDS.MOVE_LEFT:
        this.moveRelative(0, 0, -(parameters.distance || 10));
        break;
      case CONTROL_COMMANDS.MOVE_RIGHT:
        this.moveRelative(0, 0, parameters.distance || 10);
        break;
      case CONTROL_COMMANDS.MOVE_UP:
        this.changeAltitude(parameters.distance || 10);
        break;
      case CONTROL_COMMANDS.MOVE_DOWN:
        this.changeAltitude(-(parameters.distance || 10));
        break;
      case CONTROL_COMMANDS.ROTATE_LEFT:
        this.rotate(-(parameters.angle || Math.PI / 4));
        break;
      case CONTROL_COMMANDS.ROTATE_RIGHT:
        this.rotate(parameters.angle || Math.PI / 4);
        break;
      case CONTROL_COMMANDS.SET_SPEED:
        this.setTargetSpeed(parameters.speed || 5);
        break;
      case CONTROL_COMMANDS.SET_ALTITUDE:
        this.setTargetAltitude(parameters.altitude || 100);
        break;
    }
  }

  /**
   * 起飞
   */
  takeoff(targetAltitude = 50) {
    if (this.status === 'idle') {
      this.status = 'flying';
      this.flightMode = FLIGHT_MODES.AUTO;
      this.setTargetAltitude(targetAltitude);
      this.addWarning('起飞中', 'info');
    }
  }

  /**
   * 降落
   */
  land() {
    this.flightMode = FLIGHT_MODES.AUTO;
    this.setTargetAltitude(5);
    this.setTargetSpeed(2);
    this.addWarning('准备降落', 'info');
  }

  /**
   * 相对移动
   */
  moveRelative(forward, up, right) {
    const cos = Math.cos(this.heading);
    const sin = Math.sin(this.heading);
    
    this.targetPosition.x += forward * cos - right * sin;
    this.targetPosition.z += forward * sin + right * cos;
    this.targetPosition.y += up;
    
    this.flightMode = FLIGHT_MODES.MANUAL;
  }

  /**
   * 旋转
   */
  rotate(angle) {
    this.targetHeading += angle;
    this.flightMode = FLIGHT_MODES.MANUAL;
  }

  /**
   * 设置目标速度
   */
  setTargetSpeed(speed) {
    this.targetSpeed = Math.min(speed, this.type.maxSpeed);
  }

  /**
   * 设置目标高度
   */
  setTargetAltitude(altitude) {
    this.targetAltitude = Math.min(altitude, this.type.maxAltitude);
  }

  /**
   * 设置航点任务
   */
  setWaypointMission(waypoints) {
    this.waypoints = waypoints;
    this.currentWaypointIndex = 0;
    this.flightMode = FLIGHT_MODES.WAYPOINT;
    this.status = 'flying';
  }

  /**
   * 返航
   */
  returnHome() {
    this.flightMode = FLIGHT_MODES.RETURN_HOME;
    this.targetPosition = { x: 0, y: 100, z: 0 }; // 默认起飞点
    this.addWarning('返航中', 'info');
  }

  /**
   * 悬停
   */
  hover() {
    this.flightMode = FLIGHT_MODES.HOVER;
    this.targetPosition = { ...this.position };
    this.targetSpeed = 0;
  }

  /**
   * 更新飞行控制
   */
  updateFlightControl(deltaTime) {
    // 更新传感器数据
    this.updateSensors();
    
    // 根据飞行模式执行不同的控制逻辑
    switch (this.flightMode) {
      case FLIGHT_MODES.AUTO:
        this.updateAutoFlight(deltaTime);
        break;
      case FLIGHT_MODES.WAYPOINT:
        this.updateWaypointFlight(deltaTime);
        break;
      case FLIGHT_MODES.RETURN_HOME:
        this.updateReturnHome(deltaTime);
        break;
      case FLIGHT_MODES.HOVER:
        this.updateHover(deltaTime);
        break;
      case FLIGHT_MODES.MANUAL:
        this.updateManualFlight(deltaTime);
        break;
      case FLIGHT_MODES.EMERGENCY:
        this.updateEmergencyMode(deltaTime);
        break;
    }
    
    // 应用物理控制
    this.applyPhysicsControl(deltaTime);
    
    // 更新通信状态
    this.updateCommunication();
  }

  /**
   * 更新自动飞行
   */
  updateAutoFlight(deltaTime) {
    // 继续原有的巡航逻辑
    super.updatePosition(deltaTime);
  }

  /**
   * 更新航点飞行
   */
  updateWaypointFlight(deltaTime) {
    if (this.waypoints.length === 0) return;
    
    const currentWaypoint = this.waypoints[this.currentWaypointIndex];
    if (!currentWaypoint) return;
    
    // 转换航点坐标
    const targetWorld = latLngToWorldCoords(
      currentWaypoint.lat,
      currentWaypoint.lng,
      ZHUHAI_BOUNDS,
      10000
    );
    
    this.targetPosition = {
      x: targetWorld.x,
      y: currentWaypoint.altitude || 100,
      z: targetWorld.z
    };
    
    // 检查是否到达航点
    const distance = Math.sqrt(
      Math.pow(this.position.x - this.targetPosition.x, 2) +
      Math.pow(this.position.z - this.targetPosition.z, 2)
    );
    
    if (distance < 20) {
      this.currentWaypointIndex++;
      if (this.currentWaypointIndex >= this.waypoints.length) {
        // 任务完成
        this.flightMode = FLIGHT_MODES.HOVER;
        this.addWarning('航点任务完成', 'info');
      }
    }
  }

  /**
   * 更新返航模式
   */
  updateReturnHome(deltaTime) {
    const distance = Math.sqrt(
      Math.pow(this.position.x - this.targetPosition.x, 2) +
      Math.pow(this.position.z - this.targetPosition.z, 2)
    );
    
    if (distance < 10) {
      this.land();
    }
  }

  /**
   * 更新悬停模式
   */
  updateHover(deltaTime) {
    // 保持当前位置
    this.targetSpeed = 0;
  }

  /**
   * 更新手动飞行
   */
  updateManualFlight(deltaTime) {
    // 手动控制模式下，目标位置由外部指令设置
  }

  /**
   * 更新紧急模式
   */
  updateEmergencyMode(deltaTime) {
    // 紧急降落
    this.targetAltitude = 5;
    this.targetSpeed = 1;
  }

  /**
   * 应用物理控制
   */
  applyPhysicsControl(deltaTime) {
    // 位置控制
    const positionError = {
      x: this.targetPosition.x - this.position.x,
      y: this.targetPosition.y - this.position.y,
      z: this.targetPosition.z - this.position.z
    };
    
    // PID控制计算
    const controlOutput = {
      x: this.pidControllers.position.update(positionError.x, deltaTime),
      y: this.pidControllers.altitude.update(positionError.y, deltaTime),
      z: this.pidControllers.position.update(positionError.z, deltaTime)
    };
    
    // 限制加速度
    this.acceleration.x = Math.max(-this.maxAcceleration, 
                                  Math.min(this.maxAcceleration, controlOutput.x));
    this.acceleration.y = Math.max(-this.maxAcceleration, 
                                  Math.min(this.maxAcceleration, controlOutput.y));
    this.acceleration.z = Math.max(-this.maxAcceleration, 
                                  Math.min(this.maxAcceleration, controlOutput.z));
    
    // 更新速度
    this.velocity.x += this.acceleration.x * deltaTime;
    this.velocity.y += this.acceleration.y * deltaTime;
    this.velocity.z += this.acceleration.z * deltaTime;
    
    // 限制速度
    const maxSpeed = this.targetSpeed || this.type.maxSpeed;
    const currentSpeed = Math.sqrt(
      this.velocity.x * this.velocity.x + 
      this.velocity.z * this.velocity.z
    );
    
    if (currentSpeed > maxSpeed) {
      const scale = maxSpeed / currentSpeed;
      this.velocity.x *= scale;
      this.velocity.z *= scale;
    }
    
    // 更新位置
    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;
    this.position.z += this.velocity.z * deltaTime;
    
    // 航向控制
    const headingError = this.targetHeading - this.heading;
    const headingControl = this.pidControllers.heading.update(headingError, deltaTime);
    this.angularVelocity = Math.max(-this.maxAngularVelocity, 
                                   Math.min(this.maxAngularVelocity, headingControl));
    this.heading += this.angularVelocity * deltaTime;
    
    // 更新速度和高度
    this.speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
    this.altitude = this.position.y;
  }

  /**
   * 更新传感器数据
   */
  updateSensors() {
    // GPS数据
    // 这里应该从真实GPS获取，现在使用模拟数据
    this.sensors.gps.altitude = this.position.y;
    
    // IMU数据
    this.sensors.imu.yaw = this.heading;
    
    // 气压计数据
    this.sensors.barometer.altitude = this.position.y;
    
    // 指南针数据
    this.sensors.compass.heading = this.heading;
  }

  /**
   * 更新通信状态
   */
  updateCommunication() {
    // 根据距离计算信号强度
    const homeDistance = Math.sqrt(
      this.position.x * this.position.x + 
      this.position.z * this.position.z
    );
    
    this.communication.signalStrength = Math.max(0, 100 - homeDistance / 50);
    this.communication.latency = 50 + homeDistance / 10;
    this.communication.connected = this.communication.signalStrength > 10;
    
    if (!this.communication.connected && this.flightMode !== FLIGHT_MODES.RETURN_HOME) {
      this.addWarning('通信中断，自动返航', 'danger');
      this.returnHome();
    }
  }

  /**
   * 获取详细状态
   */
  getDetailedStatus() {
    const baseStatus = super.getStatus();
    return {
      ...baseStatus,
      flightMode: this.flightMode,
      targetPosition: this.targetPosition,
      velocity: this.velocity,
      sensors: this.sensors,
      communication: this.communication,
      waypoints: this.waypoints,
      currentWaypointIndex: this.currentWaypointIndex
    };
  }
}

/**
 * PID控制器
 */
class PIDController {
  constructor(kp, ki, kd) {
    this.kp = kp; // 比例增益
    this.ki = ki; // 积分增益
    this.kd = kd; // 微分增益
    
    this.previousError = 0;
    this.integral = 0;
  }

  update(error, deltaTime) {
    this.integral += error * deltaTime;
    const derivative = (error - this.previousError) / deltaTime;
    
    const output = this.kp * error + this.ki * this.integral + this.kd * derivative;
    
    this.previousError = error;
    
    return output;
  }

  reset() {
    this.previousError = 0;
    this.integral = 0;
  }
}

/**
 * 增强的无人机管理器
 */
export class EnhancedDroneManager extends DroneManager {
  constructor(scene) {
    super(scene);
    this.selectedDrone = null;
    this.controlMode = 'auto'; // auto, manual
  }

  /**
   * 添加受控无人机
   */
  addControlledDrone(id, type, startPosition, route) {
    const drone = new ControlledDrone(id, type, startPosition, route);
    this.drones.set(id, drone);
    
    this.droneGroup.add(drone.mesh);
    this.droneGroup.add(drone.trail);
    
    return drone;
  }

  /**
   * 选择无人机进行控制
   */
  selectDrone(droneId) {
    this.selectedDrone = this.drones.get(droneId);
    return this.selectedDrone;
  }

  /**
   * 向选中的无人机发送控制指令
   */
  sendCommand(command, parameters) {
    if (this.selectedDrone) {
      this.selectedDrone.executeCommand(command, parameters);
    }
  }

  /**
   * 设置航点任务
   */
  setWaypointMission(droneId, waypoints) {
    const drone = this.drones.get(droneId);
    if (drone && drone instanceof ControlledDrone) {
      drone.setWaypointMission(waypoints);
    }
  }

  /**
   * 更新所有无人机（重写父类方法）
   */
  update(deltaTime, weatherData) {
    this.drones.forEach(drone => {
      if (drone instanceof ControlledDrone) {
        drone.updateFlightControl(deltaTime);
      }
      drone.update(deltaTime, weatherData);
    });
  }

  /**
   * 获取选中无人机的详细状态
   */
  getSelectedDroneStatus() {
    if (this.selectedDrone && this.selectedDrone instanceof ControlledDrone) {
      return this.selectedDrone.getDetailedStatus();
    }
    return null;
  }
}
