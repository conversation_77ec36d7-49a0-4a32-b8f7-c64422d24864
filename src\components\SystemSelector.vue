<template>
  <div class="system-selector">
    <div class="selector-container">
      <h1>🌍 珠海市低空三维空域动态气象预警系统</h1>
      <p class="subtitle">选择您要使用的系统版本</p>
      
      <div class="version-cards">
        <!-- CesiumJS 专业版 -->
        <div class="version-card featured" @click="selectVersion('cesium')">
          <div class="card-header">
            <h2>🌍 CesiumJS 专业版</h2>
            <span class="badge recommended">推荐</span>
          </div>
          <div class="card-content">
            <div class="features">
              <div class="feature">✅ 真实3D地球</div>
              <div class="feature">✅ 高精度地形</div>
              <div class="feature">✅ 卫星影像</div>
              <div class="feature">✅ 3D建筑模型</div>
              <div class="feature">✅ 低空空域可视化</div>
              <div class="feature">✅ 动态预警效果</div>
              <div class="feature">✅ 专业级交互</div>
            </div>
            <div class="tech-info">
              <span class="tech-tag">CesiumJS</span>
              <span class="tech-tag">WebGL</span>
              <span class="tech-tag">3D GIS</span>
            </div>
          </div>
          <div class="card-footer">
            <button class="select-btn primary">启动专业版</button>
          </div>
        </div>

        <!-- Canvas 稳定版 -->
        <div class="version-card" @click="selectVersion('stable')">
          <div class="card-header">
            <h2>🎨 Canvas 稳定版</h2>
            <span class="badge stable">稳定</span>
          </div>
          <div class="card-content">
            <div class="features">
              <div class="feature">✅ 2D地图模型</div>
              <div class="feature">✅ 详细建筑群</div>
              <div class="feature">✅ 道路网络</div>
              <div class="feature">✅ 地标建筑</div>
              <div class="feature">✅ 空域管制</div>
              <div class="feature">✅ 实时预警</div>
              <div class="feature">✅ 快速响应</div>
            </div>
            <div class="tech-info">
              <span class="tech-tag">Canvas 2D</span>
              <span class="tech-tag">Vue3</span>
              <span class="tech-tag">轻量级</span>
            </div>
          </div>
          <div class="card-footer">
            <button class="select-btn secondary">启动稳定版</button>
          </div>
        </div>

        <!-- Three.js 实验版 -->
        <div class="version-card" @click="selectVersion('weather')">
          <div class="card-header">
            <h2>🚀 Three.js 实验版</h2>
            <span class="badge experimental">实验</span>
          </div>
          <div class="card-content">
            <div class="features">
              <div class="feature">✅ 3D场景渲染</div>
              <div class="feature">✅ 动态天气效果</div>
              <div class="feature">✅ 粒子系统</div>
              <div class="feature">✅ 光影效果</div>
              <div class="feature">✅ 交互控制</div>
              <div class="feature">⚠️ 实验性功能</div>
              <div class="feature">⚠️ 性能优化中</div>
            </div>
            <div class="tech-info">
              <span class="tech-tag">Three.js</span>
              <span class="tech-tag">WebGL</span>
              <span class="tech-tag">实验性</span>
            </div>
          </div>
          <div class="card-footer">
            <button class="select-btn experimental">启动实验版</button>
          </div>
        </div>
      </div>

      <div class="system-info">
        <h3>🔧 系统信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">当前版本:</span>
            <span class="info-value">{{ currentVersion }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">浏览器:</span>
            <span class="info-value">{{ browserInfo }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">WebGL支持:</span>
            <span class="info-value" :class="webglSupport ? 'supported' : 'not-supported'">
              {{ webglSupport ? '✅ 支持' : '❌ 不支持' }}
            </span>
          </div>
          <div class="info-item">
            <span class="info-label">推荐版本:</span>
            <span class="info-value recommended-text">
              {{ webglSupport ? 'CesiumJS 专业版' : 'Canvas 稳定版' }}
            </span>
          </div>
        </div>
      </div>

      <div class="quick-actions">
        <button @click="showDemo" class="action-btn demo">🎬 查看演示</button>
        <button @click="showHelp" class="action-btn help">❓ 使用帮助</button>
        <button @click="showAbout" class="action-btn about">ℹ️ 关于系统</button>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 响应式数据
const currentVersion = ref('系统选择器');
const notification = ref(null);
const webglSupport = ref(false);

// 计算属性
const browserInfo = computed(() => {
  const ua = navigator.userAgent;
  if (ua.includes('Chrome')) return 'Chrome';
  if (ua.includes('Firefox')) return 'Firefox';
  if (ua.includes('Safari')) return 'Safari';
  if (ua.includes('Edge')) return 'Edge';
  return '未知浏览器';
});

// 生命周期
onMounted(() => {
  checkWebGLSupport();
  showNotification('🌍 欢迎使用珠海市低空三维空域动态气象预警系统', 'info');
});

// 检查WebGL支持
function checkWebGLSupport() {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    webglSupport.value = !!gl;
  } catch (e) {
    webglSupport.value = false;
  }
}

// 选择版本
function selectVersion(version) {
  const versions = {
    cesium: {
      name: 'CesiumJS 专业版',
      component: 'CesiumMapWeatherSystem',
      description: '启动专业级3D地球气象预警系统'
    },
    stable: {
      name: 'Canvas 稳定版',
      component: 'StableWeatherSystem',
      description: '启动稳定的2D地图气象预警系统'
    },
    weather: {
      name: 'Three.js 实验版',
      component: 'WeatherSystem',
      description: '启动实验性3D气象预警系统'
    }
  };

  const selected = versions[version];
  if (selected) {
    showNotification(`🚀 正在启动 ${selected.name}...`, 'info');
    
    // 延迟跳转以显示通知
    setTimeout(() => {
      // 触发版本切换事件
      window.dispatchEvent(new CustomEvent('switchVersion', { 
        detail: { version, component: selected.component } 
      }));
    }, 1000);
  }
}

// 显示演示
function showDemo() {
  showNotification('🎬 演示功能开发中...', 'info');
}

// 显示帮助
function showHelp() {
  showNotification('❓ 帮助文档开发中...', 'info');
}

// 显示关于
function showAbout() {
  showNotification('ℹ️ 珠海市低空三维空域动态气象预警系统 v1.0', 'info');
}

// 通知系统
function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}
</script>

<style scoped>
.system-selector {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Microsoft YaHei', sans-serif;
}

.selector-container {
  max-width: 1200px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2.5em;
  font-weight: bold;
}

.subtitle {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 40px;
  font-size: 1.2em;
}

.version-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.version-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.version-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.version-card.featured {
  border-color: #3498db;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h2 {
  color: #2c3e50;
  font-size: 1.4em;
  margin: 0;
}

.badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8em;
  font-weight: bold;
  text-transform: uppercase;
}

.badge.recommended {
  background: #3498db;
  color: white;
}

.badge.stable {
  background: #27ae60;
  color: white;
}

.badge.experimental {
  background: #f39c12;
  color: white;
}

.features {
  margin-bottom: 20px;
}

.feature {
  padding: 5px 0;
  color: #34495e;
  font-size: 0.9em;
}

.tech-info {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.tech-tag {
  background: #ecf0f1;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

.select-btn {
  width: 100%;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-btn.primary {
  background: #3498db;
  color: white;
}

.select-btn.primary:hover {
  background: #2980b9;
}

.select-btn.secondary {
  background: #27ae60;
  color: white;
}

.select-btn.secondary:hover {
  background: #229954;
}

.select-btn.experimental {
  background: #f39c12;
  color: white;
}

.select-btn.experimental:hover {
  background: #e67e22;
}

.system-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
}

.system-info h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3em;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ecf0f1;
}

.info-label {
  color: #7f8c8d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: bold;
}

.info-value.supported {
  color: #27ae60;
}

.info-value.not-supported {
  color: #e74c3c;
}

.recommended-text {
  color: #3498db;
}

.quick-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 24px;
  border: 2px solid #3498db;
  background: transparent;
  color: #3498db;
  border-radius: 25px;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #3498db;
  color: white;
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #27ae60;
}

.notification.error {
  background: #e74c3c;
}

.notification.info {
  background: #3498db;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

@media (max-width: 768px) {
  .selector-container {
    padding: 20px;
  }
  
  h1 {
    font-size: 2em;
  }
  
  .version-cards {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 200px;
  }
}
</style>
