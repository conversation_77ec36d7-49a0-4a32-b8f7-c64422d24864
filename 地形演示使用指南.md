# 🏔️ 珠海CesiumJS地形演示使用指南

## 🚀 **快速体验地形效果**

### **步骤1：启动系统**
1. 访问 `http://localhost:5173`
2. 等待系统初始化完成（显示"🟢 系统就绪"）

### **步骤2：显示地形演示**
1. 在右侧控制面板找到"🏔️ 显示地形演示"按钮
2. 点击按钮
3. 系统将自动：
   - 创建珠海主要山峰的3D模型
   - 生成地形网格
   - 飞行到最佳观察角度
   - 启用光照和雾效

### **步骤3：观察地形效果**
您将看到：
- **🏔️ 5座主要山峰**：黄杨山、板樟山、凤凰山、将军山、狮山
- **🌄 3D地形网格**：显示地形起伏
- **🌫️ 大气效果**：雾效和光照
- **📍 山峰标签**：显示山峰名称和高度

## 🎮 **地形控制功能**

### **地形模式切换**
在"🏔️ 地形控制"面板中：

1. **🌊 平面地形**
   - 基础椭球体地形
   - 快速加载，适合概览
   - 无网络要求

2. **🌍 Cesium地形**
   - 全球高精度地形数据
   - 需要网络连接
   - 真实的全球地形

3. **🏔️ 珠海DEM**
   - 专用3D地形模型
   - 基于真实地理数据
   - 明显的视觉效果

### **视图控制**
使用"🎥 视图控制"按钮：

- **🌍 总览**：50公里高度，俯瞰全貌
- **🚁 航拍**：20公里高度，航拍视角
- **👁️ 地面**：1公里高度，地面视角
- **🏔️ 地形**：5公里高度，最佳地形观察角度

## 🎯 **预期效果**

### **山峰模型**
- **黄杨山**：棕色圆锥体，最高峰
- **板樟山**：深绿色圆锥体，第二高峰
- **凤凰山**：绿色圆锥体，中等高度
- **将军山**：橄榄色圆锥体，较低山峰
- **狮山**：深卡其色圆锥体，最低山峰

### **地形网格**
- **颜色渐变**：从浅绿（低海拔）到棕色（高海拔）
- **立体效果**：方块状地形单元
- **自然起伏**：基于噪声函数的地形变化

### **视觉增强**
- **光照效果**：真实的光影变化
- **雾效**：增强距离感和立体感
- **标签显示**：山峰名称和高度信息

## 🛠️ **故障排除**

### **如果看不到地形效果**
1. **检查系统状态**：确保显示"🟢 系统就绪"
2. **点击地形演示**：使用"🏔️ 显示地形演示"按钮
3. **调整视角**：使用"🏔️ 地形"视图按钮
4. **刷新页面**：重新加载系统

### **如果地形模型太小**
1. **使用地形视角**：点击"🏔️ 地形"按钮
2. **手动调整相机**：使用鼠标缩放和旋转
3. **飞行到区域**：点击左下角的区域列表

### **如果性能问题**
1. **清除预警**：点击"🗑️ 清除所有预警"
2. **使用平面地形**：切换到"🌊 平面地形"
3. **关闭特效**：系统会自动优化

## 📊 **技术参数**

### **地形模型规格**
- **山峰数量**：5座主要山峰
- **地形网格**：15×15网格
- **高度范围**：20米 - 2000米
- **视觉半径**：约30公里

### **性能指标**
- **实体数量**：约300个3D对象
- **渲染帧率**：30-60 FPS
- **内存使用**：约100-200MB
- **加载时间**：2-5秒

## 🎨 **自定义选项**

### **修改山峰高度**
在代码中调整 `createDemoMountains()` 函数的 `height` 参数

### **更改地形颜色**
修改 `getTerrainColor()` 函数中的颜色映射

### **调整地形密度**
在 `createDemoTerrainGrid()` 中修改 `gridSize` 参数

## 🌟 **最佳体验建议**

1. **首次使用**：先点击"🏔️ 显示地形演示"
2. **观察角度**：使用"🏔️ 地形"视图获得最佳效果
3. **交互探索**：用鼠标旋转和缩放查看不同角度
4. **功能组合**：结合气象预警功能观察地形影响

---

现在您可以清楚地看到珠海市的3D地形效果了！🏔️✨
