<template>
  <div class="simple-cesium-system">
    <!-- Cesium地图容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市3D气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Cesium版本:</span>
          <span class="status-value" :class="cesiumVersion ? 'online' : 'error'">{{ cesiumVersion || '未加载' }}</span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 3)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="flyToWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          ➕ 生成新预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="flyToOverview" class="control-btn overview" :disabled="!isSystemReady">
          🌍 总览视图
        </button>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 动态导入Cesium
let Cesium = null;
let viewer = null;

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const notification = ref(null);
const cesiumVersion = ref('');

// 系统状态
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const isSystemReady = computed(() => systemStatus.value.class === 'online');

// 系统变量
let weatherEntities = [];
let warningIdCounter = 0;

// 珠海市地理坐标
const ZHUHAI_BOUNDS = {
  center: {
    longitude: 113.5767,
    latitude: 22.2711,
    height: 15000
  }
};

// 珠海市重要区域
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2711,
    description: '珠海市中心区域'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.5200,
    latitude: 22.1300,
    description: '珠海经济特区'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3761,
    latitude: 22.0064,
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    longitude: 113.2500,
    latitude: 22.2000,
    description: '珠海农业区'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化简化版CesiumJS系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem() {
  try {
    // 步骤1: 加载Cesium模块
    systemStatus.value = { text: '加载Cesium模块...', class: 'loading' };
    await loadCesiumModule();
    
    // 步骤2: 初始化Cesium Viewer
    systemStatus.value = { text: '初始化3D地球...', class: 'loading' };
    await initializeCesiumViewer();
    
    // 步骤3: 创建珠海3D模型
    systemStatus.value = { text: '创建珠海3D模型...', class: 'loading' };
    await createZhuhaiModel();
    
    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };
    
    console.log('✅ 简化版CesiumJS系统初始化完成');
    showNotification('✅ 3D地球系统初始化成功', 'success');
    
    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 2000);
    
  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    showNotification(`❌ 初始化失败: ${error.message}`, 'error');
  }
}

// 加载Cesium模块
async function loadCesiumModule() {
  try {
    console.log('开始导入Cesium模块...');
    Cesium = await import('cesium');
    cesiumVersion.value = Cesium.VERSION;
    console.log('Cesium模块导入成功，版本:', Cesium.VERSION);
    
    // 不设置Ion令牌，使用离线模式
    console.log('✅ Cesium模块加载成功（离线模式）');
  } catch (error) {
    console.error('Cesium模块加载详细错误:', error);
    throw new Error(`Cesium模块加载失败: ${error.message}`);
  }
}

// 初始化Cesium Viewer
async function initializeCesiumViewer() {
  try {
    console.log('开始创建Cesium Viewer...');
    
    // 创建简化的Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 基础配置
      baseLayerPicker: false,
      geocoder: false,
      homeButton: true,
      sceneModePicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: true,
      vrButton: false,
      
      // 使用椭球地形（不需要网络）
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),
      
      // 使用单色背景（不需要网络）
      imageryProvider: new Cesium.SingleTileImageryProvider({
        url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        rectangle: Cesium.Rectangle.fromDegrees(-180.0, -90.0, 180.0, 90.0)
      })
    });
    
    // 设置地球背景色
    viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#1e3a8a');
    
    // 设置相机初始位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_BOUNDS.center.longitude,
        ZHUHAI_BOUNDS.center.latitude,
        ZHUHAI_BOUNDS.center.height
      ),
      orientation: {
        heading: 0.0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });
    
    // 设置场景效果
    viewer.scene.globe.enableLighting = false;
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.fog.enabled = false;
    
    console.log('✅ Cesium Viewer 创建成功');
  } catch (error) {
    console.error('Cesium Viewer 创建失败:', error);
    throw new Error(`3D地球初始化失败: ${error.message}`);
  }
}

// 创建珠海3D模型
async function createZhuhaiModel() {
  try {
    console.log('创建珠海3D模型...');
    
    // 创建珠海市地面
    createZhuhaiTerrain();
    
    // 创建建筑模型
    createZhuhaiBuildings();

    // 创建道路网络
    createRoadNetwork();

    // 创建地标建筑
    createLandmarks();

    // 创建空域管制区
    createAirspaceZones();

    // 创建区域标记
    createAreaMarkers();
    
    console.log('✅ 珠海3D模型创建完成');
  } catch (error) {
    console.error('珠海3D模型创建失败:', error);
    throw new Error(`3D模型创建失败: ${error.message}`);
  }
}

// 创建珠海地形
function createZhuhaiTerrain() {
  // 香洲区主体（详细轮廓）
  const xiangzhouArea = [
    113.55, 22.25,
    113.60, 22.25,
    113.62, 22.28,
    113.61, 22.32,
    113.58, 22.34,
    113.54, 22.33,
    113.52, 22.30,
    113.53, 22.27
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(xiangzhouArea),
      material: Cesium.Color.fromCssColorString('#16a34a').withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#fbbf24'),
      outlineWidth: 3,
      height: 0,
      extrudedHeight: 15
    }
  });

  // 横琴新区
  const hengqinArea = [
    113.50, 22.10,
    113.55, 22.10,
    113.56, 22.15,
    113.54, 22.17,
    113.51, 22.16,
    113.49, 22.13
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(hengqinArea),
      material: Cesium.Color.fromCssColorString('#22c55e').withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#fbbf24'),
      outlineWidth: 2,
      height: 0,
      extrudedHeight: 12
    }
  });

  // 金湾区
  const jinwanArea = [
    113.35, 21.98,
    113.40, 21.98,
    113.42, 22.03,
    113.40, 22.05,
    113.36, 22.04,
    113.34, 22.01
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(jinwanArea),
      material: Cesium.Color.fromCssColorString('#15803d').withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#fbbf24'),
      outlineWidth: 2,
      height: 0,
      extrudedHeight: 10
    }
  });

  // 斗门区
  const doumenArea = [
    113.20, 22.15,
    113.30, 22.15,
    113.32, 22.25,
    113.28, 22.27,
    113.22, 22.26,
    113.18, 22.20
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(doumenArea),
      material: Cesium.Color.fromCssColorString('#166534').withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#fbbf24'),
      outlineWidth: 2,
      height: 0,
      extrudedHeight: 8
    }
  });

  // 海洋区域（更大范围）
  const oceanArea = [
    113.0, 21.8,
    113.8, 21.8,
    113.8, 22.5,
    113.0, 22.5
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(oceanArea),
      material: Cesium.Color.fromCssColorString('#1e40af').withAlpha(0.7),
      height: -10
    }
  });

  // 添加一些小岛屿
  const islands = [
    { lng: 113.65, lat: 22.35, size: 0.01 },
    { lng: 113.68, lat: 22.38, size: 0.008 },
    { lng: 113.70, lat: 22.32, size: 0.006 }
  ];

  islands.forEach((island, index) => {
    const islandArea = [
      island.lng - island.size, island.lat - island.size,
      island.lng + island.size, island.lat - island.size,
      island.lng + island.size, island.lat + island.size,
      island.lng - island.size, island.lat + island.size
    ];

    viewer.entities.add({
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(islandArea),
        material: Cesium.Color.fromCssColorString('#15803d').withAlpha(0.9),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString('#fbbf24'),
        height: 0,
        extrudedHeight: 5
      }
    });
  });

  // 添加港珠澳大桥
  createBridge();
}

// 创建港珠澳大桥
function createBridge() {
  // 大桥主体
  const bridgePoints = [
    113.54, 22.21,
    113.55, 22.22,
    113.56, 22.23,
    113.57, 22.24,
    113.58, 22.25
  ];

  viewer.entities.add({
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArray(bridgePoints),
      width: 8,
      material: Cesium.Color.fromCssColorString('#f1f5f9'),
      clampToGround: false,
      height: 50
    }
  });

  // 大桥桥塔
  const towerPositions = [
    { lng: 113.55, lat: 22.22 },
    { lng: 113.57, lat: 22.24 }
  ];

  towerPositions.forEach(pos => {
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(pos.lng, pos.lat),
      box: {
        dimensions: new Cesium.Cartesian3(20, 20, 200),
        material: Cesium.Color.fromCssColorString('#e2e8f0'),
        outline: true,
        outlineColor: Cesium.Color.WHITE
      }
    });
  });
}

// 创建道路网络
function createRoadNetwork() {
  // 主要道路
  const majorRoads = [
    // 珠海大道
    {
      name: '珠海大道',
      points: [113.25, 22.20, 113.60, 22.20],
      width: 6,
      color: '#f1f5f9'
    },
    // 情侣路
    {
      name: '情侣路',
      points: [113.55, 22.25, 113.58, 22.28, 113.60, 22.32],
      width: 5,
      color: '#f472b6'
    },
    // 港珠澳大桥连接线
    {
      name: '大桥连接线',
      points: [113.54, 22.21, 113.57, 22.27],
      width: 4,
      color: '#fbbf24'
    },
    // 横琴大桥
    {
      name: '横琴大桥',
      points: [113.52, 22.30, 113.52, 22.15],
      width: 4,
      color: '#e2e8f0'
    }
  ];

  majorRoads.forEach(road => {
    viewer.entities.add({
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray(road.points),
        width: road.width,
        material: Cesium.Color.fromCssColorString(road.color),
        clampToGround: true,
        height: 2
      }
    });
  });

  // 次要道路网格
  const minorRoads = [
    [113.56, 22.26, 113.59, 22.26],
    [113.57, 22.24, 113.57, 22.30],
    [113.52, 22.12, 113.55, 22.12],
    [113.37, 22.00, 113.40, 22.00]
  ];

  minorRoads.forEach(road => {
    viewer.entities.add({
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray(road),
        width: 3,
        material: Cesium.Color.fromCssColorString('#cbd5e1'),
        clampToGround: true,
        height: 1
      }
    });
  });
}

// 创建地标建筑
function createLandmarks() {
  const landmarks = [
    {
      name: '珠海渔女',
      lng: 113.5900,
      lat: 22.3100,
      type: 'statue',
      color: '#06b6d4',
      icon: '🧜‍♀️'
    },
    {
      name: '圆明新园',
      lng: 113.5300,
      lat: 22.1400,
      type: 'park',
      color: '#f59e0b',
      icon: '🏛️'
    },
    {
      name: '长隆海洋王国',
      lng: 113.5250,
      lat: 22.1350,
      type: 'theme_park',
      color: '#ec4899',
      icon: '🐋'
    },
    {
      name: '珠海机场',
      lng: 113.3761,
      lat: 22.0064,
      type: 'airport',
      color: '#7c3aed',
      icon: '✈️'
    }
  ];

  landmarks.forEach(landmark => {
    let geometry, dimensions;

    switch (landmark.type) {
      case 'statue':
        dimensions = new Cesium.Cartesian3(15, 15, 30);
        break;
      case 'park':
        dimensions = new Cesium.Cartesian3(80, 60, 20);
        break;
      case 'theme_park':
        dimensions = new Cesium.Cartesian3(100, 80, 25);
        break;
      case 'airport':
        dimensions = new Cesium.Cartesian3(200, 100, 30);
        break;
      default:
        dimensions = new Cesium.Cartesian3(50, 50, 20);
    }

    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(landmark.lng, landmark.lat),
      box: {
        dimensions: dimensions,
        material: Cesium.Color.fromCssColorString(landmark.color).withAlpha(0.8),
        outline: true,
        outlineColor: Cesium.Color.WHITE
      },
      label: {
        text: `${landmark.icon} ${landmark.name}`,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50),
        scale: 0.8
      }
    });
  });
}

// 创建珠海建筑
function createZhuhaiBuildings() {
  // 香洲区建筑群
  const xiangzhouBuildings = [
    { lng: 113.5767, lat: 22.2711, height: 150, name: '珠海中心大厦', color: '#4a5568' },
    { lng: 113.5780, lat: 22.2720, height: 120, name: '香洲商务中心', color: '#2d3748' },
    { lng: 113.5750, lat: 22.2700, height: 100, name: '珠海国际会展中心', color: '#1a202c' },
    { lng: 113.5790, lat: 22.2730, height: 80, name: '香洲政府大楼', color: '#4a5568' },
    { lng: 113.5740, lat: 22.2690, height: 90, name: '珠海大剧院', color: '#8b5cf6' }
  ];

  // 横琴新区建筑群
  const hengqinBuildings = [
    { lng: 113.5200, lat: 22.1300, height: 200, name: '横琴金融中心', color: '#2d3748' },
    { lng: 113.5220, lat: 22.1320, height: 180, name: '横琴科技大厦', color: '#4a5568' },
    { lng: 113.5180, lat: 22.1280, height: 160, name: '横琴国际商务中心', color: '#1a202c' },
    { lng: 113.5240, lat: 22.1340, height: 140, name: '横琴创新大厦', color: '#2d3748' }
  ];

  // 金湾区建筑群
  const jinwanBuildings = [
    { lng: 113.3761, lat: 22.0064, height: 60, name: '珠海机场航站楼', color: '#7c3aed' },
    { lng: 113.3780, lat: 22.0080, height: 40, name: '金湾区政府', color: '#4a5568' },
    { lng: 113.3740, lat: 22.0040, height: 50, name: '金湾商业中心', color: '#2d3748' }
  ];

  // 创建所有建筑
  const allBuildings = [...xiangzhouBuildings, ...hengqinBuildings, ...jinwanBuildings];

  allBuildings.forEach((building, index) => {
    // 主建筑
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat),
      box: {
        dimensions: new Cesium.Cartesian3(
          40 + Math.random() * 20,  // 宽度
          40 + Math.random() * 20,  // 长度
          building.height           // 高度
        ),
        material: Cesium.Color.fromCssColorString(building.color).withAlpha(0.8),
        outline: true,
        outlineColor: Cesium.Color.WHITE.withAlpha(0.3)
      },
      label: {
        text: building.name,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -building.height/2 - 30),
        scale: 0.7
      }
    });
  });
}

// 创建空域管制区
function createAirspaceZones() {
  const airspaceZones = [
    {
      name: '珠海机场管制区',
      area: [
        113.35, 21.98,
        113.40, 21.98,
        113.40, 22.03,
        113.35, 22.03
      ],
      height: 3000,
      color: '#06b6d4',
      icon: '✈️'
    },
    {
      name: '港珠澳大桥空域',
      area: [
        113.52, 22.20,
        113.58, 22.20,
        113.58, 22.25,
        113.52, 22.25
      ],
      height: 1500,
      color: '#fbbf24',
      icon: '🌉'
    },
    {
      name: '横琴新区限制区',
      area: [
        113.50, 22.11,
        113.55, 22.11,
        113.55, 22.16,
        113.50, 22.16
      ],
      height: 2000,
      color: '#f97316',
      icon: '🏗️'
    }
  ];

  airspaceZones.forEach(zone => {
    // 创建空域边界
    viewer.entities.add({
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(zone.area),
        material: Cesium.Color.fromCssColorString(zone.color).withAlpha(0.2),
        outline: true,
        outlineColor: Cesium.Color.fromCssColorString(zone.color),
        outlineWidth: 3,
        height: 0,
        extrudedHeight: zone.height
      }
    });

    // 添加空域标签
    const centerLng = (zone.area[0] + zone.area[4]) / 2;
    const centerLat = (zone.area[1] + zone.area[5]) / 2;

    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, zone.height / 2),
      label: {
        text: `${zone.icon} ${zone.name}\n高度限制: ${zone.height}米`,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.fromCssColorString(zone.color),
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        scale: 0.8,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER
      }
    });
  });
}

// 创建区域标记
function createAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(area.longitude, area.latitude),
      point: {
        pixelSize: 15,
        color: Cesium.Color.CYAN,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: `${area.icon} ${area.name}`,
        font: '14pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
  });
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 2;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 1000);
  }
}

// 生成新预警
function generateNewWarning() {
  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetLng = (Math.random() - 0.5) * 0.02;
  const offsetLat = (Math.random() - 0.5) * 0.02;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLng,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherEntity(warning);

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 8) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherEntity(oldWarning.id);
  }
}

// 创建预警实体
function createWeatherEntity(warning) {
  const color = getWarningColor(warning.level);
  const height = warning.intensity * 300 + 500;

  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    position: Cesium.Cartesian3.fromDegrees(warning.longitude, warning.latitude),
    cylinder: {
      length: height,
      topRadius: 400,
      bottomRadius: 600,
      material: color.withAlpha(0.5),
      outline: true,
      outlineColor: color,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    label: {
      text: `${getWarningIcon(warning.type)} ${warning.type}预警\n${getWarningLevelText(warning.level)}`,
      font: '14pt Microsoft YaHei',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -height/2 - 40),
      scale: 0.8
    }
  });

  weatherEntities.push(entity);
}

// 移除预警实体
function removeWeatherEntity(warningId) {
  const entityId = `warning-${warningId}`;
  const entity = viewer.entities.getById(entityId);
  if (entity) {
    viewer.entities.remove(entity);
  }

  weatherEntities = weatherEntities.filter(e => e.id !== entityId);
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];

  weatherEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  weatherEntities = [];

  showNotification('🗑️ 已清除所有预警', 'info');
}

// 飞行到预警位置
function flyToWarning(warning) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      3000
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-30),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行到 ${warning.location} ${warning.type}预警`, 'info');
}

// 飞行到区域
function flyToArea(area) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      5000
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行到 ${area.name}`, 'info');
}

// 飞行到总览
function flyToOverview() {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      ZHUHAI_BOUNDS.center.longitude,
      ZHUHAI_BOUNDS.center.latitude,
      ZHUHAI_BOUNDS.center.height
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3.0
  });

  showNotification('🌍 返回总览视图', 'info');
}

// 工具函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊'
  };
  return icons[type] || '⚠️';
}

function getWarningColor(level) {
  const colors = {
    'red': Cesium.Color.RED,
    'orange': Cesium.Color.ORANGE,
    'yellow': Cesium.Color.YELLOW,
    'blue': Cesium.Color.BLUE
  };
  return colors[level] || Cesium.Color.WHITE;
}

function getWarningLevelText(level) {
  const texts = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理系统资源...');

  if (viewer) {
    viewer.destroy();
    viewer = null;
  }

  weatherEntities = [];

  console.log('✅ 系统资源清理完成');
}
</script>

<style scoped>
.simple-cesium-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 300px;
  max-width: 350px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.error {
  color: #FF6B6B;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red { 
  border-left: 4px solid #ff0000; 
  background: rgba(255, 0, 0, 0.1);
}
.level-orange { 
  border-left: 4px solid #ff8800; 
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow { 
  border-left: 4px solid #ffff00; 
  background: rgba(255, 255, 0, 0.1);
}
.level-blue { 
  border-left: 4px solid #0088ff; 
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 20px;
  min-width: 25px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 9px;
  color: #999;
}

.warning-control {
  margin-bottom: 20px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.overview {
  background: #2196F3;
  color: white;
}

.control-btn.overview:hover:not(:disabled) {
  background: #1976D2;
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  max-width: 250px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 250px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 16px;
  min-width: 20px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 9px;
  color: #ccc;
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
