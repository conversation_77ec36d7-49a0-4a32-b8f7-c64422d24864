<template>
  <div class="working-cesium">
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 CesiumJS 地球</h3>
      
      <div class="status-section">
        <div class="status-item">
          <span>状态:</span>
          <span :class="statusClass">{{ status }}</span>
        </div>
        <div class="status-item">
          <span>版本:</span>
          <span>{{ cesiumVersion }}</span>
        </div>
      </div>

      <div class="controls">
        <button @click="flyToChina" class="btn">🇨🇳 中国</button>
        <button @click="flyToZhuhai" class="btn">🏙️ 珠海</button>
        <button @click="addMarker" class="btn">📍 添加标记</button>
        <button @click="clearMarkers" class="btn">🗑️ 清除</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as Cesium from 'cesium';

// 响应式数据
const cesiumContainer = ref(null);
const status = ref('初始化中...');
const statusClass = ref('loading');
const cesiumVersion = ref('');

// 系统变量
let viewer = null;
let markers = [];

// 组件挂载
onMounted(async () => {
  await initCesium();
});

// 组件卸载
onUnmounted(() => {
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
});

// 初始化Cesium
async function initCesium() {
  try {
    await nextTick();
    
    status.value = '加载CesiumJS...';
    statusClass.value = 'loading';
    
    // 检查Cesium
    if (!Cesium || !Cesium.Viewer) {
      throw new Error('CesiumJS未正确加载');
    }
    
    cesiumVersion.value = Cesium.VERSION;
    
    // 创建Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      terrainProvider: Cesium.createWorldTerrain(),
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false
    });

    // 设置初始视图
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(104.0, 35.0, 10000000),
      orientation: {
        heading: 0,
        pitch: Cesium.Math.toRadians(-90),
        roll: 0
      }
    });

    // 启用效果
    viewer.scene.globe.enableLighting = true;
    viewer.scene.skyAtmosphere.show = true;

    status.value = '系统就绪';
    statusClass.value = 'ready';
    
    // 自动飞行到珠海
    setTimeout(() => {
      flyToZhuhai();
    }, 2000);
    
  } catch (error) {
    console.error('Cesium初始化失败:', error);
    status.value = '初始化失败';
    statusClass.value = 'error';
  }
}

// 飞行到中国
function flyToChina() {
  if (!viewer) return;
  
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(104.0, 35.0, 10000000),
    duration: 3
  });
}

// 飞行到珠海
function flyToZhuhai() {
  if (!viewer) return;
  
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000),
    orientation: {
      heading: 0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0
    },
    duration: 3
  });
}

// 添加标记
function addMarker() {
  if (!viewer) return;
  
  const lng = 113.5767 + (Math.random() - 0.5) * 0.5;
  const lat = 22.2707 + (Math.random() - 0.5) * 0.3;
  
  const entity = viewer.entities.add({
    name: `标记${markers.length + 1}`,
    position: Cesium.Cartesian3.fromDegrees(lng, lat, 0),
    point: {
      pixelSize: 15,
      color: Cesium.Color.YELLOW,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2
    },
    label: {
      text: `点${markers.length + 1}`,
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -30)
    }
  });
  
  markers.push(entity);
}

// 清除标记
function clearMarkers() {
  if (!viewer) return;
  
  markers.forEach(marker => {
    viewer.entities.remove(marker);
  });
  markers = [];
}
</script>

<style scoped>
.working-cesium {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 250px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #4CAF50;
  text-align: center;
}

.status-section {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.loading {
  color: #FF9800;
}

.ready {
  color: #4CAF50;
}

.error {
  color: #F44336;
}

.controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.btn {
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 11px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}
</style>
