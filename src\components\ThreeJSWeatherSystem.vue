<template>
  <div class="threejs-weather-system">
    <!-- Three.js渲染容器 -->
    <div ref="threeContainer" class="three-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市Three.js三维气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Three.js版本:</span>
          <span class="status-value" :class="threeVersion ? 'online' : 'error'">{{ threeVersion || '未加载' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">渲染器:</span>
          <span class="status-value" :class="rendererInfo.class">{{ rendererInfo.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">场景对象:</span>
          <span class="status-value online">{{ sceneObjectCount }} 个</span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 4)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="focusOnWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          ➕ 生成新预警
        </button>
        <button @click="generateSevereWarning" class="control-btn severe" :disabled="!isSystemReady">
          🚨 生成严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }" :disabled="!isSystemReady">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更新' }}
        </button>
      </div>

      <!-- 3D场景控制 -->
      <div class="scene-control">
        <h4>🎮 3D场景控制</h4>
        <div class="scene-buttons">
          <button @click="resetCamera" class="scene-btn" :disabled="!isSystemReady">🎥 重置相机</button>
          <button @click="toggleWireframe" class="scene-btn" :class="{ active: wireframeMode }">🔲 线框模式</button>
          <button @click="toggleLighting" class="scene-btn" :class="{ active: lightingEnabled }">💡 光照效果</button>
          <button @click="toggleAnimation" class="scene-btn" :class="{ active: animationEnabled }">✨ 动画效果</button>
        </div>
      </div>

      <!-- 图层控制 -->
      <div class="layer-control">
        <h4>📊 图层控制</h4>
        <div class="layer-items">
          <label class="layer-item">
            <input type="checkbox" v-model="layers.terrain" @change="toggleTerrain">
            <span>🏔️ 地形</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.buildings" @change="toggleBuildings">
            <span>🏢 建筑物</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.weather" @change="toggleWeather">
            <span>☁️ 气象预警</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.airspace" @change="toggleAirspace">
            <span>✈️ 低空空域</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.labels" @change="toggleLabels">
            <span>🏷️ 区域标签</span>
          </label>
        </div>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
            <div class="area-description">{{ area.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 相机控制面板 -->
    <div class="camera-control-panel">
      <h4>📹 相机控制</h4>
      <div class="camera-buttons">
        <button @click="setCameraView('overview')" class="camera-btn">🌍 总览</button>
        <button @click="setCameraView('aerial')" class="camera-btn">🚁 航拍</button>
        <button @click="setCameraView('ground')" class="camera-btn">👁️ 地面</button>
        <button @click="setCameraView('side')" class="camera-btn">📐 侧视</button>
      </div>
      <div class="camera-info">
        <div class="camera-item">
          <span>位置: ({{ cameraPosition.x.toFixed(0) }}, {{ cameraPosition.y.toFixed(0) }}, {{ cameraPosition.z.toFixed(0) }})</span>
        </div>
        <div class="camera-item">
          <span>目标: ({{ cameraTarget.x.toFixed(0) }}, {{ cameraTarget.y.toFixed(0) }}, {{ cameraTarget.z.toFixed(0) }})</span>
        </div>
      </div>
    </div>

    <!-- 性能监控面板 -->
    <div class="performance-panel">
      <h4>⚡ 性能监控</h4>
      <div class="performance-stats">
        <div class="perf-item">
          <span class="perf-label">FPS:</span>
          <span class="perf-value" :class="fps >= 30 ? 'good' : fps >= 15 ? 'medium' : 'poor'">{{ fps }}</span>
        </div>
        <div class="perf-item">
          <span class="perf-label">渲染时间:</span>
          <span class="perf-value">{{ renderTime.toFixed(1) }}ms</span>
        </div>
        <div class="perf-item">
          <span class="perf-label">三角形:</span>
          <span class="perf-value">{{ triangleCount }}</span>
        </div>
        <div class="perf-item">
          <span class="perf-label">内存:</span>
          <span class="perf-value">{{ memoryUsage.toFixed(1) }}MB</span>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import * as THREE from 'three';

// Three.js相关变量
let scene = null;
let camera = null;
let renderer = null;
let controls = null;
let animationId = null;

// 响应式数据
const threeContainer = ref(null);
const activeWarnings = ref([]);
const notification = ref(null);
const threeVersion = ref('');
const autoUpdate = ref(false);
const sceneObjectCount = ref(0);

// 系统状态
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const rendererInfo = ref({ text: '未知', class: 'loading' });
const isSystemReady = computed(() => systemStatus.value.class === 'online');

// 3D场景控制
const wireframeMode = ref(false);
const lightingEnabled = ref(true);
const animationEnabled = ref(true);

// 图层控制
const layers = ref({
  terrain: true,
  buildings: true,
  weather: true,
  airspace: true,
  labels: true
});

// 相机信息
const cameraPosition = ref({ x: 0, y: 0, z: 0 });
const cameraTarget = ref({ x: 0, y: 0, z: 0 });

// 性能监控
const fps = ref(0);
const renderTime = ref(0);
const triangleCount = ref(0);
const memoryUsage = ref(0);

// 系统变量
let weatherObjects = [];
let buildingObjects = [];
let airspaceObjects = [];
let labelObjects = [];
let updateInterval = null;
let warningIdCounter = 0;
let lastTime = 0;
let frameCount = 0;

// 珠海市重要区域（3D坐标）
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    position: { x: 20, y: 0, z: 10 },
    description: '珠海市中心区域，商务繁华'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    position: { x: 15, y: 0, z: 20 },
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    position: { x: 0, y: 0, z: 30 },
    description: '珠海经济特区，发展迅速'
  },
  {
    name: '金湾区',
    icon: '✈️',
    position: { x: -30, y: 0, z: 20 },
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    position: { x: -25, y: 0, z: -10 },
    description: '珠海农业区，生态优美'
  },
  {
    name: '港珠澳大桥',
    icon: '🌉',
    position: { x: 25, y: 0, z: 15 },
    description: '连接港澳的跨海大桥'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化Three.js三维气象预警系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem() {
  try {
    // 步骤1: 检查Three.js
    systemStatus.value = { text: '加载Three.js...', class: 'loading' };
    await loadThreeJS();

    // 步骤2: 检查WebGL支持
    systemStatus.value = { text: '检查WebGL支持...', class: 'loading' };
    const webglCheck = checkWebGLSupport();
    if (!webglCheck.supported) {
      throw new Error(`WebGL不支持: ${webglCheck.error}。建议使用Canvas版本。`);
    }

    // 步骤3: 初始化3D场景
    systemStatus.value = { text: '初始化3D场景...', class: 'loading' };
    await initializeThreeScene();

    // 步骤4: 创建珠海3D模型
    systemStatus.value = { text: '创建珠海3D模型...', class: 'loading' };
    await createZhuhai3DModel();

    // 步骤5: 启动渲染循环
    systemStatus.value = { text: '启动渲染引擎...', class: 'loading' };
    startRenderLoop();

    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };

    console.log('✅ Three.js三维气象预警系统初始化完成');
    showNotification('✅ Three.js 3D系统启动成功', 'success');

    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 2000);

  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };

    // 检查是否是WebGL相关错误
    if (error.message.includes('WebGL') || error.message.includes('context')) {
      showNotification('❌ WebGL不支持，建议切换到Canvas版本', 'error');
      showWebGLFallbackMessage();
    } else {
      showNotification(`❌ 初始化失败: ${error.message}`, 'error');
    }
  }
}

// 显示WebGL降级提示
function showWebGLFallbackMessage() {
  // 创建降级提示界面
  const fallbackDiv = document.createElement('div');
  fallbackDiv.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 30px;
      border-radius: 10px;
      text-align: center;
      z-index: 10000;
      max-width: 500px;
    ">
      <h3>🚨 WebGL不支持</h3>
      <p>您的浏览器或设备不支持WebGL，无法运行Three.js 3D系统。</p>
      <p><strong>建议解决方案：</strong></p>
      <ul style="text-align: left; margin: 20px 0;">
        <li>更新您的浏览器到最新版本</li>
        <li>启用硬件加速</li>
        <li>更新显卡驱动程序</li>
        <li>使用Chrome、Firefox或Edge浏览器</li>
      </ul>
      <p>或者您可以使用我们的Canvas 2D版本：</p>
      <button onclick="switchToCanvas()" style="
        background: #4CAF50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px;
      ">切换到Canvas版本</button>
      <button onclick="this.parentElement.parentElement.remove()" style="
        background: #f44336;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 10px;
      ">关闭</button>
    </div>
  `;

  document.body.appendChild(fallbackDiv);

  // 添加切换函数到全局
  window.switchToCanvas = () => {
    // 这里可以触发切换到Canvas版本的逻辑
    console.log('切换到Canvas版本');
    fallbackDiv.remove();
    // 可以通过事件或其他方式通知父组件切换
  };
}

// 加载Three.js
async function loadThreeJS() {
  try {
    threeVersion.value = THREE.REVISION;
    console.log('Three.js版本:', THREE.REVISION);
    console.log('✅ Three.js加载成功');
  } catch (error) {
    throw new Error(`Three.js加载失败: ${error.message}`);
  }
}

// 检查WebGL支持
function checkWebGLSupport() {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

    if (!gl) {
      return { supported: false, error: '您的浏览器不支持WebGL' };
    }

    // 检查WebGL扩展
    const extensions = {
      derivatives: gl.getExtension('OES_standard_derivatives'),
      fragDepth: gl.getExtension('EXT_frag_depth'),
      drawBuffers: gl.getExtension('WEBGL_draw_buffers'),
      shaderTextureLod: gl.getExtension('EXT_shader_texture_lod')
    };

    return {
      supported: true,
      context: gl,
      extensions: extensions,
      version: gl.getParameter(gl.VERSION),
      vendor: gl.getParameter(gl.VENDOR),
      renderer: gl.getParameter(gl.RENDERER)
    };
  } catch (error) {
    return { supported: false, error: `WebGL检测失败: ${error.message}` };
  }
}

// 初始化Three.js场景
async function initializeThreeScene() {
  try {
    await nextTick();

    if (!threeContainer.value) {
      throw new Error('Three.js容器未找到');
    }

    // 检查WebGL支持
    const webglCheck = checkWebGLSupport();
    if (!webglCheck.supported) {
      throw new Error(webglCheck.error);
    }

    console.log('WebGL信息:', webglCheck);

    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB); // 天空蓝色背景
    scene.fog = new THREE.Fog(0x87CEEB, 100, 1000); // 添加雾效

    // 创建相机
    const aspect = window.innerWidth / window.innerHeight;
    camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 2000);
    camera.position.set(100, 80, 100);
    camera.lookAt(0, 0, 0);

    // 创建渲染器（使用更保守的设置）
    const rendererOptions = {
      antialias: false, // 关闭抗锯齿以提高兼容性
      alpha: false,
      powerPreference: "default", // 使用默认性能设置
      failIfMajorPerformanceCaveat: false, // 即使性能较差也继续
      preserveDrawingBuffer: false,
      premultipliedAlpha: true,
      stencil: false,
      depth: true
    };

    try {
      renderer = new THREE.WebGLRenderer(rendererOptions);
    } catch (webglError) {
      console.warn('WebGL渲染器创建失败，尝试Canvas渲染器:', webglError);
      // 降级到Canvas渲染器
      throw new Error('WebGL不可用，请使用Canvas版本');
    }

    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5)); // 限制像素比

    // 保守的阴影设置
    renderer.shadowMap.enabled = false; // 暂时关闭阴影以提高兼容性
    renderer.shadowMap.type = THREE.BasicShadowMap;

    // 设置颜色空间
    try {
      renderer.outputColorSpace = THREE.SRGBColorSpace;
    } catch (e) {
      console.warn('颜色空间设置失败，使用默认设置');
    }

    // 添加到DOM
    threeContainer.value.appendChild(renderer.domElement);

    // 设置渲染器信息
    const gl = renderer.getContext();
    rendererInfo.value = {
      text: `WebGL ${webglCheck.version}`,
      class: 'online'
    };

    // 添加轨道控制器
    try {
      const { OrbitControls } = await import('three/examples/jsm/controls/OrbitControls.js');
      controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;
      controls.screenSpacePanning = false;
      controls.minDistance = 10;
      controls.maxDistance = 500;
      controls.maxPolarAngle = Math.PI / 2;
    } catch (controlsError) {
      console.warn('轨道控制器加载失败:', controlsError);
      // 可以继续运行，只是没有鼠标控制
    }

    // 添加光照
    setupLighting();

    // 监听窗口大小变化
    window.addEventListener('resize', onWindowResize);

    console.log('✅ Three.js场景初始化完成');
  } catch (error) {
    console.error('Three.js初始化详细错误:', error);
    throw new Error(`3D场景初始化失败: ${error.message}`);
  }
}

// 设置光照
function setupLighting() {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  // 主方向光（太阳光）
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
  directionalLight.position.set(100, 100, 50);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  directionalLight.shadow.camera.near = 0.5;
  directionalLight.shadow.camera.far = 500;
  directionalLight.shadow.camera.left = -100;
  directionalLight.shadow.camera.right = 100;
  directionalLight.shadow.camera.top = 100;
  directionalLight.shadow.camera.bottom = -100;
  scene.add(directionalLight);

  // 补充光源
  const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x8B4513, 0.3);
  scene.add(hemisphereLight);
}

// 创建珠海3D模型
async function createZhuhai3DModel() {
  try {
    console.log('创建珠海3D模型...');

    // 创建地形
    createTerrain();

    // 创建海洋
    createOcean();

    // 创建建筑群
    createBuildingClusters();

    // 创建道路网络
    createRoadNetwork();

    // 创建地标建筑
    createLandmarks();

    // 创建空域管制区
    createAirspaceZones();

    // 创建区域标记
    createAreaMarkers();

    // 更新场景对象计数
    updateSceneObjectCount();

    console.log('✅ 珠海3D模型创建完成');
  } catch (error) {
    throw new Error(`3D模型创建失败: ${error.message}`);
  }
}

// 创建地形
function createTerrain() {
  // 创建珠海市真实地形
  createZhuhaiTopography();

  // 创建山脉
  createMountains();

  // 创建海岸线
  createCoastline();

  // 创建岛屿
  createIslands();

  // 创建河流
  createRivers();

  // 创建公园绿地
  createParks();
}

// 创建珠海市地形（简化版本）
function createZhuhaiTopography() {
  // 使用简化的地形创建，提高兼容性
  const terrainWidth = 120;
  const terrainDepth = 100;
  const terrainSegments = 32; // 减少分段数

  try {
    // 创建地形几何体
    const geometry = new THREE.PlaneGeometry(terrainWidth, terrainDepth, terrainSegments, terrainSegments);

    // 获取顶点位置
    const vertices = geometry.attributes.position.array;

    // 根据珠海实际地形修改高度（简化版本）
    for (let i = 0; i < vertices.length; i += 3) {
      const x = vertices[i];
      const z = vertices[i + 1];

      // 简化的高度计算
      let height = calculateSimpleTerrainHeight(x, z, terrainWidth, terrainDepth);
      vertices[i + 2] = height;
    }

    // 重新计算法向量
    geometry.computeVertexNormals();

    // 创建简化的地形材质
    const terrainMaterial = new THREE.MeshLambertMaterial({
      color: 0x228B22, // 统一绿色，避免顶点颜色复杂性
      side: THREE.DoubleSide
    });

    // 创建地形网格
    const terrain = new THREE.Mesh(geometry, terrainMaterial);
    terrain.rotation.x = -Math.PI / 2;
    terrain.receiveShadow = true;
    terrain.userData = { type: 'terrain', name: '珠海地形' };

    scene.add(terrain);

    console.log('✅ 地形创建成功');
  } catch (error) {
    console.error('地形创建失败:', error);
    // 创建简单的平面作为备用
    createSimpleTerrain();
  }
}

// 简化的高度计算
function calculateSimpleTerrainHeight(x, z, width, depth) {
  // 将坐标标准化到 -1 到 1 的范围
  const normalizedX = (x / width) * 2;
  const normalizedZ = (z / depth) * 2;

  let height = 0;

  // 基础地形高度（简化）
  height += Math.sin(normalizedX * 1.5) * 0.3;
  height += Math.cos(normalizedZ * 1.2) * 0.2;

  // 珠海特定地形特征（简化）

  // 香洲区 - 相对平坦
  if (normalizedX > 0.2 && normalizedX < 0.8 && normalizedZ > -0.2 && normalizedZ < 0.4) {
    height += 0.5;
  }

  // 横琴岛 - 岛屿地形
  const hengqinDistance = Math.sqrt(Math.pow(normalizedX - 0.1, 2) + Math.pow(normalizedZ - 0.7, 2));
  if (hengqinDistance < 0.3) {
    height += (0.3 - hengqinDistance) * 1.5;
  }

  // 山区
  if (normalizedZ < -0.3) {
    const mountainHeight = Math.max(0, 1 - Math.abs(normalizedX)) * 2;
    height += mountainHeight;
  }

  // 海平面
  if (normalizedZ > 0.8 || (normalizedX > 0.8 && normalizedZ > 0.3)) {
    height = Math.min(height, -0.3);
  }

  return height;
}

// 创建简单地形作为备用
function createSimpleTerrain() {
  console.log('创建简单备用地形...');

  // 创建几个简单的地形块
  const terrainAreas = [
    { name: '香洲区', x: 20, z: 10, width: 40, depth: 30, height: 2, color: 0x228B22 },
    { name: '金湾区', x: -30, z: 20, width: 35, depth: 25, height: 1.5, color: 0x32CD32 },
    { name: '斗门区', x: -25, z: -10, width: 30, depth: 20, height: 1, color: 0x90EE90 },
    { name: '横琴岛', x: 0, z: 30, width: 25, depth: 15, height: 1.8, color: 0x00FF7F }
  ];

  terrainAreas.forEach(area => {
    const geometry = new THREE.BoxGeometry(area.width, area.height, area.depth);
    const material = new THREE.MeshLambertMaterial({
      color: area.color,
      transparent: true,
      opacity: 0.8
    });

    const terrain = new THREE.Mesh(geometry, material);
    terrain.position.set(area.x, area.height / 2, area.z);
    terrain.receiveShadow = true;
    terrain.userData = { type: 'terrain', name: area.name };

    scene.add(terrain);
  });
}

// 计算地形高度
function calculateTerrainHeight(x, z, width, depth) {
  // 将坐标标准化到 -1 到 1 的范围
  const normalizedX = (x / width) * 2;
  const normalizedZ = (z / depth) * 2;

  let height = 0;

  // 基础地形高度
  height += Math.sin(normalizedX * 2) * 0.5;
  height += Math.cos(normalizedZ * 1.5) * 0.3;

  // 添加噪声
  height += (Math.random() - 0.5) * 0.2;

  // 珠海特定地形特征

  // 香洲区 - 相对平坦，略有起伏
  if (normalizedX > 0.2 && normalizedX < 0.8 && normalizedZ > -0.2 && normalizedZ < 0.4) {
    height += 0.5 + Math.sin(normalizedX * 8) * 0.2;
  }

  // 金湾区 - 平原地带
  if (normalizedX < -0.3 && normalizedZ > 0.1 && normalizedZ < 0.6) {
    height += 0.2 + Math.cos(normalizedX * 6) * 0.1;
  }

  // 斗门区 - 农田平原
  if (normalizedX < -0.2 && normalizedZ < 0) {
    height += 0.1 + Math.sin(normalizedX * 4) * Math.cos(normalizedZ * 4) * 0.1;
  }

  // 横琴岛 - 岛屿地形
  const hengqinDistance = Math.sqrt(Math.pow(normalizedX - 0.1, 2) + Math.pow(normalizedZ - 0.7, 2));
  if (hengqinDistance < 0.3) {
    height += (0.3 - hengqinDistance) * 2;
  }

  // 凤凰山脉 - 珠海北部山区
  if (normalizedZ < -0.3) {
    const mountainHeight = Math.max(0, 1 - Math.abs(normalizedX)) * 3;
    height += mountainHeight * Math.sin(normalizedX * 3) * Math.cos(normalizedZ * 2);
  }

  // 确保海平面以下的区域
  if (normalizedZ > 0.8 || (normalizedX > 0.8 && normalizedZ > 0.3)) {
    height = Math.min(height, -0.5);
  }

  return height;
}

// 添加地形颜色
function addTerrainColors(geometry, width, depth) {
  const colors = [];
  const vertices = geometry.attributes.position.array;

  for (let i = 0; i < vertices.length; i += 3) {
    const x = vertices[i];
    const z = vertices[i + 1];
    const y = vertices[i + 2];

    let color = new THREE.Color();

    // 根据高度和位置确定颜色
    if (y < -0.3) {
      // 海洋 - 深蓝色
      color.setHex(0x006994);
    } else if (y < 0) {
      // 浅海 - 浅蓝色
      color.setHex(0x4682B4);
    } else if (y < 0.2) {
      // 海滩/平原 - 浅黄色
      color.setHex(0xF4A460);
    } else if (y < 1) {
      // 低地 - 绿色
      color.setHex(0x228B22);
    } else if (y < 2) {
      // 丘陵 - 深绿色
      color.setHex(0x006400);
    } else {
      // 山地 - 棕色
      color.setHex(0x8B4513);
    }

    colors.push(color.r, color.g, color.b);
  }

  geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
}

// 创建山脉
function createMountains() {
  // 凤凰山
  createMountain('凤凰山', -10, -40, 15, 0x8B4513);

  // 黄杨山
  createMountain('黄杨山', 20, -35, 12, 0xA0522D);

  // 板樟山
  createMountain('板樟山', -30, -30, 10, 0x8B7355);
}

// 创建单个山峰
function createMountain(name, x, z, height, color) {
  const mountainGroup = new THREE.Group();

  // 主峰
  const peakGeometry = new THREE.ConeGeometry(8, height, 8);
  const peakMaterial = new THREE.MeshLambertMaterial({ color: color });
  const peak = new THREE.Mesh(peakGeometry, peakMaterial);
  peak.position.y = height / 2;
  peak.castShadow = true;
  peak.receiveShadow = true;
  mountainGroup.add(peak);

  // 山体基座
  const baseGeometry = new THREE.CylinderGeometry(12, 15, height * 0.6, 12);
  const baseMaterial = new THREE.MeshLambertMaterial({
    color: new THREE.Color(color).multiplyScalar(0.8)
  });
  const base = new THREE.Mesh(baseGeometry, baseMaterial);
  base.position.y = height * 0.3;
  base.receiveShadow = true;
  mountainGroup.add(base);

  // 添加一些小山丘
  for (let i = 0; i < 3; i++) {
    const angle = (i / 3) * Math.PI * 2;
    const distance = 10 + Math.random() * 5;
    const hillHeight = height * (0.3 + Math.random() * 0.3);

    const hillGeometry = new THREE.ConeGeometry(4, hillHeight, 6);
    const hillMaterial = new THREE.MeshLambertMaterial({
      color: new THREE.Color(color).multiplyScalar(0.9)
    });
    const hill = new THREE.Mesh(hillGeometry, hillMaterial);
    hill.position.set(
      Math.cos(angle) * distance,
      hillHeight / 2,
      Math.sin(angle) * distance
    );
    hill.castShadow = true;
    hill.receiveShadow = true;
    mountainGroup.add(hill);
  }

  mountainGroup.position.set(x, 0, z);
  mountainGroup.userData = { type: 'mountain', name: name };
  scene.add(mountainGroup);
}

// 创建海岸线
function createCoastline() {
  // 珠海复杂的海岸线
  const coastlinePoints = [
    // 香洲区海岸
    { x: 10, z: 25 }, { x: 20, z: 30 }, { x: 35, z: 35 }, { x: 45, z: 40 },
    // 拱北海岸
    { x: 15, z: 35 }, { x: 25, z: 40 }, { x: 30, z: 45 },
    // 横琴海岸
    { x: -5, z: 45 }, { x: 5, z: 50 }, { x: 15, z: 48 },
    // 金湾海岸
    { x: -40, z: 30 }, { x: -30, z: 35 }, { x: -20, z: 32 }
  ];

  // 创建海岸线曲线
  const curve = new THREE.CatmullRomCurve3(
    coastlinePoints.map(point => new THREE.Vector3(point.x, 0.1, point.z))
  );

  // 创建海岸线几何体
  const tubeGeometry = new THREE.TubeGeometry(curve, 50, 1, 8, false);
  const coastMaterial = new THREE.MeshLambertMaterial({ color: 0xF4A460 });
  const coastline = new THREE.Mesh(tubeGeometry, coastMaterial);
  coastline.userData = { type: 'coastline' };
  scene.add(coastline);

  // 添加海滩
  coastlinePoints.forEach((point, index) => {
    if (index % 2 === 0) {
      const beachGeometry = new THREE.CircleGeometry(3, 8);
      const beachMaterial = new THREE.MeshLambertMaterial({ color: 0xFFF8DC });
      const beach = new THREE.Mesh(beachGeometry, beachMaterial);
      beach.rotation.x = -Math.PI / 2;
      beach.position.set(point.x, 0.05, point.z);
      beach.receiveShadow = true;
      scene.add(beach);
    }
  });
}

// 创建岛屿
function createIslands() {
  const islands = [
    { name: '横琴岛', x: 0, z: 45, size: 12, height: 2 },
    { name: '高栏岛', x: -35, z: 40, size: 8, height: 1.5 },
    { name: '三灶岛', x: -40, z: 25, size: 10, height: 1.8 },
    { name: '外伶仃岛', x: 50, z: 50, size: 4, height: 3 },
    { name: '内伶仃岛', x: 45, z: 45, size: 3, height: 2.5 },
    { name: '桂山岛', x: 40, z: 55, size: 5, height: 2 },
    { name: '万山岛', x: 55, z: 60, size: 6, height: 2.8 }
  ];

  islands.forEach(island => {
    const islandGroup = new THREE.Group();

    // 岛屿主体
    const islandGeometry = new THREE.CylinderGeometry(
      island.size * 0.8,
      island.size,
      island.height,
      12
    );
    const islandMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
    const islandMesh = new THREE.Mesh(islandGeometry, islandMaterial);
    islandMesh.position.y = island.height / 2;
    islandMesh.castShadow = true;
    islandMesh.receiveShadow = true;
    islandGroup.add(islandMesh);

    // 岛屿海滩
    const beachGeometry = new THREE.CylinderGeometry(
      island.size * 1.1,
      island.size * 1.2,
      0.2,
      12
    );
    const beachMaterial = new THREE.MeshLambertMaterial({ color: 0xF4A460 });
    const beach = new THREE.Mesh(beachGeometry, beachMaterial);
    beach.position.y = 0.1;
    beach.receiveShadow = true;
    islandGroup.add(beach);

    // 添加一些植被
    for (let i = 0; i < 5; i++) {
      const angle = (i / 5) * Math.PI * 2;
      const distance = Math.random() * island.size * 0.6;

      const treeGeometry = new THREE.ConeGeometry(0.5, 2, 6);
      const treeMaterial = new THREE.MeshLambertMaterial({ color: 0x006400 });
      const tree = new THREE.Mesh(treeGeometry, treeMaterial);
      tree.position.set(
        Math.cos(angle) * distance,
        island.height + 1,
        Math.sin(angle) * distance
      );
      islandGroup.add(tree);
    }

    islandGroup.position.set(island.x, 0, island.z);
    islandGroup.userData = { type: 'island', name: island.name };
    scene.add(islandGroup);
  });
}

// 创建河流（简化版本）
function createRivers() {
  try {
    // 简化的河流 - 使用简单的长方体
    const rivers = [
      { name: '前山河', x: 0, z: 0, width: 40, depth: 2 },
      { name: '鸡啼门水道', x: 30, z: 30, width: 30, depth: 1.5 },
      { name: '磨刀门水道', x: -30, z: 15, width: 25, depth: 1.5 }
    ];

    rivers.forEach(river => {
      const geometry = new THREE.BoxGeometry(river.width, 0.2, river.depth);
      const material = new THREE.MeshLambertMaterial({
        color: 0x4682B4,
        transparent: true,
        opacity: 0.8
      });
      const riverMesh = new THREE.Mesh(geometry, material);
      riverMesh.position.set(river.x, 0.1, river.z);
      riverMesh.userData = { type: 'river', name: river.name };
      scene.add(riverMesh);
    });

    console.log('✅ 河流创建成功');
  } catch (error) {
    console.error('河流创建失败:', error);
  }
}

// 创建公园绿地
function createParks() {
  const parks = [
    { name: '海滨公园', x: 25, z: 20, size: 8 },
    { name: '香洲公园', x: 15, z: 15, size: 6 },
    { name: '凤凰山公园', x: -10, z: -35, size: 10 },
    { name: '板樟山森林公园', x: -25, z: -25, size: 12 },
    { name: '金山公园', x: -30, z: 10, size: 5 }
  ];

  parks.forEach(park => {
    const parkGroup = new THREE.Group();

    // 公园草地
    const grassGeometry = new THREE.CircleGeometry(park.size, 16);
    const grassMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const grass = new THREE.Mesh(grassGeometry, grassMaterial);
    grass.rotation.x = -Math.PI / 2;
    grass.position.y = 0.05;
    grass.receiveShadow = true;
    parkGroup.add(grass);

    // 添加树木
    for (let i = 0; i < 15; i++) {
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * park.size * 0.8;

      // 树干
      const trunkGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3, 6);
      const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
      const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
      trunk.position.set(
        Math.cos(angle) * distance,
        1.5,
        Math.sin(angle) * distance
      );
      trunk.castShadow = true;
      parkGroup.add(trunk);

      // 树冠
      const crownGeometry = new THREE.SphereGeometry(1.5, 8, 6);
      const crownMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
      const crown = new THREE.Mesh(crownGeometry, crownMaterial);
      crown.position.set(
        Math.cos(angle) * distance,
        4,
        Math.sin(angle) * distance
      );
      crown.castShadow = true;
      parkGroup.add(crown);
    }

    // 添加小径
    const pathGeometry = new THREE.RingGeometry(park.size * 0.3, park.size * 0.35, 16);
    const pathMaterial = new THREE.MeshLambertMaterial({ color: 0xD2B48C });
    const path = new THREE.Mesh(pathGeometry, pathMaterial);
    path.rotation.x = -Math.PI / 2;
    path.position.y = 0.06;
    parkGroup.add(path);

    parkGroup.position.set(park.x, 0, park.z);
    parkGroup.userData = { type: 'park', name: park.name };
    scene.add(parkGroup);
  });
}

// 创建海洋
function createOcean() {
  // 创建分层海洋效果
  createOceanLayers();

  // 创建海湾
  createBays();

  // 创建珠江口
  createPearlRiverEstuary();
}

// 创建海洋层次（简化版本）
function createOceanLayers() {
  try {
    // 简化的海洋 - 单层平面
    const oceanGeometry = new THREE.PlaneGeometry(200, 200);
    const oceanMaterial = new THREE.MeshLambertMaterial({
      color: 0x006994,
      transparent: true,
      opacity: 0.7
    });

    const ocean = new THREE.Mesh(oceanGeometry, oceanMaterial);
    ocean.rotation.x = -Math.PI / 2;
    ocean.position.y = -0.5;
    ocean.receiveShadow = true;
    ocean.userData = { type: 'ocean' };
    scene.add(ocean);

    console.log('✅ 海洋创建成功');
  } catch (error) {
    console.error('海洋创建失败:', error);
  }
}

// 创建海湾
function createBays() {
  const bays = [
    { name: '香洲湾', x: 25, z: 30, size: 15 },
    { name: '拱北湾', x: 20, z: 35, size: 12 },
    { name: '横琴湾', x: 5, z: 40, size: 18 },
    { name: '金湾', x: -25, z: 25, size: 20 },
    { name: '唐家湾', x: 35, z: 25, size: 10 }
  ];

  bays.forEach(bay => {
    // 海湾水面
    const bayGeometry = new THREE.CircleGeometry(bay.size, 16);
    const bayMaterial = new THREE.MeshLambertMaterial({
      color: 0x6495ED,
      transparent: true,
      opacity: 0.8
    });
    const bayMesh = new THREE.Mesh(bayGeometry, bayMaterial);
    bayMesh.rotation.x = -Math.PI / 2;
    bayMesh.position.set(bay.x, -0.2, bay.z);
    bayMesh.receiveShadow = true;
    bayMesh.userData = { type: 'bay', name: bay.name };
    scene.add(bayMesh);

    // 海湾周围的浅滩
    const shallowGeometry = new THREE.RingGeometry(bay.size, bay.size * 1.2, 16);
    const shallowMaterial = new THREE.MeshLambertMaterial({
      color: 0xF0E68C,
      transparent: true,
      opacity: 0.6
    });
    const shallow = new THREE.Mesh(shallowGeometry, shallowMaterial);
    shallow.rotation.x = -Math.PI / 2;
    shallow.position.set(bay.x, -0.1, bay.z);
    shallow.receiveShadow = true;
    scene.add(shallow);
  });
}

// 创建珠江口
function createPearlRiverEstuary() {
  // 珠江口主体
  const estuaryPoints = [
    new THREE.Vector3(-20, -0.3, -10),
    new THREE.Vector3(-10, -0.3, 0),
    new THREE.Vector3(0, -0.3, 10),
    new THREE.Vector3(10, -0.3, 20),
    new THREE.Vector3(20, -0.3, 30),
    new THREE.Vector3(30, -0.3, 40)
  ];

  const estuaryCurve = new THREE.CatmullRomCurve3(estuaryPoints);
  const estuaryGeometry = new THREE.TubeGeometry(estuaryCurve, 40, 8, 8, false);
  const estuaryMaterial = new THREE.MeshLambertMaterial({
    color: 0x4682B4,
    transparent: true,
    opacity: 0.9
  });
  const estuary = new THREE.Mesh(estuaryGeometry, estuaryMaterial);
  estuary.userData = { type: 'estuary', name: '珠江口' };
  scene.add(estuary);

  // 河口三角洲
  const deltaGeometry = new THREE.CircleGeometry(25, 12);
  const deltaMaterial = new THREE.MeshLambertMaterial({
    color: 0xDEB887,
    transparent: true,
    opacity: 0.7
  });
  const delta = new THREE.Mesh(deltaGeometry, deltaMaterial);
  delta.rotation.x = -Math.PI / 2;
  delta.position.set(15, -0.1, 25);
  delta.receiveShadow = true;
  delta.userData = { type: 'delta' };
  scene.add(delta);
}

// 创建建筑群
function createBuildingClusters() {
  const buildingAreas = [
    { name: '香洲商务区', x: 20, z: 10, count: 15, maxHeight: 25, color: 0x4a5568, type: 'commercial' },
    { name: '拱北商圈', x: 15, z: 20, count: 10, maxHeight: 20, color: 0x2d3748, type: 'mixed' },
    { name: '横琴新区', x: 0, z: 30, count: 12, maxHeight: 30, color: 0x1a202c, type: 'modern' },
    { name: '金湾中心', x: -30, z: 20, count: 8, maxHeight: 15, color: 0x2d3748, type: 'industrial' },
    { name: '斗门区', x: -25, z: -10, count: 6, maxHeight: 12, color: 0x4a5568, type: 'residential' }
  ];

  buildingAreas.forEach(area => {
    for (let i = 0; i < area.count; i++) {
      const angle = (i / area.count) * 2 * Math.PI;
      const radius = 5 + Math.random() * 15;
      const x = area.x + Math.cos(angle) * radius;
      const z = area.z + Math.sin(angle) * radius;

      // 根据区域类型创建不同的建筑
      const building = createRealisticBuilding(area.type, area.color, area.maxHeight);
      building.position.set(x, 0, z);
      building.userData = {
        type: 'building',
        area: area.name,
        buildingType: area.type
      };

      scene.add(building);
      buildingObjects.push(building);
    }
  });
}

// 创建真实建筑模型
function createRealisticBuilding(buildingType, baseColor, maxHeight) {
  const buildingGroup = new THREE.Group();

  switch (buildingType) {
    case 'commercial':
      return createCommercialBuilding(baseColor, maxHeight);
    case 'mixed':
      return createMixedUseBuilding(baseColor, maxHeight);
    case 'modern':
      return createModernSkyscraper(baseColor, maxHeight);
    case 'industrial':
      return createIndustrialBuilding(baseColor, maxHeight);
    case 'residential':
      return createResidentialBuilding(baseColor, maxHeight);
    default:
      return createCommercialBuilding(baseColor, maxHeight);
  }
}

// 创建商业建筑
function createCommercialBuilding(baseColor, maxHeight) {
  const buildingGroup = new THREE.Group();
  const height = 8 + Math.random() * (maxHeight - 8);
  const width = 3 + Math.random() * 4;
  const depth = 3 + Math.random() * 4;

  // 主体建筑
  const mainGeometry = new THREE.BoxGeometry(width, height, depth);
  const mainMaterial = new THREE.MeshLambertMaterial({
    color: baseColor,
    transparent: true,
    opacity: 0.9
  });
  const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
  mainBuilding.position.y = height / 2;
  mainBuilding.castShadow = true;
  mainBuilding.receiveShadow = true;
  buildingGroup.add(mainBuilding);

  // 顶部装饰
  const roofGeometry = new THREE.BoxGeometry(width + 0.2, 0.5, depth + 0.2);
  const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.y = height + 0.25;
  roof.castShadow = true;
  buildingGroup.add(roof);

  // 窗户
  createWindows(buildingGroup, width, height, depth, 0x87CEEB);

  // 入口
  const entranceGeometry = new THREE.BoxGeometry(1, 2.5, 0.2);
  const entranceMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
  const entrance = new THREE.Mesh(entranceGeometry, entranceMaterial);
  entrance.position.set(0, 1.25, depth/2 + 0.1);
  buildingGroup.add(entrance);

  return buildingGroup;
}

// 创建混合用途建筑
function createMixedUseBuilding(baseColor, maxHeight) {
  const buildingGroup = new THREE.Group();
  const totalHeight = 10 + Math.random() * (maxHeight - 10);
  const width = 4 + Math.random() * 3;
  const depth = 4 + Math.random() * 3;

  // 底层商业部分
  const commercialHeight = 4;
  const commercialGeometry = new THREE.BoxGeometry(width, commercialHeight, depth);
  const commercialMaterial = new THREE.MeshLambertMaterial({
    color: new THREE.Color(baseColor).multiplyScalar(1.2)
  });
  const commercial = new THREE.Mesh(commercialGeometry, commercialMaterial);
  commercial.position.y = commercialHeight / 2;
  commercial.castShadow = true;
  commercial.receiveShadow = true;
  buildingGroup.add(commercial);

  // 上层住宅部分
  const residentialHeight = totalHeight - commercialHeight;
  const residentialWidth = width * 0.8;
  const residentialDepth = depth * 0.8;
  const residentialGeometry = new THREE.BoxGeometry(residentialWidth, residentialHeight, residentialDepth);
  const residentialMaterial = new THREE.MeshLambertMaterial({ color: baseColor });
  const residential = new THREE.Mesh(residentialGeometry, residentialMaterial);
  residential.position.y = commercialHeight + residentialHeight / 2;
  residential.castShadow = true;
  residential.receiveShadow = true;
  buildingGroup.add(residential);

  // 窗户
  createWindows(buildingGroup, width, commercialHeight, depth, 0x87CEEB, 0);
  createWindows(buildingGroup, residentialWidth, residentialHeight, residentialDepth, 0xFFFFE0, commercialHeight);

  // 阳台
  for (let floor = 1; floor < Math.floor(residentialHeight / 3); floor++) {
    const balconyGeometry = new THREE.BoxGeometry(residentialWidth + 0.4, 0.1, 1);
    const balconyMaterial = new THREE.MeshLambertMaterial({ color: 0xD3D3D3 });
    const balcony = new THREE.Mesh(balconyGeometry, balconyMaterial);
    balcony.position.set(0, commercialHeight + floor * 3, residentialDepth/2 + 0.5);
    buildingGroup.add(balcony);
  }

  return buildingGroup;
}

// 创建现代摩天大楼
function createModernSkyscraper(baseColor, maxHeight) {
  const buildingGroup = new THREE.Group();
  const height = 15 + Math.random() * (maxHeight - 15);
  const width = 5 + Math.random() * 3;
  const depth = 5 + Math.random() * 3;

  // 主塔楼
  const towerGeometry = new THREE.BoxGeometry(width, height, depth);
  const towerMaterial = new THREE.MeshLambertMaterial({
    color: baseColor,
    transparent: true,
    opacity: 0.95
  });
  const tower = new THREE.Mesh(towerGeometry, towerMaterial);
  tower.position.y = height / 2;
  tower.castShadow = true;
  tower.receiveShadow = true;
  buildingGroup.add(tower);

  // 玻璃幕墙效果
  const glassGeometry = new THREE.BoxGeometry(width + 0.1, height, depth + 0.1);
  const glassMaterial = new THREE.MeshLambertMaterial({
    color: 0x87CEEB,
    transparent: true,
    opacity: 0.3
  });
  const glass = new THREE.Mesh(glassGeometry, glassMaterial);
  glass.position.y = height / 2;
  buildingGroup.add(glass);

  // 顶部天线
  const antennaGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3, 8);
  const antennaMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 });
  const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
  antenna.position.y = height + 1.5;
  buildingGroup.add(antenna);

  // 现代化窗户网格
  createModernWindows(buildingGroup, width, height, depth);

  // 裙楼
  const podiumGeometry = new THREE.BoxGeometry(width + 2, 5, depth + 2);
  const podiumMaterial = new THREE.MeshLambertMaterial({
    color: new THREE.Color(baseColor).multiplyScalar(0.8)
  });
  const podium = new THREE.Mesh(podiumGeometry, podiumMaterial);
  podium.position.y = 2.5;
  podium.castShadow = true;
  podium.receiveShadow = true;
  buildingGroup.add(podium);

  return buildingGroup;
}

// 创建工业建筑
function createIndustrialBuilding(baseColor, maxHeight) {
  const buildingGroup = new THREE.Group();
  const height = 6 + Math.random() * (maxHeight - 6);
  const width = 6 + Math.random() * 4;
  const depth = 8 + Math.random() * 4;

  // 主厂房
  const mainGeometry = new THREE.BoxGeometry(width, height, depth);
  const mainMaterial = new THREE.MeshLambertMaterial({ color: baseColor });
  const main = new THREE.Mesh(mainGeometry, mainMaterial);
  main.position.y = height / 2;
  main.castShadow = true;
  main.receiveShadow = true;
  buildingGroup.add(main);

  // 锯齿形屋顶
  const roofSegments = 3;
  for (let i = 0; i < roofSegments; i++) {
    const roofGeometry = new THREE.BoxGeometry(width / roofSegments, 1, depth);
    const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x708090 });
    const roofSegment = new THREE.Mesh(roofGeometry, roofMaterial);
    roofSegment.position.set(
      (i - roofSegments/2 + 0.5) * width / roofSegments,
      height + 0.5 + (i % 2) * 0.5,
      0
    );
    buildingGroup.add(roofSegment);
  }

  // 烟囱
  const chimneyGeometry = new THREE.CylinderGeometry(0.5, 0.5, 8, 8);
  const chimneyMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
  const chimney = new THREE.Mesh(chimneyGeometry, chimneyMaterial);
  chimney.position.set(width/3, height + 4, -depth/3);
  buildingGroup.add(chimney);

  // 工业窗户
  createIndustrialWindows(buildingGroup, width, height, depth);

  // 货物装卸区
  const loadingGeometry = new THREE.BoxGeometry(3, 3, 1);
  const loadingMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
  const loading = new THREE.Mesh(loadingGeometry, loadingMaterial);
  loading.position.set(0, 1.5, depth/2 + 0.5);
  buildingGroup.add(loading);

  return buildingGroup;
}

// 创建住宅建筑
function createResidentialBuilding(baseColor, maxHeight) {
  const buildingGroup = new THREE.Group();
  const height = 6 + Math.random() * (maxHeight - 6);
  const width = 4 + Math.random() * 2;
  const depth = 4 + Math.random() * 2;

  // 主体建筑
  const mainGeometry = new THREE.BoxGeometry(width, height, depth);
  const mainMaterial = new THREE.MeshLambertMaterial({ color: baseColor });
  const main = new THREE.Mesh(mainGeometry, mainMaterial);
  main.position.y = height / 2;
  main.castShadow = true;
  main.receiveShadow = true;
  buildingGroup.add(main);

  // 斜屋顶
  const roofGeometry = new THREE.ConeGeometry(Math.max(width, depth) * 0.7, 2, 4);
  const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.y = height + 1;
  roof.rotation.y = Math.PI / 4;
  buildingGroup.add(roof);

  // 住宅窗户
  createResidentialWindows(buildingGroup, width, height, depth);

  // 门
  const doorGeometry = new THREE.BoxGeometry(0.8, 2, 0.1);
  const doorMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
  const door = new THREE.Mesh(doorGeometry, doorMaterial);
  door.position.set(0, 1, depth/2 + 0.05);
  buildingGroup.add(door);

  // 小花园
  const gardenGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.1, 8);
  const gardenMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
  const garden = new THREE.Mesh(gardenGeometry, gardenMaterial);
  garden.position.set(width/2 + 1, 0.05, 0);
  buildingGroup.add(garden);

  return buildingGroup;
}

// 创建普通窗户
function createWindows(buildingGroup, width, height, depth, windowColor, yOffset = 0) {
  const windowWidth = 0.8;
  const windowHeight = 1.2;
  const windowDepth = 0.05;

  // 前后面窗户
  for (let floor = 1; floor < Math.floor(height / 3); floor++) {
    for (let col = 0; col < Math.floor(width / 1.5); col++) {
      // 前面窗户
      const frontWindowGeometry = new THREE.BoxGeometry(windowWidth, windowHeight, windowDepth);
      const frontWindowMaterial = new THREE.MeshLambertMaterial({ color: windowColor });
      const frontWindow = new THREE.Mesh(frontWindowGeometry, frontWindowMaterial);
      frontWindow.position.set(
        (col - Math.floor(width / 1.5) / 2 + 0.5) * 1.5,
        yOffset + floor * 3,
        depth/2 + windowDepth/2
      );
      buildingGroup.add(frontWindow);

      // 后面窗户
      const backWindow = frontWindow.clone();
      backWindow.position.z = -depth/2 - windowDepth/2;
      buildingGroup.add(backWindow);
    }
  }

  // 左右面窗户
  for (let floor = 1; floor < Math.floor(height / 3); floor++) {
    for (let col = 0; col < Math.floor(depth / 1.5); col++) {
      // 左面窗户
      const leftWindowGeometry = new THREE.BoxGeometry(windowDepth, windowHeight, windowWidth);
      const leftWindowMaterial = new THREE.MeshLambertMaterial({ color: windowColor });
      const leftWindow = new THREE.Mesh(leftWindowGeometry, leftWindowMaterial);
      leftWindow.position.set(
        -width/2 - windowDepth/2,
        yOffset + floor * 3,
        (col - Math.floor(depth / 1.5) / 2 + 0.5) * 1.5
      );
      buildingGroup.add(leftWindow);

      // 右面窗户
      const rightWindow = leftWindow.clone();
      rightWindow.position.x = width/2 + windowDepth/2;
      buildingGroup.add(rightWindow);
    }
  }
}

// 创建现代化窗户网格
function createModernWindows(buildingGroup, width, height, depth) {
  const windowSize = 0.6;
  const spacing = 1.2;

  // 创建玻璃幕墙网格
  for (let y = 2; y < height - 1; y += spacing) {
    for (let x = -width/2 + spacing/2; x < width/2; x += spacing) {
      // 前面
      const frontWindowGeometry = new THREE.BoxGeometry(windowSize, windowSize, 0.02);
      const frontWindowMaterial = new THREE.MeshLambertMaterial({
        color: 0x87CEEB,
        transparent: true,
        opacity: 0.8
      });
      const frontWindow = new THREE.Mesh(frontWindowGeometry, frontWindowMaterial);
      frontWindow.position.set(x, y, depth/2 + 0.01);
      buildingGroup.add(frontWindow);

      // 后面
      const backWindow = frontWindow.clone();
      backWindow.position.z = -depth/2 - 0.01;
      buildingGroup.add(backWindow);
    }

    for (let z = -depth/2 + spacing/2; z < depth/2; z += spacing) {
      // 左面
      const leftWindowGeometry = new THREE.BoxGeometry(0.02, windowSize, windowSize);
      const leftWindowMaterial = new THREE.MeshLambertMaterial({
        color: 0x87CEEB,
        transparent: true,
        opacity: 0.8
      });
      const leftWindow = new THREE.Mesh(leftWindowGeometry, leftWindowMaterial);
      leftWindow.position.set(-width/2 - 0.01, y, z);
      buildingGroup.add(leftWindow);

      // 右面
      const rightWindow = leftWindow.clone();
      rightWindow.position.x = width/2 + 0.01;
      buildingGroup.add(rightWindow);
    }
  }
}

// 创建工业窗户
function createIndustrialWindows(buildingGroup, width, height, depth) {
  const windowWidth = 2;
  const windowHeight = 1.5;

  // 大型工业窗户
  for (let i = 0; i < 2; i++) {
    const windowGeometry = new THREE.BoxGeometry(windowWidth, windowHeight, 0.05);
    const windowMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
    const window = new THREE.Mesh(windowGeometry, windowMaterial);
    window.position.set(
      (i - 0.5) * width * 0.6,
      height * 0.6,
      depth/2 + 0.025
    );
    buildingGroup.add(window);
  }

  // 侧面通风窗
  for (let i = 0; i < 3; i++) {
    const ventGeometry = new THREE.BoxGeometry(0.05, 0.5, 1);
    const ventMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F });
    const vent = new THREE.Mesh(ventGeometry, ventMaterial);
    vent.position.set(
      width/2 + 0.025,
      height * 0.3 + i * 1.5,
      0
    );
    buildingGroup.add(vent);
  }
}

// 创建住宅窗户
function createResidentialWindows(buildingGroup, width, height, depth) {
  const floors = Math.floor(height / 3);

  for (let floor = 0; floor < floors; floor++) {
    // 前面窗户
    for (let i = 0; i < 2; i++) {
      const windowGeometry = new THREE.BoxGeometry(0.8, 1, 0.05);
      const windowMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFE0 });
      const window = new THREE.Mesh(windowGeometry, windowMaterial);
      window.position.set(
        (i - 0.5) * width * 0.6,
        1.5 + floor * 3,
        depth/2 + 0.025
      );
      buildingGroup.add(window);

      // 窗框
      const frameGeometry = new THREE.BoxGeometry(0.9, 1.1, 0.02);
      const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
      const frame = new THREE.Mesh(frameGeometry, frameMaterial);
      frame.position.set(
        (i - 0.5) * width * 0.6,
        1.5 + floor * 3,
        depth/2 + 0.04
      );
      buildingGroup.add(frame);
    }
  }
}

// 创建道路网络
function createRoadNetwork() {
  const roads = [
    // 珠海大道
    { start: { x: -40, z: 0 }, end: { x: 40, z: 0 }, width: 2 },
    // 情侣路
    { start: { x: 10, z: -20 }, end: { x: 30, z: 30 }, width: 1.5 },
    // 港珠澳大桥连接线
    { start: { x: 15, z: 20 }, end: { x: 25, z: 15 }, width: 1.8 },
    // 横琴大桥
    { start: { x: 10, z: 25 }, end: { x: 0, z: 30 }, width: 1.5 },
    // 金湾大道
    { start: { x: -40, z: 20 }, end: { x: -20, z: 20 }, width: 1.2 }
  ];

  roads.forEach((road, index) => {
    const distance = Math.sqrt(
      Math.pow(road.end.x - road.start.x, 2) +
      Math.pow(road.end.z - road.start.z, 2)
    );

    const geometry = new THREE.BoxGeometry(distance, 0.1, road.width);
    const material = new THREE.MeshLambertMaterial({ color: 0xf1f5f9 });

    const roadMesh = new THREE.Mesh(geometry, material);
    roadMesh.position.set(
      (road.start.x + road.end.x) / 2,
      0.05,
      (road.start.z + road.end.z) / 2
    );

    const angle = Math.atan2(road.end.z - road.start.z, road.end.x - road.start.x);
    roadMesh.rotation.y = angle;

    roadMesh.userData = { type: 'road', index: index };
    scene.add(roadMesh);
  });
}

// 创建地标建筑
function createLandmarks() {
  // 珠海大剧院 - 现代艺术建筑
  createZhuhaiGrandTheatre(25, 15);

  // 珠海渔女 - 标志性雕塑
  createFisherGirlStatue(30, 25);

  // 圆明新园 - 古典园林建筑群
  createYuanmingNewPark(5, 35);

  // 长隆海洋王国 - 主题公园建筑
  createChimelongOceanKingdom(-5, 35);

  // 珠海机场 - 现代航站楼
  createZhuhaiAirport(-30, 20);
}

// 创建珠海大剧院
function createZhuhaiGrandTheatre(x, z) {
  const theatreGroup = new THREE.Group();

  // 主体建筑 - 贝壳形状
  const mainGeometry = new THREE.SphereGeometry(4, 16, 8, 0, Math.PI * 2, 0, Math.PI * 0.7);
  const mainMaterial = new THREE.MeshLambertMaterial({
    color: 0x8b5cf6,
    transparent: true,
    opacity: 0.9
  });
  const main = new THREE.Mesh(mainGeometry, mainMaterial);
  main.position.y = 4;
  main.castShadow = true;
  main.receiveShadow = true;
  theatreGroup.add(main);

  // 玻璃幕墙
  const glassGeometry = new THREE.SphereGeometry(4.1, 16, 8, 0, Math.PI * 2, 0, Math.PI * 0.7);
  const glassMaterial = new THREE.MeshLambertMaterial({
    color: 0x87CEEB,
    transparent: true,
    opacity: 0.3
  });
  const glass = new THREE.Mesh(glassGeometry, glassMaterial);
  glass.position.y = 4;
  theatreGroup.add(glass);

  // 入口大厅
  const entranceGeometry = new THREE.BoxGeometry(6, 3, 2);
  const entranceMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
  const entrance = new THREE.Mesh(entranceGeometry, entranceMaterial);
  entrance.position.set(0, 1.5, 3);
  entrance.castShadow = true;
  theatreGroup.add(entrance);

  // 装饰柱子
  for (let i = 0; i < 4; i++) {
    const columnGeometry = new THREE.CylinderGeometry(0.3, 0.3, 3, 8);
    const columnMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
    const column = new THREE.Mesh(columnGeometry, columnMaterial);
    column.position.set((i - 1.5) * 1.5, 1.5, 4);
    theatreGroup.add(column);
  }

  theatreGroup.position.set(x, 0, z);
  theatreGroup.userData = { type: 'landmark', name: '珠海大剧院' };
  scene.add(theatreGroup);
}

// 创建珠海渔女雕塑
function createFisherGirlStatue(x, z) {
  const statueGroup = new THREE.Group();

  // 基座
  const baseGeometry = new THREE.CylinderGeometry(3, 3, 1, 8);
  const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
  const base = new THREE.Mesh(baseGeometry, baseMaterial);
  base.position.y = 0.5;
  base.castShadow = true;
  base.receiveShadow = true;
  statueGroup.add(base);

  // 雕塑主体
  const bodyGeometry = new THREE.CylinderGeometry(0.8, 1.2, 5, 8);
  const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x06b6d4 });
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
  body.position.y = 3.5;
  body.castShadow = true;
  statueGroup.add(body);

  // 头部
  const headGeometry = new THREE.SphereGeometry(0.6, 8, 8);
  const headMaterial = new THREE.MeshLambertMaterial({ color: 0x06b6d4 });
  const head = new THREE.Mesh(headGeometry, headMaterial);
  head.position.y = 6.5;
  head.castShadow = true;
  statueGroup.add(head);

  // 手臂（举起的珍珠）
  const armGeometry = new THREE.CylinderGeometry(0.2, 0.3, 2, 6);
  const armMaterial = new THREE.MeshLambertMaterial({ color: 0x06b6d4 });
  const arm = new THREE.Mesh(armGeometry, armMaterial);
  arm.position.set(1, 7, 0);
  arm.rotation.z = -Math.PI / 4;
  statueGroup.add(arm);

  // 珍珠
  const pearlGeometry = new THREE.SphereGeometry(0.3, 8, 8);
  const pearlMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
  const pearl = new THREE.Mesh(pearlGeometry, pearlMaterial);
  pearl.position.set(1.8, 8.2, 0);
  statueGroup.add(pearl);

  statueGroup.position.set(x, 0, z);
  statueGroup.userData = { type: 'landmark', name: '珠海渔女' };
  scene.add(statueGroup);
}

// 创建圆明新园
function createYuanmingNewPark(x, z) {
  const parkGroup = new THREE.Group();

  // 主殿
  const mainHallGeometry = new THREE.BoxGeometry(8, 6, 6);
  const mainHallMaterial = new THREE.MeshLambertMaterial({ color: 0xf59e0b });
  const mainHall = new THREE.Mesh(mainHallGeometry, mainHallMaterial);
  mainHall.position.y = 3;
  mainHall.castShadow = true;
  mainHall.receiveShadow = true;
  parkGroup.add(mainHall);

  // 中式屋顶
  const roofGeometry = new THREE.ConeGeometry(6, 2, 4);
  const roofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.y = 7;
  roof.rotation.y = Math.PI / 4;
  parkGroup.add(roof);

  // 侧殿
  for (let i = 0; i < 2; i++) {
    const sideHallGeometry = new THREE.BoxGeometry(4, 4, 4);
    const sideHallMaterial = new THREE.MeshLambertMaterial({ color: 0xDAA520 });
    const sideHall = new THREE.Mesh(sideHallGeometry, sideHallMaterial);
    sideHall.position.set((i - 0.5) * 10, 2, 0);
    sideHall.castShadow = true;
    parkGroup.add(sideHall);

    // 侧殿屋顶
    const sideRoofGeometry = new THREE.ConeGeometry(3, 1.5, 4);
    const sideRoofMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
    const sideRoof = new THREE.Mesh(sideRoofGeometry, sideRoofMaterial);
    sideRoof.position.set((i - 0.5) * 10, 5, 0);
    sideRoof.rotation.y = Math.PI / 4;
    parkGroup.add(sideRoof);
  }

  // 装饰柱子
  for (let i = 0; i < 6; i++) {
    const columnGeometry = new THREE.CylinderGeometry(0.3, 0.3, 6, 8);
    const columnMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 });
    const column = new THREE.Mesh(columnGeometry, columnMaterial);
    column.position.set((i - 2.5) * 1.5, 3, 3.5);
    parkGroup.add(column);
  }

  parkGroup.position.set(x, 0, z);
  parkGroup.userData = { type: 'landmark', name: '圆明新园' };
  scene.add(parkGroup);
}

// 创建长隆海洋王国
function createChimelongOceanKingdom(x, z) {
  const parkGroup = new THREE.Group();

  // 主城堡
  const castleGeometry = new THREE.CylinderGeometry(3, 4, 10, 8);
  const castleMaterial = new THREE.MeshLambertMaterial({ color: 0xec4899 });
  const castle = new THREE.Mesh(castleGeometry, castleMaterial);
  castle.position.y = 5;
  castle.castShadow = true;
  castle.receiveShadow = true;
  parkGroup.add(castle);

  // 城堡顶部
  const topGeometry = new THREE.ConeGeometry(3.5, 3, 8);
  const topMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
  const top = new THREE.Mesh(topGeometry, topMaterial);
  top.position.y = 11.5;
  parkGroup.add(top);

  // 侧塔
  for (let i = 0; i < 4; i++) {
    const angle = (i / 4) * Math.PI * 2;
    const towerGeometry = new THREE.CylinderGeometry(1, 1.5, 6, 6);
    const towerMaterial = new THREE.MeshLambertMaterial({ color: 0xFFB6C1 });
    const tower = new THREE.Mesh(towerGeometry, towerMaterial);
    tower.position.set(
      Math.cos(angle) * 5,
      3,
      Math.sin(angle) * 5
    );
    tower.castShadow = true;
    parkGroup.add(tower);

    // 塔顶
    const towerTopGeometry = new THREE.ConeGeometry(1.2, 2, 6);
    const towerTopMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
    const towerTop = new THREE.Mesh(towerTopGeometry, towerTopMaterial);
    towerTop.position.set(
      Math.cos(angle) * 5,
      7,
      Math.sin(angle) * 5
    );
    parkGroup.add(towerTop);
  }

  // 装饰旗帜
  const flagGeometry = new THREE.BoxGeometry(0.1, 2, 1);
  const flagMaterial = new THREE.MeshLambertMaterial({ color: 0xFF1493 });
  const flag = new THREE.Mesh(flagGeometry, flagMaterial);
  flag.position.set(0, 14, 0);
  parkGroup.add(flag);

  parkGroup.position.set(x, 0, z);
  parkGroup.userData = { type: 'landmark', name: '长隆海洋王国' };
  scene.add(parkGroup);
}

// 创建珠海机场
function createZhuhaiAirport(x, z) {
  const airportGroup = new THREE.Group();

  // 主航站楼
  const terminalGeometry = new THREE.BoxGeometry(12, 4, 8);
  const terminalMaterial = new THREE.MeshLambertMaterial({ color: 0x7c3aed });
  const terminal = new THREE.Mesh(terminalGeometry, terminalMaterial);
  terminal.position.y = 2;
  terminal.castShadow = true;
  terminal.receiveShadow = true;
  airportGroup.add(terminal);

  // 弧形屋顶
  const roofGeometry = new THREE.CylinderGeometry(8, 8, 12, 16, 1, false, 0, Math.PI);
  const roofMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.y = 4;
  roof.rotation.z = Math.PI / 2;
  airportGroup.add(roof);

  // 控制塔
  const towerGeometry = new THREE.CylinderGeometry(1, 1, 8, 8);
  const towerMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
  const tower = new THREE.Mesh(towerGeometry, towerMaterial);
  tower.position.set(8, 4, 0);
  tower.castShadow = true;
  airportGroup.add(tower);

  // 控制室
  const controlGeometry = new THREE.BoxGeometry(2, 1.5, 2);
  const controlMaterial = new THREE.MeshLambertMaterial({ color: 0x87CEEB });
  const control = new THREE.Mesh(controlGeometry, controlMaterial);
  control.position.set(8, 8.75, 0);
  airportGroup.add(control);

  // 跑道标记
  const runwayGeometry = new THREE.BoxGeometry(20, 0.1, 3);
  const runwayMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F4F });
  const runway = new THREE.Mesh(runwayGeometry, runwayMaterial);
  runway.position.set(0, 0.05, -10);
  airportGroup.add(runway);

  // 跑道中线
  for (let i = 0; i < 10; i++) {
    const lineGeometry = new THREE.BoxGeometry(1, 0.11, 0.2);
    const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
    const line = new THREE.Mesh(lineGeometry, lineMaterial);
    line.position.set((i - 4.5) * 2, 0.06, -10);
    airportGroup.add(line);
  }

  airportGroup.position.set(x, 0, z);
  airportGroup.userData = { type: 'landmark', name: '珠海机场' };
  scene.add(airportGroup);
}

// 创建空域管制区
function createAirspaceZones() {
  const airspaceZones = [
    { name: '机场管制区', x: -30, z: 20, width: 20, height: 30, depth: 15, color: 0x06b6d4 },
    { name: '大桥空域', x: 20, z: 17, width: 15, height: 20, depth: 10, color: 0xfbbf24 },
    { name: '横琴限制区', x: 0, z: 30, width: 18, height: 25, depth: 12, color: 0xf97316 }
  ];

  airspaceZones.forEach(zone => {
    const geometry = new THREE.BoxGeometry(zone.width, zone.height, zone.depth);
    const material = new THREE.MeshLambertMaterial({
      color: zone.color,
      transparent: true,
      opacity: 0.2,
      wireframe: false
    });

    const airspace = new THREE.Mesh(geometry, material);
    airspace.position.set(zone.x, zone.height / 2, zone.z);
    airspace.userData = {
      type: 'airspace',
      name: zone.name
    };

    scene.add(airspace);
    airspaceObjects.push(airspace);

    // 添加边框
    const edges = new THREE.EdgesGeometry(geometry);
    const lineMaterial = new THREE.LineBasicMaterial({ color: zone.color });
    const wireframe = new THREE.LineSegments(edges, lineMaterial);
    wireframe.position.copy(airspace.position);
    scene.add(wireframe);
    airspaceObjects.push(wireframe);
  });
}

// 创建区域标记
function createAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    // 创建标记柱
    const geometry = new THREE.CylinderGeometry(0.5, 0.5, 5, 8);
    const material = new THREE.MeshLambertMaterial({ color: 0x06b6d4 });

    const marker = new THREE.Mesh(geometry, material);
    marker.position.set(area.position.x, 2.5, area.position.z);
    marker.castShadow = true;
    marker.userData = {
      type: 'marker',
      area: area.name
    };

    scene.add(marker);
    labelObjects.push(marker);

    // 创建标记顶部球体
    const sphereGeometry = new THREE.SphereGeometry(1, 8, 8);
    const sphereMaterial = new THREE.MeshLambertMaterial({ color: 0xffd700 });

    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    sphere.position.set(area.position.x, 6, area.position.z);
    sphere.castShadow = true;
    sphere.userData = {
      type: 'marker_top',
      area: area.name
    };

    scene.add(sphere);
    labelObjects.push(sphere);
  });
}

// 更新场景对象计数
function updateSceneObjectCount() {
  sceneObjectCount.value = scene.children.length;
}

// 启动渲染循环
function startRenderLoop() {
  const clock = new THREE.Clock();

  function animate() {
    animationId = requestAnimationFrame(animate);

    const deltaTime = clock.getDelta();
    const currentTime = performance.now();

    // 更新控制器
    if (controls) {
      controls.update();
    }

    // 更新相机信息
    if (camera) {
      cameraPosition.value = {
        x: camera.position.x,
        y: camera.position.y,
        z: camera.position.z
      };

      const target = controls ? controls.target : new THREE.Vector3(0, 0, 0);
      cameraTarget.value = {
        x: target.x,
        y: target.y,
        z: target.z
      };
    }

    // 动画效果
    if (animationEnabled.value) {
      updateAnimations(currentTime);
    }

    // 渲染场景
    const renderStart = performance.now();
    if (renderer && scene && camera) {
      renderer.render(scene, camera);
    }
    renderTime.value = performance.now() - renderStart;

    // 更新性能统计
    updatePerformanceStats(currentTime);
  }

  animate();
}

// 更新动画
function updateAnimations(time) {
  // 海洋波动效果
  const oceanMesh = scene.children.find(child => child.userData.type === 'ocean');
  if (oceanMesh) {
    oceanMesh.material.opacity = 0.7 + Math.sin(time * 0.001) * 0.1;
  }

  // 预警区域脉冲效果
  weatherObjects.forEach(obj => {
    if (obj.userData.type === 'warning') {
      const scale = 1 + Math.sin(time * 0.003 + obj.userData.id) * 0.2;
      obj.scale.set(scale, 1, scale);
    }
  });

  // 地标建筑旋转效果
  scene.children.forEach(child => {
    if (child.userData.type === 'landmark' && child.userData.name === '珠海大剧院') {
      child.rotation.y = time * 0.0005;
    }
  });
}

// 更新性能统计
function updatePerformanceStats(currentTime) {
  frameCount++;

  if (currentTime - lastTime >= 1000) {
    fps.value = Math.round((frameCount * 1000) / (currentTime - lastTime));
    frameCount = 0;
    lastTime = currentTime;

    // 更新三角形数量
    if (renderer) {
      triangleCount.value = renderer.info.render.triangles;
    }

    // 估算内存使用
    if (renderer) {
      const info = renderer.info;
      memoryUsage.value = (info.memory.geometries + info.memory.textures) * 0.001; // 转换为MB
    }
  }
}

// 窗口大小变化处理
function onWindowResize() {
  if (camera && renderer) {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  }
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 3;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 1000);
  }
}

// 生成新预警
function generateNewWarning() {
  if (!isSystemReady.value) return;

  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷风'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetX = (Math.random() - 0.5) * 10;
  const offsetZ = (Math.random() - 0.5) * 10;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    position: {
      x: selectedArea.position.x + offsetX,
      y: 0,
      z: selectedArea.position.z + offsetZ
    },
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherObject(warning);
  updateSceneObjectCount();

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherObject(oldWarning.id);
  }
}

// 生成严重预警
function generateSevereWarning() {
  if (!isSystemReady.value) return;

  const warningTypes = ['龙卷风', '冰雹', '暴雨', '大风'];
  const warningLevels = ['red', 'orange'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetX = (Math.random() - 0.5) * 10;
  const offsetZ = (Math.random() - 0.5) * 10;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    position: {
      x: selectedArea.position.x + offsetX,
      y: 0,
      z: selectedArea.position.z + offsetZ
    },
    intensity: Math.floor(Math.random() * 2) + 2,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherObject(warning);
  updateSceneObjectCount();

  console.log(`🚨 新增严重${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 严重${warning.type}预警 - ${warning.location}`, 'error');

  setTimeout(() => {
    focusOnWarning(warning);
  }, 500);

  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherObject(oldWarning.id);
  }
}

// 创建预警3D对象
function createWeatherObject(warning) {
  if (!scene) return;

  const color = getWarningColorHex(warning.level);
  const height = warning.intensity * 5 + 10;
  const radius = warning.intensity * 2 + 3;

  // 创建预警圆柱体
  const geometry = new THREE.CylinderGeometry(radius, radius * 1.5, height, 16);
  const material = new THREE.MeshLambertMaterial({
    color: color,
    transparent: true,
    opacity: 0.6
  });

  const warningMesh = new THREE.Mesh(geometry, material);
  warningMesh.position.set(
    warning.position.x,
    height / 2,
    warning.position.z
  );
  warningMesh.userData = {
    type: 'warning',
    id: warning.id,
    warningType: warning.type,
    level: warning.level
  };

  scene.add(warningMesh);
  weatherObjects.push(warningMesh);

  // 添加预警顶部指示器
  const topGeometry = new THREE.SphereGeometry(1, 8, 8);
  const topMaterial = new THREE.MeshLambertMaterial({ color: color });

  const topMesh = new THREE.Mesh(topGeometry, topMaterial);
  topMesh.position.set(
    warning.position.x,
    height + 1,
    warning.position.z
  );
  topMesh.userData = {
    type: 'warning_top',
    id: warning.id
  };

  scene.add(topMesh);
  weatherObjects.push(topMesh);
}

// 移除预警对象
function removeWeatherObject(warningId) {
  if (!scene) return;

  const objectsToRemove = weatherObjects.filter(obj => obj.userData.id === warningId);
  objectsToRemove.forEach(obj => {
    scene.remove(obj);
    obj.geometry.dispose();
    obj.material.dispose();
  });

  weatherObjects = weatherObjects.filter(obj => obj.userData.id !== warningId);
  updateSceneObjectCount();
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];

  weatherObjects.forEach(obj => {
    if (scene) {
      scene.remove(obj);
      obj.geometry.dispose();
      obj.material.dispose();
    }
  });
  weatherObjects = [];
  updateSceneObjectCount();

  showNotification('🗑️ 已清除所有预警', 'info');
}

// 聚焦预警
function focusOnWarning(warning) {
  if (!camera || !controls) return;

  const targetPosition = new THREE.Vector3(
    warning.position.x,
    warning.position.y + 20,
    warning.position.z + 30
  );

  const targetLookAt = new THREE.Vector3(
    warning.position.x,
    warning.position.y,
    warning.position.z
  );

  // 平滑移动相机
  animateCameraTo(targetPosition, targetLookAt);

  showNotification(`📍 聚焦到 ${warning.location} ${warning.type}预警`, 'info');
}

// 飞行到区域
function flyToArea(area) {
  if (!camera || !controls) return;

  const targetPosition = new THREE.Vector3(
    area.position.x,
    area.position.y + 25,
    area.position.z + 35
  );

  const targetLookAt = new THREE.Vector3(
    area.position.x,
    area.position.y,
    area.position.z
  );

  animateCameraTo(targetPosition, targetLookAt);

  showNotification(`📍 飞行到 ${area.name}`, 'info');
}

// 相机动画
function animateCameraTo(targetPosition, targetLookAt, duration = 2000) {
  if (!camera || !controls) return;

  const startPosition = camera.position.clone();
  const startLookAt = controls.target.clone();
  const startTime = Date.now();

  function animate() {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用缓动函数
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    // 插值位置
    camera.position.lerpVectors(startPosition, targetPosition, easeProgress);
    controls.target.lerpVectors(startLookAt, targetLookAt, easeProgress);

    controls.update();

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  }

  animate();
}

// 重置相机
function resetCamera() {
  if (!camera || !controls) return;

  const targetPosition = new THREE.Vector3(100, 80, 100);
  const targetLookAt = new THREE.Vector3(0, 0, 0);

  animateCameraTo(targetPosition, targetLookAt);

  showNotification('🎥 相机已重置', 'info');
}

// 设置相机视图
function setCameraView(viewType) {
  if (!camera || !controls) return;

  let targetPosition, targetLookAt;

  switch (viewType) {
    case 'overview':
      targetPosition = new THREE.Vector3(100, 80, 100);
      targetLookAt = new THREE.Vector3(0, 0, 0);
      break;
    case 'aerial':
      targetPosition = new THREE.Vector3(0, 150, 0);
      targetLookAt = new THREE.Vector3(0, 0, 0);
      break;
    case 'ground':
      targetPosition = new THREE.Vector3(50, 10, 50);
      targetLookAt = new THREE.Vector3(0, 5, 0);
      break;
    case 'side':
      targetPosition = new THREE.Vector3(0, 50, 100);
      targetLookAt = new THREE.Vector3(0, 0, 0);
      break;
    default:
      return;
  }

  animateCameraTo(targetPosition, targetLookAt);

  const viewNames = {
    overview: '总览视图',
    aerial: '航拍视图',
    ground: '地面视图',
    side: '侧视图'
  };

  showNotification(`📹 切换到${viewNames[viewType]}`, 'info');
}

// 切换线框模式
function toggleWireframe() {
  wireframeMode.value = !wireframeMode.value;

  scene.children.forEach(child => {
    if (child.material && child.userData.type !== 'ocean') {
      child.material.wireframe = wireframeMode.value;
    }
  });

  showNotification(`🔲 线框模式${wireframeMode.value ? '开启' : '关闭'}`, 'info');
}

// 切换光照效果
function toggleLighting() {
  lightingEnabled.value = !lightingEnabled.value;

  scene.children.forEach(child => {
    if (child.type === 'DirectionalLight' || child.type === 'AmbientLight' || child.type === 'HemisphereLight') {
      child.visible = lightingEnabled.value;
    }
  });

  showNotification(`💡 光照效果${lightingEnabled.value ? '开启' : '关闭'}`, 'info');
}

// 切换动画效果
function toggleAnimation() {
  animationEnabled.value = !animationEnabled.value;
  showNotification(`✨ 动画效果${animationEnabled.value ? '开启' : '关闭'}`, 'info');
}

// 图层控制函数
function toggleTerrain() {
  scene.children.forEach(child => {
    if (child.userData.type === 'terrain') {
      child.visible = layers.terrain;
    }
  });
  showNotification(`🏔️ 地形${layers.terrain ? '显示' : '隐藏'}`, 'info');
}

function toggleBuildings() {
  buildingObjects.forEach(obj => {
    obj.visible = layers.buildings;
  });
  showNotification(`🏢 建筑物${layers.buildings ? '显示' : '隐藏'}`, 'info');
}

function toggleWeather() {
  weatherObjects.forEach(obj => {
    obj.visible = layers.weather;
  });
  showNotification(`☁️ 气象预警${layers.weather ? '显示' : '隐藏'}`, 'info');
}

function toggleAirspace() {
  airspaceObjects.forEach(obj => {
    obj.visible = layers.airspace;
  });
  showNotification(`✈️ 低空空域${layers.airspace ? '显示' : '隐藏'}`, 'info');
}

function toggleLabels() {
  labelObjects.forEach(obj => {
    obj.visible = layers.labels;
  });
  showNotification(`🏷️ 区域标签${layers.labels ? '显示' : '隐藏'}`, 'info');
}

// 自动更新控制
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (Math.random() < 0.3) {
        generateNewWarning();
      }
    }, 8000);
    showNotification('▶️ 自动更新已开启', 'info');
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
    showNotification('⏸️ 自动更新已暂停', 'info');
  }
}

// 工具函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊',
    '龙卷风': '🌪️'
  };
  return icons[type] || '⚠️';
}

function getWarningColorHex(level) {
  const colors = {
    'red': 0xff0000,
    'orange': 0xff8800,
    'yellow': 0xffff00,
    'blue': 0x0088ff
  };
  return colors[level] || 0x888888;
}

function getWarningLevelText(level) {
  const texts = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(w => w.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理Three.js系统资源...');

  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }

  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }

  if (renderer) {
    renderer.dispose();
    renderer = null;
  }

  if (scene) {
    scene.clear();
    scene = null;
  }

  window.removeEventListener('resize', onWindowResize);

  // 清空对象数组
  weatherObjects = [];
  buildingObjects = [];
  airspaceObjects = [];
  labelObjects = [];

  console.log('✅ Three.js系统资源清理完成');
}
</script>

<style scoped>
.threejs-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  overflow: hidden;
}

.three-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.error {
  color: #FF6B6B;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red { 
  border-left: 4px solid #ff0000; 
  background: rgba(255, 0, 0, 0.1);
}
.level-orange { 
  border-left: 4px solid #ff8800; 
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow { 
  border-left: 4px solid #ffff00; 
  background: rgba(255, 255, 0, 0.1);
}
.level-blue { 
  border-left: 4px solid #0088ff; 
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 20px;
  min-width: 25px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 9px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 8px;
  color: #666;
}

.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

.warning-control, .scene-control, .layer-control {
  margin-bottom: 20px;
}

.control-btn, .scene-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled, .scene-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.severe {
  background: #FF6B35;
  color: white;
}

.control-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.active, .scene-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active), .scene-btn:not(.active) {
  background: #666;
  color: white;
}

.scene-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.scene-btn {
  padding: 6px 4px;
  font-size: 9px;
}

.layer-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.layer-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.layer-item input[type="checkbox"] {
  margin: 0;
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  max-width: 280px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 350px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.area-description {
  font-size: 9px;
  color: #999;
}

.camera-control-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-control-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.camera-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
  margin-bottom: 15px;
}

.camera-btn {
  padding: 8px 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
  background: #4a5568;
  color: white;
}

.camera-btn:hover {
  background: #2d3748;
}

.camera-info {
  font-size: 9px;
  color: #ccc;
}

.camera-item {
  margin-bottom: 3px;
}

.performance-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 180px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.performance-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.performance-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.perf-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.perf-label {
  color: #ccc;
}

.perf-value {
  font-weight: bold;
}

.perf-value.good {
  color: #90EE90;
}

.perf-value.medium {
  color: #FFA500;
}

.perf-value.poor {
  color: #FF6B6B;
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
