<template>
  <div class="cesium-weather-system">
    <!-- Cesium地图容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市低空三维空域动态气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">地图状态:</span>
          <span class="status-value" :class="mapStatus.class">{{ mapStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Cesium版本:</span>
          <span class="status-value" :class="cesiumVersion ? 'online' : 'error'">{{ cesiumVersion || '未加载' }}</span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 5)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="flyToWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          ➕ 生成新预警
        </button>
        <button @click="generateSevereWarning" class="control-btn severe" :disabled="!isSystemReady">
          🚨 生成严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }" :disabled="!isSystemReady">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更新' }}
        </button>
        <button @click="exportWarningData" class="control-btn export" :disabled="activeWarnings.length === 0">
          📊 导出预警数据
        </button>
      </div>

      <!-- 图层控制 -->
      <div class="layer-control">
        <h4>📊 图层控制</h4>
        <div class="layer-items">
          <label class="layer-item">
            <input type="checkbox" v-model="layers.terrain" @change="toggleTerrain">
            <span>🏔️ 地形</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.buildings" @change="toggleBuildings">
            <span>🏢 建筑物</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.weather" @change="toggleWeather">
            <span>☁️ 气象预警</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.airspace" @change="toggleAirspace">
            <span>✈️ 低空空域</span>
          </label>
          <label class="layer-item">
            <input type="checkbox" v-model="layers.labels" @change="toggleLabels">
            <span>🏷️ 区域标签</span>
          </label>
        </div>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions-panel">
      <h4>⚡ 快速操作</h4>
      <div class="quick-actions-grid">
        <button @click="flyToOverview" class="quick-btn overview" :disabled="!isSystemReady">
          🌍 总览
        </button>
        <button @click="focusOnSevereWarnings" class="quick-btn severe" :disabled="!hasSevereWarnings">
          🚨 严重预警
        </button>
        <button @click="toggleAllLayers" class="quick-btn layers">
          👁️ {{ allLayersVisible ? '隐藏图层' : '显示图层' }}
        </button>
        <button @click="resetView" class="quick-btn reset" :disabled="!isSystemReady">
          🔄 重置视图
        </button>
      </div>
    </div>

    <!-- 系统状态面板 -->
    <div class="status-panel">
      <div class="status-item">
        <span class="status-label">更新时间:</span>
        <span class="status-value">{{ lastUpdateTime }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">活跃预警:</span>
        <span class="status-value">{{ activeWarnings.length }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">严重预警:</span>
        <span class="status-value severe-count">{{ severeWarningsCount }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">覆盖区域:</span>
        <span class="status-value">珠海市全域</span>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';

// 动态导入Cesium
let Cesium = null;
let viewer = null;

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const lastUpdateTime = ref('');
const autoUpdate = ref(false);
const notification = ref(null);
const cesiumVersion = ref('');

// 系统状态
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const mapStatus = ref({ text: '加载中...', class: 'loading' });
const isSystemReady = computed(() => systemStatus.value.class === 'online' && mapStatus.value.class === 'online');

// 计算属性
const severeWarningsCount = computed(() => 
  activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange').length
);

const hasSevereWarnings = computed(() => severeWarningsCount.value > 0);

const allLayersVisible = computed(() => 
  layers.terrain && layers.buildings && layers.weather && layers.airspace && layers.labels
);

// 图层控制
const layers = ref({
  terrain: true,
  buildings: true,
  weather: true,
  airspace: true,
  labels: true
});

// 系统变量
let weatherEntities = [];
let buildingEntities = [];
let airspaceEntities = [];
let labelEntities = [];
let updateInterval = null;
let warningIdCounter = 0;

// 珠海市地理坐标
const ZHUHAI_BOUNDS = {
  center: {
    longitude: 113.5767,
    latitude: 22.2711,
    height: 25000
  }
};

// 珠海市重要区域
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2711,
    description: '珠海市中心区域'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.5200,
    latitude: 22.1300,
    description: '珠海经济特区'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3761,
    latitude: 22.0064,
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    longitude: 113.2500,
    latitude: 22.2000,
    description: '珠海农业区'
  },
  {
    name: '港珠澳大桥',
    icon: '🌉',
    longitude: 113.5400,
    latitude: 22.2100,
    description: '连接港澳的跨海大桥'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化CesiumJS气象预警系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
async function initializeSystem() {
  try {
    // 步骤1: 加载Cesium模块
    systemStatus.value = { text: '加载Cesium模块...', class: 'loading' };
    await loadCesiumModule();
    
    // 步骤2: 初始化Cesium Viewer
    systemStatus.value = { text: '初始化地球...', class: 'loading' };
    await initializeCesiumViewer();
    
    // 步骤3: 设置珠海地图
    mapStatus.value = { text: '设置珠海地图...', class: 'loading' };
    await setupZhuhaiMap();
    
    // 步骤4: 初始化预警系统
    systemStatus.value = { text: '初始化预警系统...', class: 'loading' };
    await initializeWeatherSystem();
    
    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };
    mapStatus.value = { text: '🟢 地图就绪', class: 'online' };
    
    console.log('✅ CesiumJS气象预警系统初始化完成');
    showNotification('✅ 系统初始化成功', 'success');
    
    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 2000);
    
  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    mapStatus.value = { text: '❌ 加载失败', class: 'error' };
    showNotification(`❌ 初始化失败: ${error.message}`, 'error');
  }
}

// 加载Cesium模块
async function loadCesiumModule() {
  try {
    console.log('开始导入Cesium模块...');
    Cesium = await import('cesium');
    cesiumVersion.value = Cesium.VERSION;
    console.log('Cesium模块导入成功，版本:', Cesium.VERSION);

    // 设置Cesium Ion访问令牌（使用公共令牌）
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';

    console.log('✅ Cesium模块加载成功');
  } catch (error) {
    console.error('Cesium模块加载详细错误:', error);
    throw new Error(`Cesium模块加载失败: ${error.message}`);
  }
}

// 初始化Cesium Viewer
async function initializeCesiumViewer() {
  try {
    console.log('开始创建Cesium Viewer...');

    // 创建Cesium Viewer（使用简化配置）
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 基础配置
      baseLayerPicker: true,
      geocoder: true,
      homeButton: true,
      sceneModePicker: true,
      navigationHelpButton: true,
      animation: false,
      timeline: false,
      fullscreenButton: true,
      vrButton: false,

      // 使用默认地形提供商（更稳定）
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),

      // 使用默认影像提供商
      imageryProvider: new Cesium.OpenStreetMapImageryProvider({
        url: 'https://a.tile.openstreetmap.org/'
      }),

      // 性能配置
      requestRenderMode: false,
      maximumRenderTimeChange: Infinity
    });

    // 添加多个影像图层以确保显示
    try {
      // 添加Bing Maps影像（备用）
      const bingProvider = new Cesium.BingMapsImageryProvider({
        url: 'https://dev.virtualearth.net',
        key: '', // 可以为空，使用默认
        mapStyle: Cesium.BingMapsStyle.AERIAL_WITH_LABELS
      });
      viewer.imageryLayers.addImageryProvider(bingProvider);
    } catch (e) {
      console.log('Bing Maps加载失败，使用默认影像');
    }

    // 禁用默认的双击行为
    viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);

    // 设置相机初始位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_BOUNDS.center.longitude,
        ZHUHAI_BOUNDS.center.latitude,
        ZHUHAI_BOUNDS.center.height
      ),
      orientation: {
        heading: 0.0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });

    // 设置地球的外观
    viewer.scene.globe.enableLighting = true;
    viewer.scene.globe.dynamicAtmosphereLighting = true;
    viewer.scene.globe.atmosphereLightIntensity = 10.0;
    viewer.scene.globe.showWaterEffect = true;

    // 设置大气效果
    viewer.scene.skyAtmosphere.show = true;
    viewer.scene.fog.enabled = true;
    viewer.scene.fog.density = 0.0001;

    console.log('✅ Cesium Viewer 创建成功');
  } catch (error) {
    console.error('Cesium Viewer 创建失败:', error);
    throw new Error(`地球初始化失败: ${error.message}`);
  }
}

// 设置珠海地图
async function setupZhuhaiMap() {
  try {
    console.log('设置珠海地图区域...');

    // 添加珠海市边界
    await addZhuhaiBoundary();

    // 添加珠海建筑模型
    await addZhuhaiBuildingModels();

    // 添加低空空域
    await addAirspaceZones();

    // 添加区域标记
    await addAreaLabels();

    console.log('✅ 珠海地图设置完成');
  } catch (error) {
    console.error('珠海地图设置失败:', error);
    throw new Error(`地图设置失败: ${error.message}`);
  }
}

// 添加珠海市边界
async function addZhuhaiBoundary() {
  // 珠海市简化边界
  const zhuhaiOutline = [
    113.2, 22.0,
    113.7, 22.0,
    113.7, 22.4,
    113.2, 22.4,
    113.2, 22.0
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(zhuhaiOutline),
      material: Cesium.Color.CYAN.withAlpha(0.1),
      outline: true,
      outlineColor: Cesium.Color.CYAN,
      height: 0
    }
  });
}

// 添加珠海建筑模型
async function addZhuhaiBuildingModels() {
  console.log('添加珠海建筑模型...');

  // 香洲区建筑群
  const xiangzhouBuildings = [
    { lng: 113.5767, lat: 22.2711, height: 150, name: '珠海中心大厦' },
    { lng: 113.5780, lat: 22.2720, height: 120, name: '香洲商务中心' },
    { lng: 113.5750, lat: 22.2700, height: 100, name: '珠海国际会展中心' },
    { lng: 113.5790, lat: 22.2730, height: 80, name: '香洲政府大楼' },
    { lng: 113.5740, lat: 22.2690, height: 90, name: '珠海大剧院' }
  ];

  // 横琴新区建筑群
  const hengqinBuildings = [
    { lng: 113.5200, lat: 22.1300, height: 200, name: '横琴金融中心' },
    { lng: 113.5220, lat: 22.1320, height: 180, name: '横琴科技大厦' },
    { lng: 113.5180, lat: 22.1280, height: 160, name: '横琴国际商务中心' },
    { lng: 113.5240, lat: 22.1340, height: 140, name: '横琴创新大厦' }
  ];

  // 金湾区建筑群
  const jinwanBuildings = [
    { lng: 113.3761, lat: 22.0064, height: 60, name: '珠海机场航站楼' },
    { lng: 113.3780, lat: 22.0080, height: 40, name: '金湾区政府' },
    { lng: 113.3740, lat: 22.0040, height: 50, name: '金湾商业中心' }
  ];

  // 创建建筑模型
  const allBuildings = [...xiangzhouBuildings, ...hengqinBuildings, ...jinwanBuildings];

  allBuildings.forEach((building, index) => {
    // 主建筑
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat),
      box: {
        dimensions: new Cesium.Cartesian3(
          50 + Math.random() * 30,  // 宽度
          50 + Math.random() * 30,  // 长度
          building.height           // 高度
        ),
        material: Cesium.Color.fromRandom({
          red: 0.3 + Math.random() * 0.4,
          green: 0.3 + Math.random() * 0.4,
          blue: 0.3 + Math.random() * 0.4,
          alpha: 0.8
        }),
        outline: true,
        outlineColor: Cesium.Color.WHITE.withAlpha(0.3)
      },
      label: {
        text: building.name,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -building.height/2 - 30),
        scale: 0.8,
        show: layers.labels
      }
    });

    buildingEntities.push(entity);

    // 添加周围的小建筑
    for (let i = 0; i < 3; i++) {
      const offsetLng = (Math.random() - 0.5) * 0.005;
      const offsetLat = (Math.random() - 0.5) * 0.005;
      const smallHeight = 20 + Math.random() * 40;

      const smallBuilding = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(
          building.lng + offsetLng,
          building.lat + offsetLat
        ),
        box: {
          dimensions: new Cesium.Cartesian3(
            15 + Math.random() * 20,
            15 + Math.random() * 20,
            smallHeight
          ),
          material: Cesium.Color.fromRandom({
            red: 0.4 + Math.random() * 0.3,
            green: 0.4 + Math.random() * 0.3,
            blue: 0.4 + Math.random() * 0.3,
            alpha: 0.7
          }),
          outline: true,
          outlineColor: Cesium.Color.WHITE.withAlpha(0.2)
        }
      });

      buildingEntities.push(smallBuilding);
    }
  });

  console.log('✅ 珠海建筑模型添加完成');
}

// 添加低空空域
async function addAirspaceZones() {
  console.log('添加低空空域...');

  // 珠海机场管制区
  const airportZone = [
    113.35, 21.98,
    113.40, 21.98,
    113.40, 22.03,
    113.35, 22.03
  ];

  const airportEntity = viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(airportZone),
      material: Cesium.Color.CYAN.withAlpha(0.3),
      outline: true,
      outlineColor: Cesium.Color.CYAN,
      height: 0,
      extrudedHeight: 3000
    },
    label: {
      text: '✈️ 珠海机场管制区\n高度限制: 3000米',
      position: Cesium.Cartesian3.fromDegrees(113.375, 22.005, 1500),
      font: '14pt Microsoft YaHei',
      fillColor: Cesium.Color.CYAN,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      show: layers.airspace
    }
  });

  airspaceEntities.push(airportEntity);

  // 港珠澳大桥空域
  const bridgeZone = [
    113.52, 22.20,
    113.56, 22.20,
    113.56, 22.23,
    113.52, 22.23
  ];

  const bridgeEntity = viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(bridgeZone),
      material: Cesium.Color.YELLOW.withAlpha(0.3),
      outline: true,
      outlineColor: Cesium.Color.YELLOW,
      height: 0,
      extrudedHeight: 1500
    },
    label: {
      text: '🌉 港珠澳大桥空域\n高度限制: 1500米',
      position: Cesium.Cartesian3.fromDegrees(113.54, 22.215, 750),
      font: '14pt Microsoft YaHei',
      fillColor: Cesium.Color.YELLOW,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      show: layers.airspace
    }
  });

  airspaceEntities.push(bridgeEntity);

  // 横琴新区限制区
  const hengqinZone = [
    113.50, 22.11,
    113.54, 22.11,
    113.54, 22.15,
    113.50, 22.15
  ];

  const hengqinEntity = viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(hengqinZone),
      material: Cesium.Color.ORANGE.withAlpha(0.3),
      outline: true,
      outlineColor: Cesium.Color.ORANGE,
      height: 0,
      extrudedHeight: 2000
    },
    label: {
      text: '🏗️ 横琴新区限制区\n高度限制: 2000米',
      position: Cesium.Cartesian3.fromDegrees(113.52, 22.13, 1000),
      font: '14pt Microsoft YaHei',
      fillColor: Cesium.Color.ORANGE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      show: layers.airspace
    }
  });

  airspaceEntities.push(hengqinEntity);

  console.log('✅ 低空空域添加完成');
}

// 添加区域标签
async function addAreaLabels() {
  console.log('添加区域标签...');

  zhuhaiAreas.value.forEach(area => {
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(area.longitude, area.latitude),
      point: {
        pixelSize: 15,
        color: Cesium.Color.CYAN,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        show: layers.labels
      },
      label: {
        text: `${area.icon} ${area.name}`,
        font: '14pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -40),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        show: layers.labels
      }
    });

    labelEntities.push(entity);
  });

  console.log('✅ 区域标签添加完成');
}

// 初始化预警系统
async function initializeWeatherSystem() {
  try {
    console.log('初始化预警系统...');

    // 初始化预警数据
    activeWarnings.value = [];
    weatherEntities = [];
    warningIdCounter = 0;

    // 更新时间
    updateLastUpdateTime();

    console.log('✅ 预警系统初始化完成');
  } catch (error) {
    console.error('预警系统初始化失败:', error);
    throw new Error(`预警系统初始化失败: ${error.message}`);
  }
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 3;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 1000);
  }
}

// 生成新预警
function generateNewWarning() {
  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷风'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetLng = (Math.random() - 0.5) * 0.02;
  const offsetLat = (Math.random() - 0.5) * 0.02;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLng,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherEntity(warning);
  updateLastUpdateTime();

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherEntity(oldWarning.id);
  }
}

// 生成严重预警
function generateSevereWarning() {
  const warningTypes = ['龙卷风', '冰雹', '暴雨', '大风'];
  const warningLevels = ['red', 'orange'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetLng = (Math.random() - 0.5) * 0.02;
  const offsetLat = (Math.random() - 0.5) * 0.02;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLng,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 2) + 2,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherEntity(warning);
  updateLastUpdateTime();

  console.log(`🚨 新增严重${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 严重${warning.type}预警 - ${warning.location}`, 'error');

  setTimeout(() => {
    flyToWarning(warning);
  }, 500);

  if (activeWarnings.value.length > 10) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherEntity(oldWarning.id);
  }
}

// 创建预警实体
function createWeatherEntity(warning) {
  const color = getWarningColor(warning.level);
  const height = warning.intensity * 500 + 1000;

  // 主预警区域
  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    position: Cesium.Cartesian3.fromDegrees(warning.longitude, warning.latitude),
    cylinder: {
      length: height,
      topRadius: 800,
      bottomRadius: 1200,
      material: color.withAlpha(0.4),
      outline: true,
      outlineColor: color,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    label: {
      text: `${getWarningIcon(warning.type)} ${warning.type}预警\n${getWarningLevelText(warning.level)}\n${warning.location}`,
      font: '16pt Microsoft YaHei',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 3,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -height/2 - 50),
      scale: 1.0,
      show: layers.weather
    },
    point: {
      pixelSize: 20,
      color: color,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 3,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      show: layers.weather
    }
  });

  weatherEntities.push(entity);

  // 添加动画效果
  if (warning.level === 'red' || warning.level === 'orange') {
    addWarningAnimation(entity, warning);
  }
}

// 添加预警动画
function addWarningAnimation(entity, warning) {
  const startTime = viewer.clock.currentTime;
  const stopTime = Cesium.JulianDate.addSeconds(startTime, 30, new Cesium.JulianDate());

  // 创建脉冲动画
  entity.cylinder.material = new Cesium.ColorMaterialProperty(
    new Cesium.SampledProperty(Cesium.Color)
  );

  const color = getWarningColor(warning.level);
  const pulseProperty = entity.cylinder.material.color;

  for (let i = 0; i <= 30; i++) {
    const time = Cesium.JulianDate.addSeconds(startTime, i, new Cesium.JulianDate());
    const alpha = 0.2 + 0.3 * Math.sin(i * 0.5);
    pulseProperty.addSample(time, color.withAlpha(alpha));
  }
}

// 移除预警实体
function removeWeatherEntity(warningId) {
  const entityId = `warning-${warningId}`;
  const entity = viewer.entities.getById(entityId);
  if (entity) {
    viewer.entities.remove(entity);
  }

  weatherEntities = weatherEntities.filter(e => e.id !== entityId);
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];

  weatherEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  weatherEntities = [];

  updateLastUpdateTime();
  showNotification('🗑️ 已清除所有预警', 'info');
}

// 飞行到预警位置
function flyToWarning(warning) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      5000
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-30),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行到 ${warning.location} ${warning.type}预警`, 'info');
}

// 飞行到区域
function flyToArea(area) {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      8000
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行到 ${area.name}`, 'info');
}

// 飞行到总览
function flyToOverview() {
  if (!viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      ZHUHAI_BOUNDS.center.longitude,
      ZHUHAI_BOUNDS.center.latitude,
      ZHUHAI_BOUNDS.center.height
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3.0
  });

  showNotification('🌍 返回总览视图', 'info');
}

// 聚焦严重预警
function focusOnSevereWarnings() {
  const severeWarnings = activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange');
  if (severeWarnings.length === 0) return;

  const warning = severeWarnings[0];
  flyToWarning(warning);
}

// 重置视图
function resetView() {
  flyToOverview();
}

// 图层控制函数
function toggleTerrain() {
  // Cesium地形控制
  if (layers.terrain) {
    viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
  } else {
    Cesium.createWorldTerrainAsync().then(terrainProvider => {
      viewer.terrainProvider = terrainProvider;
    });
  }
  showNotification(`🏔️ 地形${layers.terrain ? '显示' : '隐藏'}`, 'info');
}

function toggleBuildings() {
  buildingEntities.forEach(entity => {
    entity.show = layers.buildings;
  });
  showNotification(`🏢 建筑物${layers.buildings ? '显示' : '隐藏'}`, 'info');
}

function toggleWeather() {
  weatherEntities.forEach(entity => {
    if (entity.cylinder) entity.cylinder.show = layers.weather;
    if (entity.point) entity.point.show = layers.weather;
    if (entity.label) entity.label.show = layers.weather;
  });
  showNotification(`☁️ 气象预警${layers.weather ? '显示' : '隐藏'}`, 'info');
}

function toggleAirspace() {
  airspaceEntities.forEach(entity => {
    entity.show = layers.airspace;
  });
  showNotification(`✈️ 低空空域${layers.airspace ? '显示' : '隐藏'}`, 'info');
}

function toggleLabels() {
  labelEntities.forEach(entity => {
    if (entity.label) entity.label.show = layers.labels;
    if (entity.point) entity.point.show = layers.labels;
  });

  buildingEntities.forEach(entity => {
    if (entity.label) entity.label.show = layers.labels;
  });

  showNotification(`🏷️ 区域标签${layers.labels ? '显示' : '隐藏'}`, 'info');
}

function toggleAllLayers() {
  const newState = !allLayersVisible.value;

  layers.terrain = newState;
  layers.buildings = newState;
  layers.weather = newState;
  layers.airspace = newState;
  layers.labels = newState;

  toggleTerrain();
  toggleBuildings();
  toggleWeather();
  toggleAirspace();
  toggleLabels();

  showNotification(`👁️ ${newState ? '显示' : '隐藏'}所有图层`, 'info');
}

// 自动更新控制
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (Math.random() < 0.3) {
        generateNewWarning();
      }
    }, 8000);
    showNotification('▶️ 自动更新已开启', 'info');
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
    showNotification('⏸️ 自动更新已暂停', 'info');
  }
}

// 导出预警数据
function exportWarningData() {
  const data = {
    exportTime: new Date().toISOString(),
    totalWarnings: activeWarnings.value.length,
    severeWarnings: severeWarningsCount.value,
    warnings: activeWarnings.value.map(w => ({
      id: w.id,
      type: w.type,
      level: w.level,
      location: w.location,
      coordinates: {
        longitude: w.longitude,
        latitude: w.latitude
      },
      intensity: w.intensity,
      time: w.time,
      timestamp: w.timestamp
    }))
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `珠海气象预警数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showNotification('📊 预警数据导出成功', 'success');
}

// 工具函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊',
    '龙卷风': '🌪️'
  };
  return icons[type] || '⚠️';
}

function getWarningColor(level) {
  const colors = {
    'red': Cesium.Color.RED,
    'orange': Cesium.Color.ORANGE,
    'yellow': Cesium.Color.YELLOW,
    'blue': Cesium.Color.BLUE
  };
  return colors[level] || Cesium.Color.WHITE;
}

function getWarningLevelText(level) {
  const texts = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(w => w.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

function updateLastUpdateTime() {
  lastUpdateTime.value = new Date().toLocaleTimeString();
}

function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理系统资源...');

  if (updateInterval) {
    clearInterval(updateInterval);
    updateInterval = null;
  }

  if (viewer) {
    viewer.destroy();
    viewer = null;
  }

  // 清空实体数组
  weatherEntities = [];
  buildingEntities = [];
  airspaceEntities = [];
  labelEntities = [];

  console.log('✅ 系统资源清理完成');
}
</script>

<style scoped>
.cesium-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.error {
  color: #FF6B6B;
}

.status-value.severe-count {
  color: #FF6B6B;
  font-weight: bold;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red { 
  border-left: 4px solid #ff0000; 
  background: rgba(255, 0, 0, 0.1);
}
.level-orange { 
  border-left: 4px solid #ff8800; 
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow { 
  border-left: 4px solid #ffff00; 
  background: rgba(255, 255, 0, 0.1);
}
.level-blue { 
  border-left: 4px solid #0088ff; 
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 24px;
  min-width: 30px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 10px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 9px;
  color: #666;
}

.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

.warning-control {
  margin-bottom: 20px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.severe {
  background: #FF6B35;
  color: white;
}

.control-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.export {
  background: #2196F3;
  color: white;
}

.control-btn.export:hover:not(:disabled) {
  background: #1976D2;
}

.control-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active) {
  background: #666;
  color: white;
}

.layer-control {
  margin-bottom: 20px;
}

.layer-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.layer-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.layer-item input[type="checkbox"] {
  margin: 0;
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  max-width: 250px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
}

.quick-actions-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.quick-actions-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-btn {
  padding: 8px 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-btn.overview {
  background: #4CAF50;
  color: white;
}

.quick-btn.overview:hover:not(:disabled) {
  background: #45a049;
}

.quick-btn.severe {
  background: #FF6B35;
  color: white;
}

.quick-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.quick-btn.layers {
  background: #2196F3;
  color: white;
}

.quick-btn.layers:hover:not(:disabled) {
  background: #1976D2;
}

.quick-btn.reset {
  background: #9C27B0;
  color: white;
}

.quick-btn.reset:hover:not(:disabled) {
  background: #7B1FA2;
}

.status-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
