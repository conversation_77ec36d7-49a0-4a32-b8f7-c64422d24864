<template>
  <div class="drone-control-panel">
    <div class="panel-header">
      <h2>🚁 无人机控制中心</h2>
    </div>

    <!-- 无人机选择 -->
    <div class="control-section">
      <h3>选择无人机</h3>
      <div class="drone-selector">
        <select v-model="selectedDroneId" @change="selectDrone" class="drone-select">
          <option value="">请选择无人机</option>
          <option 
            v-for="drone in droneList" 
            :key="drone.id" 
            :value="drone.id"
          >
            {{ drone.id }} - {{ drone.type }}
          </option>
        </select>
      </div>
    </div>

    <!-- 选中无人机状态 -->
    <div v-if="selectedDrone" class="control-section">
      <h3>无人机状态</h3>
      <div class="drone-status">
        <div class="status-grid">
          <div class="status-item">
            <span class="label">飞行模式:</span>
            <span class="value">{{ getFlightModeText(selectedDrone.flightMode) }}</span>
          </div>
          <div class="status-item">
            <span class="label">电池:</span>
            <span class="value" :class="getBatteryClass(selectedDrone.battery)">
              {{ selectedDrone.battery.toFixed(0) }}%
            </span>
          </div>
          <div class="status-item">
            <span class="label">高度:</span>
            <span class="value">{{ selectedDrone.altitude.toFixed(1) }}m</span>
          </div>
          <div class="status-item">
            <span class="label">速度:</span>
            <span class="value">{{ selectedDrone.speed.toFixed(1) }}m/s</span>
          </div>
          <div class="status-item">
            <span class="label">信号:</span>
            <span class="value" :class="getSignalClass(selectedDrone.communication?.signalStrength)">
              {{ selectedDrone.communication?.signalStrength?.toFixed(0) || 0 }}%
            </span>
          </div>
          <div class="status-item">
            <span class="label">延迟:</span>
            <span class="value">{{ selectedDrone.communication?.latency?.toFixed(0) || 0 }}ms</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 飞行控制 -->
    <div v-if="selectedDrone" class="control-section">
      <h3>飞行控制</h3>
      
      <!-- 基础控制 -->
      <div class="basic-controls">
        <button @click="takeoff" class="control-btn takeoff" :disabled="!canTakeoff">
          🛫 起飞
        </button>
        <button @click="land" class="control-btn land" :disabled="!canLand">
          🛬 降落
        </button>
        <button @click="returnHome" class="control-btn return">
          🏠 返航
        </button>
        <button @click="hover" class="control-btn hover">
          ⏸️ 悬停
        </button>
      </div>

      <!-- 方向控制 -->
      <div class="direction-controls">
        <div class="direction-grid">
          <div></div>
          <button @click="moveForward" class="dir-btn">⬆️</button>
          <div></div>
          <button @click="moveLeft" class="dir-btn">⬅️</button>
          <button @click="hover" class="dir-btn center">⏹️</button>
          <button @click="moveRight" class="dir-btn">➡️</button>
          <div></div>
          <button @click="moveBackward" class="dir-btn">⬇️</button>
          <div></div>
        </div>
        
        <div class="altitude-controls">
          <button @click="moveUp" class="alt-btn">🔺 上升</button>
          <button @click="moveDown" class="alt-btn">🔻 下降</button>
        </div>
        
        <div class="rotation-controls">
          <button @click="rotateLeft" class="rot-btn">↺ 左转</button>
          <button @click="rotateRight" class="rot-btn">↻ 右转</button>
        </div>
      </div>

      <!-- 参数设置 -->
      <div class="parameter-controls">
        <div class="param-item">
          <label>目标速度 (m/s):</label>
          <input 
            type="range" 
            v-model="targetSpeed" 
            @change="setSpeed"
            min="1" 
            max="20" 
            step="0.5"
            class="param-slider"
          />
          <span class="param-value">{{ targetSpeed }}</span>
        </div>
        
        <div class="param-item">
          <label>目标高度 (m):</label>
          <input 
            type="range" 
            v-model="targetAltitude" 
            @change="setAltitude"
            min="10" 
            max="500" 
            step="5"
            class="param-slider"
          />
          <span class="param-value">{{ targetAltitude }}</span>
        </div>
      </div>
    </div>

    <!-- 航点任务 -->
    <div v-if="selectedDrone" class="control-section">
      <h3>航点任务</h3>
      <div class="waypoint-controls">
        <div class="waypoint-list">
          <div 
            v-for="(waypoint, index) in waypoints" 
            :key="index"
            class="waypoint-item"
            :class="{ active: index === selectedDrone.currentWaypointIndex }"
          >
            <span class="waypoint-index">{{ index + 1 }}</span>
            <span class="waypoint-coords">
              {{ waypoint.lat.toFixed(4) }}, {{ waypoint.lng.toFixed(4) }}
            </span>
            <span class="waypoint-alt">{{ waypoint.altitude }}m</span>
            <button @click="removeWaypoint(index)" class="remove-btn">❌</button>
          </div>
        </div>
        
        <div class="waypoint-actions">
          <button @click="addCurrentPositionAsWaypoint" class="waypoint-btn">
            📍 添加当前位置
          </button>
          <button @click="startWaypointMission" class="waypoint-btn" :disabled="waypoints.length === 0">
            🎯 开始任务
          </button>
          <button @click="clearWaypoints" class="waypoint-btn">
            🗑️ 清空航点
          </button>
        </div>
      </div>
    </div>

    <!-- 传感器数据 -->
    <div v-if="selectedDrone && selectedDrone.sensors" class="control-section">
      <h3>传感器数据</h3>
      <div class="sensor-data">
        <div class="sensor-group">
          <h4>GPS</h4>
          <div class="sensor-item">
            <span>纬度: {{ selectedDrone.sensors.gps?.lat?.toFixed(6) || 'N/A' }}</span>
          </div>
          <div class="sensor-item">
            <span>经度: {{ selectedDrone.sensors.gps?.lng?.toFixed(6) || 'N/A' }}</span>
          </div>
          <div class="sensor-item">
            <span>精度: {{ selectedDrone.sensors.gps?.accuracy?.toFixed(1) || 'N/A' }}m</span>
          </div>
        </div>
        
        <div class="sensor-group">
          <h4>IMU</h4>
          <div class="sensor-item">
            <span>俯仰: {{ selectedDrone.sensors.imu?.pitch?.toFixed(1) || 0 }}°</span>
          </div>
          <div class="sensor-item">
            <span>横滚: {{ selectedDrone.sensors.imu?.roll?.toFixed(1) || 0 }}°</span>
          </div>
          <div class="sensor-item">
            <span>偏航: {{ selectedDrone.sensors.imu?.yaw?.toFixed(1) || 0 }}°</span>
          </div>
        </div>
        
        <div class="sensor-group">
          <h4>气压计</h4>
          <div class="sensor-item">
            <span>高度: {{ selectedDrone.sensors.barometer?.altitude?.toFixed(1) || 0 }}m</span>
          </div>
          <div class="sensor-item">
            <span>气压: {{ selectedDrone.sensors.barometer?.pressure?.toFixed(2) || 0 }}hPa</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 紧急控制 -->
    <div v-if="selectedDrone" class="control-section emergency">
      <h3>⚠️ 紧急控制</h3>
      <div class="emergency-controls">
        <button @click="emergencyStop" class="emergency-btn">
          🛑 紧急停止
        </button>
        <button @click="emergencyLand" class="emergency-btn">
          🚨 紧急降落
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { CONTROL_COMMANDS, FLIGHT_MODES } from '../utils/droneControlSystem.js';

// 定义事件
const emit = defineEmits([
  'drone-command',
  'select-drone',
  'waypoint-mission'
]);

// 接收props
const props = defineProps({
  droneList: {
    type: Array,
    default: () => []
  },
  selectedDroneData: {
    type: Object,
    default: null
  }
});

// 响应式数据
const selectedDroneId = ref('');
const selectedDrone = ref(null);
const targetSpeed = ref(10);
const targetAltitude = ref(100);
const waypoints = ref([]);

// 计算属性
const canTakeoff = computed(() => {
  return selectedDrone.value && selectedDrone.value.status === 'idle';
});

const canLand = computed(() => {
  return selectedDrone.value && selectedDrone.value.status === 'flying';
});

// 方法
function selectDrone() {
  if (selectedDroneId.value) {
    emit('select-drone', selectedDroneId.value);
    selectedDrone.value = props.selectedDroneData;
  }
}

function getFlightModeText(mode) {
  const modeMap = {
    [FLIGHT_MODES.MANUAL]: '手动控制',
    [FLIGHT_MODES.AUTO]: '自动巡航',
    [FLIGHT_MODES.WAYPOINT]: '航点飞行',
    [FLIGHT_MODES.RETURN_HOME]: '返航模式',
    [FLIGHT_MODES.HOVER]: '悬停模式',
    [FLIGHT_MODES.EMERGENCY]: '紧急模式'
  };
  return modeMap[mode] || '未知模式';
}

function getBatteryClass(battery) {
  if (battery > 50) return 'battery-good';
  if (battery > 20) return 'battery-medium';
  return 'battery-low';
}

function getSignalClass(signal) {
  if (signal > 70) return 'signal-good';
  if (signal > 30) return 'signal-medium';
  return 'signal-low';
}

// 基础控制命令
function takeoff() {
  emit('drone-command', CONTROL_COMMANDS.TAKEOFF, { altitude: targetAltitude.value });
}

function land() {
  emit('drone-command', CONTROL_COMMANDS.LAND);
}

function returnHome() {
  emit('drone-command', 'return_home');
}

function hover() {
  emit('drone-command', 'hover');
}

// 方向控制
function moveForward() {
  emit('drone-command', CONTROL_COMMANDS.MOVE_FORWARD, { distance: 20 });
}

function moveBackward() {
  emit('drone-command', CONTROL_COMMANDS.MOVE_BACKWARD, { distance: 20 });
}

function moveLeft() {
  emit('drone-command', CONTROL_COMMANDS.MOVE_LEFT, { distance: 20 });
}

function moveRight() {
  emit('drone-command', CONTROL_COMMANDS.MOVE_RIGHT, { distance: 20 });
}

function moveUp() {
  emit('drone-command', CONTROL_COMMANDS.MOVE_UP, { distance: 10 });
}

function moveDown() {
  emit('drone-command', CONTROL_COMMANDS.MOVE_DOWN, { distance: 10 });
}

function rotateLeft() {
  emit('drone-command', CONTROL_COMMANDS.ROTATE_LEFT, { angle: Math.PI / 4 });
}

function rotateRight() {
  emit('drone-command', CONTROL_COMMANDS.ROTATE_RIGHT, { angle: Math.PI / 4 });
}

// 参数设置
function setSpeed() {
  emit('drone-command', CONTROL_COMMANDS.SET_SPEED, { speed: targetSpeed.value });
}

function setAltitude() {
  emit('drone-command', CONTROL_COMMANDS.SET_ALTITUDE, { altitude: targetAltitude.value });
}

// 航点任务
function addCurrentPositionAsWaypoint() {
  if (selectedDrone.value) {
    waypoints.value.push({
      lat: 22.2769 + (Math.random() - 0.5) * 0.1, // 模拟坐标
      lng: 113.5678 + (Math.random() - 0.5) * 0.1,
      altitude: selectedDrone.value.altitude
    });
  }
}

function removeWaypoint(index) {
  waypoints.value.splice(index, 1);
}

function startWaypointMission() {
  emit('waypoint-mission', selectedDroneId.value, waypoints.value);
}

function clearWaypoints() {
  waypoints.value = [];
}

// 紧急控制
function emergencyStop() {
  emit('drone-command', 'emergency_stop');
}

function emergencyLand() {
  emit('drone-command', 'emergency_land');
}

// 监听选中无人机数据变化
import { watch } from 'vue';
watch(() => props.selectedDroneData, (newData) => {
  selectedDrone.value = newData;
});
</script>

<style scoped>
.drone-control-panel {
  position: absolute;
  top: 20px;
  right: 380px;
  width: 350px;
  max-height: calc(100vh - 40px);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  z-index: 1000;
}

.panel-header h2 {
  margin: 0 0 20px 0;
  font-size: 18px;
  text-align: center;
  color: #00ff88;
  border-bottom: 2px solid #00ff88;
  padding-bottom: 10px;
}

.control-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-section:last-child {
  border-bottom: none;
}

.control-section h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #88ccff;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 无人机选择 */
.drone-select {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
}

/* 状态显示 */
.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  padding: 4px 0;
}

.label {
  color: #ccc;
}

.value {
  font-weight: bold;
}

.battery-good { color: #00ff88; }
.battery-medium { color: #ffff00; }
.battery-low { color: #ff0000; }

.signal-good { color: #00ff88; }
.signal-medium { color: #ffff00; }
.signal-low { color: #ff0000; }

/* 基础控制 */
.basic-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: #00ff88;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.takeoff:hover { border-color: #00ff88; }
.land:hover { border-color: #ffff00; }
.return:hover { border-color: #ff8800; }
.hover:hover { border-color: #88ccff; }

/* 方向控制 */
.direction-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.direction-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 5px;
  max-width: 150px;
  margin: 0 auto;
}

.dir-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.dir-btn:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: #00ff88;
}

.dir-btn.center {
  background: rgba(255, 0, 0, 0.2);
}

.altitude-controls, .rotation-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.alt-btn, .rot-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.alt-btn:hover, .rot-btn:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: #00ff88;
}

/* 参数控制 */
.parameter-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.param-item label {
  font-size: 12px;
  color: #ccc;
}

.param-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #333;
  outline: none;
  -webkit-appearance: none;
}

.param-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00ff88;
  cursor: pointer;
}

.param-value {
  font-size: 12px;
  color: #00ff88;
  text-align: center;
}

/* 航点控制 */
.waypoint-list {
  max-height: 150px;
  overflow-y: auto;
  margin-bottom: 10px;
}

.waypoint-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 11px;
}

.waypoint-item.active {
  background: rgba(0, 255, 136, 0.2);
  border: 1px solid #00ff88;
}

.waypoint-index {
  background: #00ff88;
  color: black;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.remove-btn {
  background: none;
  border: none;
  color: #ff6666;
  cursor: pointer;
  font-size: 10px;
}

.waypoint-actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.waypoint-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.waypoint-btn:hover:not(:disabled) {
  background: rgba(0, 255, 136, 0.2);
  border-color: #00ff88;
}

.waypoint-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 传感器数据 */
.sensor-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sensor-group {
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  border-radius: 6px;
}

.sensor-group h4 {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: #88ccff;
}

.sensor-item {
  font-size: 11px;
  margin-bottom: 3px;
}

/* 紧急控制 */
.emergency {
  border: 2px solid #ff0000;
  background: rgba(255, 0, 0, 0.1);
}

.emergency-controls {
  display: flex;
  gap: 10px;
}

.emergency-btn {
  flex: 1;
  background: rgba(255, 0, 0, 0.3);
  border: 1px solid #ff0000;
  color: white;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.emergency-btn:hover {
  background: rgba(255, 0, 0, 0.5);
  transform: scale(1.05);
}

/* 滚动条 */
.drone-control-panel::-webkit-scrollbar {
  width: 6px;
}

.drone-control-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.drone-control-panel::-webkit-scrollbar-thumb {
  background: #00ff88;
  border-radius: 3px;
}
</style>
