/**
 * 真实建筑系统
 * 创建逼真的珠海市建筑群
 */

import * as THREE from 'three';
import { latLngToWorldCoords, ZHUHAI_BOUNDS } from './zhuhaiTerrain.js';

// 建筑类型定义
export const BUILDING_TYPES = {
  RESIDENTIAL: 'residential',     // 住宅
  COMMERCIAL: 'commercial',       // 商业
  OFFICE: 'office',              // 办公
  INDUSTRIAL: 'industrial',       // 工业
  LANDMARK: 'landmark',          // 地标
  AIRPORT: 'airport',            // 机场
  BRIDGE: 'bridge'               // 桥梁
};

// 珠海市真实建筑数据
export const ZHUHAI_BUILDINGS = {
  // 香洲区主要建筑
  xiangzhou: [
    { name: '珠海中心大厦', lat: 22.2769, lng: 113.5678, height: 180, type: BUILDING_TYPES.OFFICE },
    { name: '华发商都', lat: 22.2750, lng: 113.5650, height: 120, type: BUILDING_TYPES.COMMERCIAL },
    { name: '珠海国际会展中心', lat: 22.2800, lng: 113.5700, height: 60, type: BUILDING_TYPES.LANDMARK },
    { name: '海滨公园', lat: 22.2720, lng: 113.5720, height: 15, type: BUILDING_TYPES.LANDMARK },
    { name: '情侣路沿线住宅', lat: 22.2650, lng: 113.5800, height: 80, type: BUILDING_TYPES.RESIDENTIAL }
  ],
  
  // 金湾区建筑
  jinwan: [
    { name: '珠海机场航站楼', lat: 22.0064, lng: 113.3761, height: 45, type: BUILDING_TYPES.AIRPORT },
    { name: '金湾政府大楼', lat: 22.0100, lng: 113.3800, height: 80, type: BUILDING_TYPES.OFFICE },
    { name: '航空产业园', lat: 22.0200, lng: 113.3900, height: 60, type: BUILDING_TYPES.INDUSTRIAL }
  ],
  
  // 横琴新区建筑
  hengqin: [
    { name: '横琴金融岛', lat: 22.1333, lng: 113.5333, height: 200, type: BUILDING_TYPES.OFFICE },
    { name: '长隆海洋王国', lat: 22.1300, lng: 113.5400, height: 40, type: BUILDING_TYPES.LANDMARK },
    { name: '横琴口岸', lat: 22.1350, lng: 113.5300, height: 30, type: BUILDING_TYPES.LANDMARK }
  ],
  
  // 港珠澳大桥
  bridge: [
    { name: '港珠澳大桥珠海段', lat: 22.2133, lng: 113.5822, height: 65, type: BUILDING_TYPES.BRIDGE }
  ]
};

/**
 * 创建真实的珠海建筑群
 */
export function createRealisticBuildings(scene, options = {}) {
  const {
    scale = 15000,
    enableLighting = true,
    enableShadows = true,
    detailLevel = 'high'
  } = options;

  const buildingGroup = new THREE.Group();
  buildingGroup.userData.type = 'buildings';
  
  // 创建各区域建筑
  Object.entries(ZHUHAI_BUILDINGS).forEach(([district, buildings]) => {
    const districtGroup = createDistrictBuildings(district, buildings, scale, detailLevel);
    buildingGroup.add(districtGroup);
  });
  
  // 添加住宅区
  const residentialAreas = createResidentialAreas(scale);
  buildingGroup.add(residentialAreas);
  
  // 添加商业区
  const commercialAreas = createCommercialAreas(scale);
  buildingGroup.add(commercialAreas);
  
  scene.add(buildingGroup);
  
  return buildingGroup;
}

/**
 * 创建区域建筑群
 */
function createDistrictBuildings(district, buildings, scale, detailLevel) {
  const districtGroup = new THREE.Group();
  districtGroup.userData.district = district;
  
  buildings.forEach(buildingData => {
    const building = createRealisticBuilding(buildingData, scale, detailLevel);
    districtGroup.add(building);
  });
  
  return districtGroup;
}

/**
 * 创建真实建筑
 */
function createRealisticBuilding(buildingData, scale, detailLevel) {
  const worldCoords = latLngToWorldCoords(buildingData.lat, buildingData.lng, ZHUHAI_BOUNDS, scale);
  
  let building;
  
  switch (buildingData.type) {
    case BUILDING_TYPES.OFFICE:
      building = createOfficeBuilding(buildingData);
      break;
    case BUILDING_TYPES.COMMERCIAL:
      building = createCommercialBuilding(buildingData);
      break;
    case BUILDING_TYPES.RESIDENTIAL:
      building = createResidentialBuilding(buildingData);
      break;
    case BUILDING_TYPES.INDUSTRIAL:
      building = createIndustrialBuilding(buildingData);
      break;
    case BUILDING_TYPES.LANDMARK:
      building = createLandmarkBuilding(buildingData);
      break;
    case BUILDING_TYPES.AIRPORT:
      building = createAirportBuilding(buildingData);
      break;
    case BUILDING_TYPES.BRIDGE:
      building = createBridgeStructure(buildingData);
      break;
    default:
      building = createGenericBuilding(buildingData);
  }
  
  building.position.set(worldCoords.x, buildingData.height / 2, worldCoords.z);
  building.userData = { ...buildingData, worldCoords };
  
  return building;
}

/**
 * 创建办公楼
 */
function createOfficeBuilding(data) {
  const group = new THREE.Group();
  
  // 主体建筑
  const mainGeometry = new THREE.BoxGeometry(40, data.height, 30);
  const mainMaterial = new THREE.MeshStandardMaterial({
    color: 0x4A90E2,
    metalness: 0.3,
    roughness: 0.4
  });
  const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
  group.add(mainBuilding);
  
  // 添加窗户
  addWindows(group, 40, data.height, 30);
  
  // 添加顶部结构
  const roofGeometry = new THREE.BoxGeometry(42, 5, 32);
  const roofMaterial = new THREE.MeshStandardMaterial({
    color: 0x2C3E50,
    metalness: 0.8,
    roughness: 0.2
  });
  const roof = new THREE.Mesh(roofGeometry, roofMaterial);
  roof.position.y = data.height / 2 + 2.5;
  group.add(roof);
  
  return group;
}

/**
 * 创建商业建筑
 */
function createCommercialBuilding(data) {
  const group = new THREE.Group();
  
  // 主体建筑 - 更宽更低
  const mainGeometry = new THREE.BoxGeometry(60, data.height, 40);
  const mainMaterial = new THREE.MeshStandardMaterial({
    color: 0xE74C3C,
    metalness: 0.2,
    roughness: 0.6
  });
  const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
  group.add(mainBuilding);
  
  // 添加大型玻璃幕墙
  addGlassFacade(group, 60, data.height, 40);
  
  // 添加招牌
  const signGeometry = new THREE.BoxGeometry(50, 8, 2);
  const signMaterial = new THREE.MeshStandardMaterial({
    color: 0xF39C12,
    emissive: 0x332200
  });
  const sign = new THREE.Mesh(signGeometry, signMaterial);
  sign.position.set(0, data.height / 2 + 4, 21);
  group.add(sign);
  
  return group;
}

/**
 * 创建住宅建筑
 */
function createResidentialBuilding(data) {
  const group = new THREE.Group();
  
  // 主体建筑
  const mainGeometry = new THREE.BoxGeometry(25, data.height, 20);
  const mainMaterial = new THREE.MeshStandardMaterial({
    color: 0x95A5A6,
    metalness: 0.1,
    roughness: 0.8
  });
  const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
  group.add(mainBuilding);
  
  // 添加阳台
  addBalconies(group, 25, data.height, 20);
  
  // 添加小窗户
  addResidentialWindows(group, 25, data.height, 20);
  
  return group;
}

/**
 * 创建工业建筑
 */
function createIndustrialBuilding(data) {
  const group = new THREE.Group();
  
  // 主厂房
  const mainGeometry = new THREE.BoxGeometry(80, data.height, 50);
  const mainMaterial = new THREE.MeshStandardMaterial({
    color: 0x7F8C8D,
    metalness: 0.6,
    roughness: 0.7
  });
  const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
  group.add(mainBuilding);
  
  // 添加烟囱
  const chimneyGeometry = new THREE.CylinderGeometry(3, 4, data.height + 20, 8);
  const chimneyMaterial = new THREE.MeshStandardMaterial({
    color: 0x34495E,
    metalness: 0.3,
    roughness: 0.9
  });
  const chimney = new THREE.Mesh(chimneyGeometry, chimneyMaterial);
  chimney.position.set(20, (data.height + 20) / 2, 10);
  group.add(chimney);
  
  return group;
}

/**
 * 创建地标建筑
 */
function createLandmarkBuilding(data) {
  const group = new THREE.Group();
  
  if (data.name.includes('会展')) {
    // 会展中心 - 特殊造型
    const mainGeometry = new THREE.CylinderGeometry(40, 50, data.height, 12);
    const mainMaterial = new THREE.MeshStandardMaterial({
      color: 0x3498DB,
      metalness: 0.4,
      roughness: 0.3
    });
    const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
    group.add(mainBuilding);
  } else if (data.name.includes('公园')) {
    // 公园建筑 - 低矮绿色
    const mainGeometry = new THREE.BoxGeometry(100, data.height, 60);
    const mainMaterial = new THREE.MeshStandardMaterial({
      color: 0x27AE60,
      metalness: 0.1,
      roughness: 0.9
    });
    const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
    group.add(mainBuilding);
  } else {
    // 通用地标
    const mainGeometry = new THREE.BoxGeometry(50, data.height, 40);
    const mainMaterial = new THREE.MeshStandardMaterial({
      color: 0xF1C40F,
      metalness: 0.3,
      roughness: 0.4
    });
    const mainBuilding = new THREE.Mesh(mainGeometry, mainMaterial);
    group.add(mainBuilding);
  }
  
  return group;
}

/**
 * 创建机场建筑
 */
function createAirportBuilding(data) {
  const group = new THREE.Group();
  
  // 航站楼主体
  const terminalGeometry = new THREE.BoxGeometry(200, data.height, 80);
  const terminalMaterial = new THREE.MeshStandardMaterial({
    color: 0xBDC3C7,
    metalness: 0.5,
    roughness: 0.3
  });
  const terminal = new THREE.Mesh(terminalGeometry, terminalMaterial);
  group.add(terminal);
  
  // 控制塔
  const towerGeometry = new THREE.CylinderGeometry(8, 10, data.height + 30, 8);
  const towerMaterial = new THREE.MeshStandardMaterial({
    color: 0x34495E,
    metalness: 0.7,
    roughness: 0.2
  });
  const tower = new THREE.Mesh(towerGeometry, towerMaterial);
  tower.position.set(80, (data.height + 30) / 2, 0);
  group.add(tower);
  
  // 跑道标识
  const runwayGeometry = new THREE.BoxGeometry(1000, 1, 50);
  const runwayMaterial = new THREE.MeshStandardMaterial({
    color: 0x2C3E50,
    metalness: 0.1,
    roughness: 0.9
  });
  const runway = new THREE.Mesh(runwayGeometry, runwayMaterial);
  runway.position.set(0, -data.height / 2, 200);
  group.add(runway);
  
  return group;
}

/**
 * 创建桥梁结构
 */
function createBridgeStructure(data) {
  const group = new THREE.Group();
  
  // 桥面
  const deckGeometry = new THREE.BoxGeometry(2000, 8, 40);
  const deckMaterial = new THREE.MeshStandardMaterial({
    color: 0x95A5A6,
    metalness: 0.3,
    roughness: 0.6
  });
  const deck = new THREE.Mesh(deckGeometry, deckMaterial);
  deck.position.y = data.height - 4;
  group.add(deck);
  
  // 桥塔
  for (let i = -3; i <= 3; i++) {
    if (i !== 0) {
      const towerGeometry = new THREE.BoxGeometry(15, data.height * 2, 15);
      const towerMaterial = new THREE.MeshStandardMaterial({
        color: 0x34495E,
        metalness: 0.6,
        roughness: 0.3
      });
      const tower = new THREE.Mesh(towerGeometry, towerMaterial);
      tower.position.set(i * 400, data.height, 0);
      group.add(tower);
      
      // 缆绳
      addCables(group, i * 400, data.height * 2, 0, 0, data.height - 4, 0);
    }
  }
  
  return group;
}

/**
 * 添加窗户
 */
function addWindows(group, width, height, depth) {
  const windowMaterial = new THREE.MeshStandardMaterial({
    color: 0x87CEEB,
    metalness: 0.9,
    roughness: 0.1,
    transparent: true,
    opacity: 0.7
  });
  
  const floors = Math.floor(height / 4);
  const windowsPerFloor = Math.floor(width / 5);
  
  for (let floor = 0; floor < floors; floor++) {
    for (let window = 0; window < windowsPerFloor; window++) {
      const windowGeometry = new THREE.BoxGeometry(3, 2.5, 0.2);
      const windowMesh = new THREE.Mesh(windowGeometry, windowMaterial);
      
      windowMesh.position.set(
        (window - windowsPerFloor / 2) * 5,
        (floor - floors / 2) * 4 + 2,
        depth / 2 + 0.1
      );
      
      group.add(windowMesh);
    }
  }
}

/**
 * 添加玻璃幕墙
 */
function addGlassFacade(group, width, height, depth) {
  const glassGeometry = new THREE.BoxGeometry(width - 2, height - 2, 0.5);
  const glassMaterial = new THREE.MeshStandardMaterial({
    color: 0x87CEEB,
    metalness: 0.9,
    roughness: 0.05,
    transparent: true,
    opacity: 0.6
  });
  
  const glassFacade = new THREE.Mesh(glassGeometry, glassMaterial);
  glassFacade.position.set(0, 0, depth / 2 + 0.25);
  group.add(glassFacade);
}

/**
 * 添加阳台
 */
function addBalconies(group, width, height, depth) {
  const floors = Math.floor(height / 3);
  
  for (let floor = 1; floor < floors; floor++) {
    const balconyGeometry = new THREE.BoxGeometry(width + 4, 0.5, 3);
    const balconyMaterial = new THREE.MeshStandardMaterial({
      color: 0xBDC3C7,
      metalness: 0.2,
      roughness: 0.8
    });
    
    const balcony = new THREE.Mesh(balconyGeometry, balconyMaterial);
    balcony.position.set(0, (floor - floors / 2) * 3, depth / 2 + 1.5);
    group.add(balcony);
  }
}

/**
 * 添加住宅窗户
 */
function addResidentialWindows(group, width, height, depth) {
  const windowMaterial = new THREE.MeshStandardMaterial({
    color: 0xF4D03F,
    emissive: 0x332200,
    metalness: 0.1,
    roughness: 0.9
  });
  
  const floors = Math.floor(height / 3);
  const windowsPerFloor = Math.floor(width / 4);
  
  for (let floor = 0; floor < floors; floor++) {
    for (let window = 0; window < windowsPerFloor; window++) {
      if (Math.random() > 0.3) { // 不是所有窗户都亮着
        const windowGeometry = new THREE.BoxGeometry(2, 1.5, 0.2);
        const windowMesh = new THREE.Mesh(windowGeometry, windowMaterial);
        
        windowMesh.position.set(
          (window - windowsPerFloor / 2) * 4,
          (floor - floors / 2) * 3 + 1.5,
          depth / 2 + 0.1
        );
        
        group.add(windowMesh);
      }
    }
  }
}

/**
 * 添加缆绳
 */
function addCables(group, x1, y1, z1, x2, y2, z2) {
  const cableGeometry = new THREE.CylinderGeometry(0.2, 0.2, 
    Math.sqrt((x2-x1)**2 + (y2-y1)**2 + (z2-z1)**2), 8);
  const cableMaterial = new THREE.MeshStandardMaterial({
    color: 0x2C3E50,
    metalness: 0.8,
    roughness: 0.2
  });
  
  const cable = new THREE.Mesh(cableGeometry, cableMaterial);
  cable.position.set((x1+x2)/2, (y1+y2)/2, (z1+z2)/2);
  
  const direction = new THREE.Vector3(x2-x1, y2-y1, z2-z1).normalize();
  cable.lookAt(cable.position.clone().add(direction));
  cable.rotateX(Math.PI / 2);
  
  group.add(cable);
}

/**
 * 创建住宅区
 */
function createResidentialAreas(scale) {
  const residentialGroup = new THREE.Group();
  residentialGroup.userData.type = 'residential_areas';
  
  // 在各个区域添加住宅群
  const areas = [
    { center: { x: 1000, z: 1000 }, count: 20, name: '香洲住宅区' },
    { center: { x: -2000, z: -500 }, count: 15, name: '金湾住宅区' },
    { center: { x: 2000, z: -1500 }, count: 12, name: '横琴住宅区' }
  ];
  
  areas.forEach(area => {
    for (let i = 0; i < area.count; i++) {
      const building = createResidentialBuilding({
        height: 30 + Math.random() * 50,
        type: BUILDING_TYPES.RESIDENTIAL
      });
      
      building.position.set(
        area.center.x + (Math.random() - 0.5) * 800,
        building.children[0].geometry.parameters.height / 2,
        area.center.z + (Math.random() - 0.5) * 600
      );
      
      residentialGroup.add(building);
    }
  });
  
  return residentialGroup;
}

/**
 * 创建商业区
 */
function createCommercialAreas(scale) {
  const commercialGroup = new THREE.Group();
  commercialGroup.userData.type = 'commercial_areas';
  
  // 商业中心区域
  const centers = [
    { center: { x: 0, z: 0 }, count: 8, name: '香洲商业中心' },
    { center: { x: 2000, z: -1000 }, count: 5, name: '横琴商业区' }
  ];
  
  centers.forEach(center => {
    for (let i = 0; i < center.count; i++) {
      const building = createCommercialBuilding({
        height: 40 + Math.random() * 80,
        type: BUILDING_TYPES.COMMERCIAL
      });
      
      building.position.set(
        center.center.x + (Math.random() - 0.5) * 400,
        building.children[0].geometry.parameters.height / 2,
        center.center.z + (Math.random() - 0.5) * 400
      );
      
      commercialGroup.add(building);
    }
  });
  
  return commercialGroup;
}

/**
 * 创建通用建筑
 */
function createGenericBuilding(data) {
  const geometry = new THREE.BoxGeometry(30, data.height, 25);
  const material = new THREE.MeshStandardMaterial({
    color: 0x95A5A6,
    metalness: 0.3,
    roughness: 0.7
  });
  
  return new THREE.Mesh(geometry, material);
}
