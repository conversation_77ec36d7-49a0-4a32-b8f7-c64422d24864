# 珠海市低空三维空域动态气象预警系统

基于 Vue3 + Three.js 开发的珠海市低空三维空域动态气象预警系统，集成真实地理数据、无人机巡航监控和增强气象预警功能，为低空飞行提供全方位安全保障。

## 🌟 主要功能

### 🗺️ 珠海市真实地形
- **真实地理数据**: 基于珠海市实际地理坐标和地形数据
- **地标标识**: 机场、港珠澳大桥、凤凰山、黄杨山等重要地标
- **海岸线显示**: 珠海市完整海岸线和海域边界
- **区域划分**: 香洲区、金湾区、斗门区、横琴新区等行政区域
- **高度着色**: 根据海拔高度自动着色（海面-蓝色，平原-绿色，山地-棕色）

### 🌤️ 气象数据可视化
- **风场显示**: 3D风向箭头，颜色编码风速等级
- **温度分布**: 热力图显示，实时温度变化
- **湿度云层**: 半透明球体表示湿度分布
- **降水系统**: 粒子效果模拟降水强度

### 🚁 无人机巡航系统
- **多类型无人机**: 巡逻、气象监测、应急救援三种无人机类型
- **智能路径规划**: 海岸线、机场、山区、大桥等预设巡航路线
- **实时状态监控**: 电池电量、飞行高度、速度、航向等参数
- **自动预警响应**: 根据天气条件自动调整飞行策略
- **3D模型显示**: 真实的无人机3D模型和飞行轨迹

### 🚨 增强气象预警系统
- **专业风力预警**: 按蒲福风级（0-12级）精确分级预警
- **暴雨等级预警**: 小雨到特大暴雨六级降水预警
- **复合天气预警**: 雷暴、台风、飑线等复合天气类型
- **特殊天气监测**: 大雾、结冰、低能见度等特殊条件
- **智能预警建议**: 针对不同预警等级提供专业飞行建议

### 🎮 交互控制
- **相机控制**: 鼠标拖拽旋转、滚轮缩放、右键平移
- **时间轴控制**: 可调节时间，查看历史和预测数据
- **图层开关**: 独立控制各气象要素的显示/隐藏
- **视角切换**: 全景、近景、侧面等预设视角

### 📊 信息面板
- **实时预警信息**: 当前活跃预警列表
- **选中点详情**: 点击地形查看具体位置天气数据
- **天气概况**: 整体气象参数统计
- **系统状态**: 数据连接和系统运行状态

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```
