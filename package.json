{"name": "dikong", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"-": "^0.0.1", "adapter": "^1.0.0-beta.10", "cesium": "^1.130.0", "chart.js": "^4.4.9", "chartjs": "^0.3.24", "moment": "^2.30.1", "three": "^0.177.0", "vue": "^3.5.13"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-cesium": "^1.2.23", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}