/**
 * 真实虚拟无人机系统
 * 创建逼真的无人机3D模型和飞行系统
 */

import * as THREE from 'three';

// 无人机型号定义
export const DRONE_MODELS = {
  DJI_PHANTOM: {
    name: 'DJI Phantom 4',
    bodySize: { width: 3, height: 1.2, depth: 3 },
    armLength: 2.5,
    propellerRadius: 1.2,
    color: 0xFFFFFF,
    maxSpeed: 20,
    maxAltitude: 500,
    batteryLife: 1800
  },
  DJI_MAVIC: {
    name: 'DJI Mavic Pro',
    bodySize: { width: 2, height: 0.8, depth: 2 },
    armLength: 1.8,
    propellerRadius: 0.8,
    color: 0x2C3E50,
    maxSpeed: 18,
    maxAltitude: 500,
    batteryLife: 1620
  },
  INDUSTRIAL_DRONE: {
    name: '工业级无人机',
    bodySize: { width: 4, height: 1.5, depth: 4 },
    armLength: 3.5,
    propellerRadius: 1.8,
    color: 0xFF6B35,
    maxSpeed: 15,
    maxAltitude: 1000,
    batteryLife: 3600
  }
};

/**
 * 创建真实的无人机模型
 */
export function createRealisticDrone(modelType = 'DJI_PHANTOM', options = {}) {
  const model = DRONE_MODELS[modelType];
  if (!model) {
    console.warn(`Unknown drone model: ${modelType}`);
    return createRealisticDrone('DJI_PHANTOM', options);
  }

  const droneGroup = new THREE.Group();
  droneGroup.userData.model = model;
  droneGroup.userData.type = 'realistic_drone';

  // 创建机身
  const body = createDroneBody(model);
  droneGroup.add(body);

  // 创建机臂
  const arms = createDroneArms(model);
  arms.forEach(arm => droneGroup.add(arm));

  // 创建螺旋桨
  const propellers = createPropellers(model);
  propellers.forEach(prop => droneGroup.add(prop));
  droneGroup.userData.propellers = propellers;

  // 创建云台和相机
  const gimbal = createGimbal(model);
  droneGroup.add(gimbal);

  // 创建LED指示灯
  const leds = createLEDs(model);
  leds.forEach(led => droneGroup.add(led));
  droneGroup.userData.leds = leds;

  // 创建起落架
  const landingGear = createLandingGear(model);
  landingGear.forEach(gear => droneGroup.add(gear));

  // 添加传感器
  const sensors = createSensors(model);
  sensors.forEach(sensor => droneGroup.add(sensor));

  // 设置阴影
  droneGroup.traverse(child => {
    if (child.isMesh) {
      child.castShadow = true;
      child.receiveShadow = true;
    }
  });

  return droneGroup;
}

/**
 * 创建无人机机身
 */
function createDroneBody(model) {
  const bodyGroup = new THREE.Group();

  // 主机身
  const mainBodyGeometry = new THREE.BoxGeometry(
    model.bodySize.width,
    model.bodySize.height,
    model.bodySize.depth
  );
  
  // 圆角处理
  const roundedBodyGeometry = new THREE.BoxGeometry(
    model.bodySize.width * 0.9,
    model.bodySize.height,
    model.bodySize.depth * 0.9
  );
  roundedBodyGeometry.translate(0, 0, 0);

  const bodyMaterial = new THREE.MeshStandardMaterial({
    color: model.color,
    metalness: 0.3,
    roughness: 0.4
  });

  const mainBody = new THREE.Mesh(roundedBodyGeometry, bodyMaterial);
  bodyGroup.add(mainBody);

  // 顶部盖板
  const topCoverGeometry = new THREE.BoxGeometry(
    model.bodySize.width * 0.95,
    0.1,
    model.bodySize.depth * 0.95
  );
  const topCoverMaterial = new THREE.MeshStandardMaterial({
    color: model.color === 0xFFFFFF ? 0xE8E8E8 : model.color,
    metalness: 0.5,
    roughness: 0.2
  });
  const topCover = new THREE.Mesh(topCoverGeometry, topCoverMaterial);
  topCover.position.y = model.bodySize.height / 2 + 0.05;
  bodyGroup.add(topCover);

  // 底部电池仓
  const batteryGeometry = new THREE.BoxGeometry(
    model.bodySize.width * 0.7,
    0.3,
    model.bodySize.depth * 0.4
  );
  const batteryMaterial = new THREE.MeshStandardMaterial({
    color: 0x2C3E50,
    metalness: 0.6,
    roughness: 0.3
  });
  const battery = new THREE.Mesh(batteryGeometry, batteryMaterial);
  battery.position.y = -model.bodySize.height / 2 - 0.15;
  bodyGroup.add(battery);

  return bodyGroup;
}

/**
 * 创建无人机机臂
 */
function createDroneArms(model) {
  const arms = [];
  const armPositions = [
    { x: 1, z: 1 },   // 右前
    { x: -1, z: 1 },  // 左前
    { x: 1, z: -1 },  // 右后
    { x: -1, z: -1 }  // 左后
  ];

  armPositions.forEach((pos, index) => {
    const armGroup = new THREE.Group();

    // 机臂主体
    const armGeometry = new THREE.CylinderGeometry(0.1, 0.15, model.armLength, 8);
    const armMaterial = new THREE.MeshStandardMaterial({
      color: model.color === 0xFFFFFF ? 0xD0D0D0 : model.color,
      metalness: 0.7,
      roughness: 0.3
    });
    const arm = new THREE.Mesh(armGeometry, armMaterial);
    
    // 旋转机臂指向对角
    const angle = Math.atan2(pos.z, pos.x);
    arm.rotation.z = -angle + Math.PI / 2;
    arm.position.set(
      pos.x * model.armLength / 2,
      0,
      pos.z * model.armLength / 2
    );
    
    armGroup.add(arm);

    // 电机座
    const motorMountGeometry = new THREE.CylinderGeometry(0.3, 0.25, 0.4, 12);
    const motorMountMaterial = new THREE.MeshStandardMaterial({
      color: 0x34495E,
      metalness: 0.8,
      roughness: 0.2
    });
    const motorMount = new THREE.Mesh(motorMountGeometry, motorMountMaterial);
    motorMount.position.set(
      pos.x * model.armLength,
      0.2,
      pos.z * model.armLength
    );
    armGroup.add(motorMount);

    arms.push(armGroup);
  });

  return arms;
}

/**
 * 创建螺旋桨
 */
function createPropellers(model) {
  const propellers = [];
  const propPositions = [
    { x: 1, z: 1 },   // 右前
    { x: -1, z: 1 },  // 左前
    { x: 1, z: -1 },  // 右后
    { x: -1, z: -1 }  // 左后
  ];

  propPositions.forEach((pos, index) => {
    const propGroup = new THREE.Group();

    // 螺旋桨叶片
    for (let i = 0; i < 2; i++) {
      const bladeGeometry = new THREE.BoxGeometry(
        model.propellerRadius * 2,
        0.02,
        0.15
      );
      const bladeMaterial = new THREE.MeshStandardMaterial({
        color: 0x2C3E50,
        metalness: 0.4,
        roughness: 0.6,
        transparent: true,
        opacity: 0.8
      });
      const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
      blade.rotation.y = i * Math.PI;
      propGroup.add(blade);
    }

    // 螺旋桨中心
    const hubGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.05, 8);
    const hubMaterial = new THREE.MeshStandardMaterial({
      color: 0x34495E,
      metalness: 0.8,
      roughness: 0.2
    });
    const hub = new THREE.Mesh(hubGeometry, hubMaterial);
    propGroup.add(hub);

    propGroup.position.set(
      pos.x * model.armLength,
      0.4,
      pos.z * model.armLength
    );

    // 存储旋转方向（前后螺旋桨相反）
    propGroup.userData.rotationDirection = (index % 2 === 0) ? 1 : -1;
    propGroup.userData.spinning = false;

    propellers.push(propGroup);
  });

  return propellers;
}

/**
 * 创建云台和相机
 */
function createGimbal(model) {
  const gimbalGroup = new THREE.Group();

  // 云台主体
  const gimbalGeometry = new THREE.SphereGeometry(0.3, 16, 12);
  const gimbalMaterial = new THREE.MeshStandardMaterial({
    color: 0x2C3E50,
    metalness: 0.6,
    roughness: 0.4
  });
  const gimbal = new THREE.Mesh(gimbalGeometry, gimbalMaterial);
  gimbal.position.y = -model.bodySize.height / 2 - 0.5;
  gimbalGroup.add(gimbal);

  // 相机
  const cameraGeometry = new THREE.BoxGeometry(0.4, 0.3, 0.6);
  const cameraMaterial = new THREE.MeshStandardMaterial({
    color: 0x1A1A1A,
    metalness: 0.3,
    roughness: 0.7
  });
  const camera = new THREE.Mesh(cameraGeometry, cameraMaterial);
  camera.position.set(0, -model.bodySize.height / 2 - 0.7, 0.2);
  gimbalGroup.add(camera);

  // 镜头
  const lensGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.2, 16);
  const lensMaterial = new THREE.MeshStandardMaterial({
    color: 0x000000,
    metalness: 0.9,
    roughness: 0.1
  });
  const lens = new THREE.Mesh(lensGeometry, lensMaterial);
  lens.rotation.x = Math.PI / 2;
  lens.position.set(0, -model.bodySize.height / 2 - 0.7, 0.4);
  gimbalGroup.add(lens);

  gimbalGroup.userData.type = 'gimbal';
  return gimbalGroup;
}

/**
 * 创建LED指示灯
 */
function createLEDs(model) {
  const leds = [];
  const ledPositions = [
    { x: model.bodySize.width / 2, y: 0, z: 0, color: 0x00FF00 },      // 右侧绿灯
    { x: -model.bodySize.width / 2, y: 0, z: 0, color: 0xFF0000 },     // 左侧红灯
    { x: 0, y: 0, z: model.bodySize.depth / 2, color: 0xFFFFFF },       // 前方白灯
    { x: 0, y: 0, z: -model.bodySize.depth / 2, color: 0xFF0000 }       // 后方红灯
  ];

  ledPositions.forEach(pos => {
    const ledGeometry = new THREE.SphereGeometry(0.05, 8, 6);
    const ledMaterial = new THREE.MeshStandardMaterial({
      color: pos.color,
      emissive: pos.color,
      emissiveIntensity: 0.5
    });
    const led = new THREE.Mesh(ledGeometry, ledMaterial);
    led.position.set(pos.x, pos.y, pos.z);
    led.userData.originalColor = pos.color;
    led.userData.type = 'led';
    leds.push(led);
  });

  return leds;
}

/**
 * 创建起落架
 */
function createLandingGear(model) {
  const landingGear = [];
  const gearPositions = [
    { x: model.bodySize.width / 3, z: model.bodySize.depth / 3 },
    { x: -model.bodySize.width / 3, z: model.bodySize.depth / 3 },
    { x: model.bodySize.width / 3, z: -model.bodySize.depth / 3 },
    { x: -model.bodySize.width / 3, z: -model.bodySize.depth / 3 }
  ];

  gearPositions.forEach(pos => {
    const gearGroup = new THREE.Group();

    // 支撑杆
    const strutGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.5, 8);
    const strutMaterial = new THREE.MeshStandardMaterial({
      color: 0x2C3E50,
      metalness: 0.7,
      roughness: 0.3
    });
    const strut = new THREE.Mesh(strutGeometry, strutMaterial);
    strut.position.y = -0.25;
    gearGroup.add(strut);

    // 脚垫
    const footGeometry = new THREE.SphereGeometry(0.08, 8, 6);
    const footMaterial = new THREE.MeshStandardMaterial({
      color: 0x1A1A1A,
      metalness: 0.2,
      roughness: 0.8
    });
    const foot = new THREE.Mesh(footGeometry, footMaterial);
    foot.position.y = -0.5;
    foot.scale.y = 0.5;
    gearGroup.add(foot);

    gearGroup.position.set(pos.x, -model.bodySize.height / 2, pos.z);
    landingGear.push(gearGroup);
  });

  return landingGear;
}

/**
 * 创建传感器
 */
function createSensors(model) {
  const sensors = [];

  // 前方避障传感器
  const frontSensorGeometry = new THREE.BoxGeometry(0.1, 0.05, 0.05);
  const sensorMaterial = new THREE.MeshStandardMaterial({
    color: 0x1A1A1A,
    metalness: 0.8,
    roughness: 0.2
  });
  const frontSensor = new THREE.Mesh(frontSensorGeometry, sensorMaterial);
  frontSensor.position.set(0, 0, model.bodySize.depth / 2 + 0.05);
  sensors.push(frontSensor);

  // 底部视觉传感器
  const bottomSensorGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.03, 8);
  const bottomSensor = new THREE.Mesh(bottomSensorGeometry, sensorMaterial);
  bottomSensor.position.set(0, -model.bodySize.height / 2 - 0.02, 0);
  sensors.push(bottomSensor);

  return sensors;
}

/**
 * 更新无人机动画
 */
export function updateDroneAnimation(droneGroup, isFlying = false, time = 0) {
  if (!droneGroup.userData.propellers) return;

  const propellers = droneGroup.userData.propellers;
  const leds = droneGroup.userData.leds;

  // 更新螺旋桨旋转
  if (isFlying) {
    propellers.forEach(prop => {
      const rotationSpeed = 0.5; // 旋转速度
      prop.rotation.y += rotationSpeed * prop.userData.rotationDirection;
      prop.userData.spinning = true;

      // 飞行时螺旋桨半透明效果
      prop.children.forEach(child => {
        if (child.material) {
          child.material.opacity = 0.3;
          child.material.transparent = true;
        }
      });
    });
  } else {
    propellers.forEach(prop => {
      prop.userData.spinning = false;
      // 停止时螺旋桨不透明
      prop.children.forEach(child => {
        if (child.material) {
          child.material.opacity = 0.8;
          child.material.transparent = true;
        }
      });
    });
  }

  // 更新LED闪烁
  if (leds) {
    leds.forEach((led, index) => {
      const blinkSpeed = isFlying ? 2 : 0.5;
      const intensity = 0.3 + 0.7 * Math.abs(Math.sin(time * blinkSpeed + index));
      led.material.emissiveIntensity = intensity;
    });
  }

  // 飞行时轻微摇摆
  if (isFlying) {
    const swayAmount = 0.02;
    droneGroup.rotation.x = Math.sin(time * 0.8) * swayAmount;
    droneGroup.rotation.z = Math.cos(time * 0.6) * swayAmount;
  } else {
    droneGroup.rotation.x = 0;
    droneGroup.rotation.z = 0;
  }
}

/**
 * 设置无人机状态LED
 */
export function setDroneStatusLED(droneGroup, status) {
  if (!droneGroup.userData.leds) return;

  const leds = droneGroup.userData.leds;
  let color = 0x00FF00; // 默认绿色

  switch (status) {
    case 'idle':
      color = 0x0000FF; // 蓝色
      break;
    case 'flying':
      color = 0x00FF00; // 绿色
      break;
    case 'warning':
      color = 0xFFFF00; // 黄色
      break;
    case 'error':
      color = 0xFF0000; // 红色
      break;
    case 'emergency':
      color = 0xFF0000; // 红色闪烁
      break;
  }

  leds.forEach(led => {
    led.material.emissive.setHex(color);
  });
}

/**
 * 创建无人机飞行轨迹
 */
export function createDroneTrail(maxPoints = 100) {
  const trailGeometry = new THREE.BufferGeometry();
  const positions = new Float32Array(maxPoints * 3);
  const colors = new Float32Array(maxPoints * 3);

  trailGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  trailGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

  const trailMaterial = new THREE.LineBasicMaterial({
    vertexColors: true,
    transparent: true,
    opacity: 0.6
  });

  const trail = new THREE.Line(trailGeometry, trailMaterial);
  trail.userData.maxPoints = maxPoints;
  trail.userData.currentPoints = 0;

  return trail;
}

/**
 * 更新无人机轨迹
 */
export function updateDroneTrail(trail, position) {
  const geometry = trail.geometry;
  const positions = geometry.attributes.position.array;
  const colors = geometry.attributes.color.array;
  const maxPoints = trail.userData.maxPoints;
  let currentPoints = trail.userData.currentPoints;

  // 添加新点
  if (currentPoints < maxPoints) {
    const index = currentPoints * 3;
    positions[index] = position.x;
    positions[index + 1] = position.y;
    positions[index + 2] = position.z;

    // 设置颜色（从绿到红渐变）
    const alpha = currentPoints / maxPoints;
    colors[index] = 1 - alpha;     // R
    colors[index + 1] = alpha;     // G
    colors[index + 2] = 0;         // B

    currentPoints++;
    trail.userData.currentPoints = currentPoints;
  } else {
    // 移动所有点
    for (let i = 0; i < (maxPoints - 1) * 3; i++) {
      positions[i] = positions[i + 3];
      colors[i] = colors[i + 3];
    }

    // 添加新点到末尾
    const lastIndex = (maxPoints - 1) * 3;
    positions[lastIndex] = position.x;
    positions[lastIndex + 1] = position.y;
    positions[lastIndex + 2] = position.z;

    colors[lastIndex] = 0;     // R
    colors[lastIndex + 1] = 1; // G
    colors[lastIndex + 2] = 0; // B
  }

  geometry.attributes.position.needsUpdate = true;
  geometry.attributes.color.needsUpdate = true;
  geometry.setDrawRange(0, currentPoints);
}
