<template>
  <div class="cesium-weather-system">
    <!-- Cesium地图容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>

    <!-- 预警系统控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市低空三维空域动态气象预警系统</h3>

      <!-- 实时预警信息 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div
            v-for="warning in activeWarnings"
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="flyToWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
              <div class="warning-intensity">强度: {{ warning.intensity.toFixed(1) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色预警</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色预警</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色预警</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色预警</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate">
          ➕ 生成新预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更新' }}
        </button>
      </div>

      <!-- 图层控制 -->
      <div class="layer-control">
        <h4>📊 图层控制</h4>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.buildings" @change="toggleBuildings">
            🏢 建筑物
          </label>
        </div>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.weather" @change="toggleWeather">
            ☁️ 气象预警
          </label>
        </div>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.airspace" @change="toggleAirspace">
            ✈️ 低空空域
          </label>
        </div>
        <div class="layer-item">
          <label>
            <input type="checkbox" v-model="layers.labels" @change="toggleLabels">
            🏷️ 区域标签
          </label>
        </div>
      </div>
    </div>

    <!-- 珠海区域信息面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div
          v-for="area in zhuhaiAreas"
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态面板 -->
    <div class="status-panel">
      <div class="status-item">
        <span class="status-label">系统状态:</span>
        <span class="status-value online">🟢 在线运行</span>
      </div>
      <div class="status-item">
        <span class="status-label">更新时间:</span>
        <span class="status-value">{{ lastUpdateTime }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">活跃预警:</span>
        <span class="status-value">{{ activeWarnings.length }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">覆盖区域:</span>
        <span class="status-value">珠海市全域</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue';
import * as Cesium from 'cesium';

// 设置Cesium访问令牌
try {
  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
} catch (error) {
  console.error('Cesium令牌设置失败:', error);
}

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const lastUpdateTime = ref('');
const autoUpdate = ref(true);

// 图层控制
const layers = reactive({
  buildings: true,
  weather: true,
  airspace: true,
  labels: true
});

// Cesium相关变量
let viewer = null;
let weatherEntities = [];
let airspaceEntities = [];
let labelEntities = [];
let updateInterval = null;
let warningIdCounter = 0;

// 珠海市地理坐标 - 固定在珠海市范围
const ZHUHAI_BOUNDS = {
  west: 113.0354,
  south: 21.7098,
  east: 113.7759,
  north: 22.5159,
  center: {
    longitude: 113.5767,
    latitude: 22.2711,
    height: 25000  // 降低高度以更好地观察珠海
  }
};

// 珠海市重要区域 - 详细区域信息
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2711,
    description: '珠海市中心区域，政治经济文化中心',
    population: '约100万人',
    area: '300平方公里'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸，客流量巨大',
    population: '日均30万人次',
    area: '5平方公里'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.5200,
    latitude: 22.1300,
    description: '珠海经济特区，粤港澳大湾区重点开发区',
    population: '约8万人',
    area: '106平方公里'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3761,
    latitude: 22.0064,
    description: '珠海机场所在地，航空产业基地',
    population: '约15万人',
    area: '574平方公里'
  },
  {
    name: '斗门区',
    icon: '🌾',
    longitude: 113.2500,
    latitude: 22.2000,
    description: '珠海农业区，生态环境优美',
    population: '约35万人',
    area: '674平方公里'
  },
  {
    name: '港珠澳大桥',
    icon: '🌉',
    longitude: 113.5400,
    latitude: 22.2100,
    description: '连接港澳的跨海大桥，世界级工程',
    population: '日均5万车次',
    area: '55公里'
  }
]);

onMounted(() => {
  console.log('初始化珠海市气象预警系统...');
  initCesium();
  initZhuhaiMap();
  initWeatherSystem();
  startDataUpdate();

  // 生成初始预警数据
  setTimeout(() => {
    generateInitialWarnings();
  }, 2000);
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
  if (viewer) {
    viewer.destroy();
  }
});

// 初始化Cesium地球 - 固定在珠海市
function initCesium() {
  try {
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 基础配置
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: true,
      sceneModePicker: false,
      selectionIndicator: true,
      timeline: false,
      navigationHelpButton: false,
      navigationInstructionsInitiallyVisible: false,

      // 使用高分辨率影像
      imageryProvider: new Cesium.ArcGisMapServerImageryProvider({
        url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
      })
    });

    // 设置相机限制 - 限制在珠海市范围内
    const rectangle = Cesium.Rectangle.fromDegrees(
      ZHUHAI_BOUNDS.west - 0.1,
      ZHUHAI_BOUNDS.south - 0.1,
      ZHUHAI_BOUNDS.east + 0.1,
      ZHUHAI_BOUNDS.north + 0.1
    );

    viewer.scene.camera.constrainedAxis = Cesium.Cartesian3.UNIT_Z;
    viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;

    // 设置缩放限制
    viewer.scene.screenSpaceCameraController.minimumZoomDistance = 5000;   // 最小5公里
    viewer.scene.screenSpaceCameraController.maximumZoomDistance = 100000; // 最大100公里

    // 固定视角到珠海市中心
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_BOUNDS.center.longitude,
        ZHUHAI_BOUNDS.center.latitude,
        ZHUHAI_BOUNDS.center.height
      ),
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-60),  // 更陡的俯视角度
        roll: 0.0
      }
    });

    // 启用深度测试和阴影
    viewer.scene.globe.depthTestAgainstTerrain = true;
    viewer.scene.globe.enableLighting = true;

    console.log('Cesium地球初始化成功 - 已固定在珠海市');
  } catch (error) {
    console.error('Cesium初始化失败:', error);
  }
}

// 初始化珠海地图
function initZhuhaiMap() {
  try {
    // 添加建筑物图层
    if (layers.buildings) {
      viewer.scene.primitives.add(Cesium.createOsmBuildings());
    }

    // 添加珠海市区域标记
    addZhuhaiAreaMarkers();

    // 创建低空空域
    createZhuhaiAirspace();

    console.log('珠海地图初始化完成');
  } catch (error) {
    console.error('珠海地图初始化失败:', error);
  }
}

// 添加珠海市区域标记
function addZhuhaiAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(area.longitude, area.latitude, 100),
      billboard: {
        image: createAreaIcon(area.icon),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        scale: 1.2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: area.name,
        font: '16pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 3,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -60),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    entity.userData = area;
    labelEntities.push(entity);
  });
}

// 创建区域图标
function createAreaIcon(iconText) {
  const canvas = document.createElement('canvas');
  canvas.width = 64;
  canvas.height = 64;
  const context = canvas.getContext('2d');

  // 绘制圆形背景
  context.fillStyle = 'rgba(0, 0, 0, 0.8)';
  context.beginPath();
  context.arc(32, 32, 30, 0, 2 * Math.PI);
  context.fill();

  // 绘制图标
  context.font = '24px Arial';
  context.textAlign = 'center';
  context.textBaseline = 'middle';
  context.fillStyle = 'white';
  context.fillText(iconText, 32, 32);

  return canvas;
}

// 创建珠海低空空域
function createZhuhaiAirspace() {
  const airspaceZones = [
    {
      name: '香洲低空空域',
      coordinates: [
        113.55, 22.25,
        113.60, 22.25,
        113.60, 22.30,
        113.55, 22.30
      ],
      height: 1000,
      color: Cesium.Color.CYAN
    },
    {
      name: '机场管制空域',
      coordinates: [
        113.35, 22.00,
        113.40, 22.00,
        113.40, 22.05,
        113.35, 22.05
      ],
      height: 3000,
      color: Cesium.Color.ORANGE
    },
    {
      name: '横琴新区空域',
      coordinates: [
        113.50, 22.10,
        113.55, 22.10,
        113.55, 22.15,
        113.50, 22.15
      ],
      height: 1500,
      color: Cesium.Color.PURPLE
    }
  ];

  airspaceZones.forEach(zone => {
    const entity = viewer.entities.add({
      name: zone.name,
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(zone.coordinates),
        height: 0,
        extrudedHeight: zone.height,
        material: zone.color.withAlpha(0.3),
        outline: true,
        outlineColor: zone.color,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });
    airspaceEntities.push(entity);
  });
}

// 初始化气象系统
function initWeatherSystem() {
  console.log('初始化气象预警系统...');
}

// 生成初始预警数据
function generateInitialWarnings() {
  const initialWarnings = [
    {
      type: '大风',
      level: 'yellow',
      location: '香洲区',
      longitude: 113.5767,
      latitude: 22.2711,
      intensity: 0.6
    },
    {
      type: '暴雨',
      level: 'orange',
      location: '横琴新区',
      longitude: 113.5200,
      latitude: 22.1300,
      intensity: 0.8
    }
  ];

  initialWarnings.forEach(warning => {
    createWarning(warning);
  });
}

// 创建新预警
function createWarning(warningData) {
  const warning = {
    id: ++warningIdCounter,
    type: warningData.type,
    level: warningData.level,
    location: warningData.location,
    longitude: warningData.longitude,
    latitude: warningData.latitude,
    intensity: warningData.intensity,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  // 创建三维预警实体
  createWeatherEntity(warning);

  // 添加到预警列表
  activeWarnings.value.push(warning);

  console.log(`创建${warning.type}预警:`, warning);
  return warning;
}

// 生成新预警
function generateNewWarning() {
  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷风'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const randomArea = areas[Math.floor(Math.random() * areas.length)];
  const randomType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const randomLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  // 在选定区域附近随机生成位置
  const offsetRange = 0.02; // 约2公里范围
  const longitude = randomArea.longitude + (Math.random() - 0.5) * offsetRange;
  const latitude = randomArea.latitude + (Math.random() - 0.5) * offsetRange;

  const newWarning = {
    type: randomType,
    level: randomLevel,
    location: randomArea.name,
    longitude: longitude,
    latitude: latitude,
    intensity: Math.random()
  };

  createWarning(newWarning);
  updateLastUpdateTime();
}

// 清除所有预警
function clearAllWarnings() {
  // 移除所有预警实体
  weatherEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  weatherEntities = [];

  // 清空预警列表
  activeWarnings.value = [];

  updateLastUpdateTime();
  console.log('已清除所有预警');
}

// 自动更新预警数据
function startDataUpdate() {
  updateLastUpdateTime();

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (autoUpdate.value) {
        // 随机决定是否生成新预警
        if (Math.random() < 0.3 && activeWarnings.value.length < 8) {
          generateNewWarning();
        }

        // 随机移除过期预警
        if (activeWarnings.value.length > 0 && Math.random() < 0.2) {
          removeOldestWarning();
        }

        updateLastUpdateTime();
      }
    }, 8000); // 每8秒检查一次
  }
}

// 移除最旧的预警
function removeOldestWarning() {
  if (activeWarnings.value.length === 0) return;

  // 找到最旧的预警
  const oldestWarning = activeWarnings.value.reduce((oldest, current) => {
    return current.timestamp < oldest.timestamp ? current : oldest;
  });

  // 移除对应的实体
  const entityIndex = weatherEntities.findIndex(entity =>
    entity.userData && entity.userData.id === oldestWarning.id
  );

  if (entityIndex !== -1) {
    viewer.entities.remove(weatherEntities[entityIndex]);
    weatherEntities.splice(entityIndex, 1);
  }

  // 从预警列表中移除
  const warningIndex = activeWarnings.value.findIndex(w => w.id === oldestWarning.id);
  if (warningIndex !== -1) {
    activeWarnings.value.splice(warningIndex, 1);
  }

  console.log(`移除过期预警: ${oldestWarning.type} - ${oldestWarning.location}`);
}

// 切换自动更新
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    startDataUpdate();
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
  }

  console.log(`自动更新: ${autoUpdate.value ? '开启' : '关闭'}`);
}

// 更新最后更新时间
function updateLastUpdateTime() {
  lastUpdateTime.value = new Date().toLocaleTimeString();
}

// 创建气象预警实体
function createWeatherEntity(warning) {
  const color = getWarningColor(warning.level);
  const radius = 1000 + warning.intensity * 2000; // 1-3公里半径
  const height = 300 + warning.intensity * 1200;  // 300-1500米高度

  const entity = viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      height / 2
    ),
    ellipsoid: {
      radii: new Cesium.Cartesian3(radius, radius, height),
      material: color.withAlpha(0.6),
      outline: true,
      outlineColor: color,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    label: {
      text: `${warning.type}预警\n${getWarningLevelText(warning.level)}\n${warning.location}`,
      font: '14pt Microsoft YaHei',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -height/2 - 50),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      disableDepthTestDistance: Number.POSITIVE_INFINITY
    }
  });

  entity.userData = warning;
  weatherEntities.push(entity);

  return entity;
}

// 飞行到预警位置
function flyToWarning(warning) {
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      8000  // 8公里高度
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2.0
  });

  console.log(`飞行到预警位置: ${warning.type} - ${warning.location}`);
}

// 飞行到区域
function flyToArea(area) {
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      12000  // 12公里高度
    ),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-50),
      roll: 0.0
    },
    duration: 2.5
  });

  console.log(`飞行到区域: ${area.name}`);
}



// 获取预警颜色
function getWarningColor(level) {
  switch (level) {
    case 'red': return Cesium.Color.RED;
    case 'orange': return Cesium.Color.ORANGE;
    case 'yellow': return Cesium.Color.YELLOW;
    case 'blue': return Cesium.Color.BLUE;
    default: return Cesium.Color.GRAY;
  }
}

// 统计函数
function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(warning => warning.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(warning => warning.location === area.name).length;
}

// 图层控制函数
function toggleBuildings() {
  const primitives = viewer.scene.primitives;
  const osmBuildings = primitives._primitives.find(p => p._url && p._url.includes('buildings'));

  if (layers.buildings && !osmBuildings) {
    primitives.add(Cesium.createOsmBuildings());
  } else if (!layers.buildings && osmBuildings) {
    primitives.remove(osmBuildings);
  }

  console.log(`建筑物图层: ${layers.buildings ? '开启' : '关闭'}`);
}

function toggleWeather() {
  weatherEntities.forEach(entity => {
    entity.show = layers.weather;
  });

  console.log(`气象预警图层: ${layers.weather ? '开启' : '关闭'}`);
}

function toggleAirspace() {
  airspaceEntities.forEach(entity => {
    entity.show = layers.airspace;
  });

  console.log(`低空空域图层: ${layers.airspace ? '开启' : '关闭'}`);
}

function toggleLabels() {
  labelEntities.forEach(entity => {
    entity.show = layers.labels;
  });

  console.log(`区域标签图层: ${layers.labels ? '开启' : '关闭'}`);
}

// 辅助函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊',
    '龙卷风': '🌪️'
  };
  return icons[type] || '⚠️';
}

function getWarningLevelText(level) {
  const levelMap = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return levelMap[level] || '未知预警';
}
</script>

<style scoped>
.cesium-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red {
  border-left: 4px solid #ff0000;
  background: rgba(255, 0, 0, 0.1);
}
.level-orange {
  border-left: 4px solid #ff8800;
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow {
  border-left: 4px solid #ffff00;
  background: rgba(255, 255, 0, 0.1);
}
.level-blue {
  border-left: 4px solid #0088ff;
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 24px;
  min-width: 30px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 10px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 9px;
  color: #666;
  margin-bottom: 2px;
}

.warning-intensity {
  font-size: 9px;
  color: #888;
}

.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

.warning-control {
  margin-bottom: 20px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover {
  background: #45a049;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover {
  background: #da190b;
}

.control-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active) {
  background: #666;
  color: white;
}

.layer-control {
  margin-bottom: 15px;
}

.layer-item {
  margin-bottom: 8px;
}

.layer-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.layer-item label:hover {
  background: rgba(255, 255, 255, 0.1);
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  max-width: 250px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
}

.status-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.status-label {
  color: #ccc;
}

.status-value {
  color: white;
  font-weight: bold;
}

.status-value.online {
  color: #90EE90;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
