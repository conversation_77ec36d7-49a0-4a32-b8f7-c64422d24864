<template>
  <div id="app">
    <!-- 系统选择器 -->
    <SystemSelector v-if="currentComponent === 'selector'" />

    <!-- CesiumJS 专业版 -->
    <CesiumMapWeatherSystem v-else-if="currentComponent === 'CesiumMapWeatherSystem'" />

    <!-- Canvas 稳定版 -->
    <StableWeatherSystem v-else-if="currentComponent === 'StableWeatherSystem'" />

    <!-- Three.js 实验版 -->
    <StableWeatherSystem v-else-if="currentComponent === 'WeatherSystem'" />

    <!-- Enhanced Canvas 3D版 -->
    <Enhanced3DMapSystem v-else-if="currentComponent === 'Enhanced3DMapSystem'" />

    <!-- Three.js 专业3D版 -->
    <ThreeJSWeatherSystem v-else-if="currentComponent === 'ThreeJSWeatherSystem'" />

    <!-- CesiumJS 地理信息3D版 -->
    <CesiumZhuhaiWeatherSystem v-else-if="currentComponent === 'CesiumZhuhaiWeatherSystem'" />

    <!-- 返回按钮 -->
    <button
      v-if="currentComponent !== 'selector'"
      @click="backToSelector"
      class="back-button"
      title="返回系统选择器"
    >
      🏠 返回选择器
    </button>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import SystemSelector from './components/SystemSelector.vue';
import CesiumMapWeatherSystem from './components/ReliableCesiumSystem.vue';
import StableWeatherSystem from './components/StableWeatherSystem.vue';
import Enhanced3DMapSystem from './components/Enhanced3DMapSystem.vue';
import ThreeJSWeatherSystem from './components/ThreeJSWeatherSystem.vue';
import CesiumZhuhaiWeatherSystem from './components/CesiumZhuhaiWeatherSystem.vue';

// 当前组件
const currentComponent = ref('CesiumZhuhaiWeatherSystem'); // 默认启动CesiumJS版本

// 检测WebGL支持
const checkWebGLSupport = () => {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  } catch (e) {
    return false;
  }
};

// 自动降级机制
const initializeSystem = () => {
  // 检查WebGL支持
  if (!checkWebGLSupport()) {
    console.log('🔄 WebGL不支持，自动切换到Canvas版本');
    currentComponent.value = 'Enhanced3DMapSystem';
    showNotification('WebGL不支持，已自动切换到Canvas版本', 'info');
    return;
  }

  // 监听Three.js初始化错误
  window.addEventListener('threejs-error', () => {
    console.log('🔄 Three.js初始化失败，切换到Canvas版本');
    currentComponent.value = 'Enhanced3DMapSystem';
    showNotification('Three.js初始化失败，已切换到Canvas版本', 'info');
  });
};

// 通知系统
const notification = ref(null);

const showNotification = (message, type = 'info') => {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 5000);
};

// 组件切换函数
const switchToComponent = (componentName) => {
  currentComponent.value = componentName;
  showNotification(`已切换到${getComponentDisplayName(componentName)}`, 'success');
};

const getComponentDisplayName = (componentName) => {
  const names = {
    'CesiumMapWeatherSystem': 'CesiumJS版本',
    'ThreeJSWeatherSystem': 'Three.js版本',
    'Enhanced3DMapSystem': 'Canvas 3D版本',
    'WeatherSystem': 'Three.js实验版'
  };
  return names[componentName] || componentName;
};

// 初始化
onMounted(() => {
  initializeSystem();

  // 监听CesiumJS切换事件
  window.addEventListener('switchToCanvas', () => {
    switchToComponent('Enhanced3DMapSystem');
  });
});

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('switchToCanvas', () => {});
});

// 版本切换处理
function handleVersionSwitch(event) {
  const { component } = event.detail;
  currentComponent.value = component;
  console.log(`🔄 切换到组件: ${component}`);
}

// 返回选择器
function backToSelector() {
  currentComponent.value = 'selector';
  console.log('🏠 返回系统选择器');
}

// 生命周期
onMounted(() => {
  window.addEventListener('switchVersion', handleVersionSwitch);
  console.log('🚀 应用启动，当前组件:', currentComponent.value);
});

onUnmounted(() => {
  window.removeEventListener('switchVersion', handleVersionSwitch);
});
</script>

<style>
@import './styles/weather-system.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
  position: relative;
}

.back-button {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 10000;
  padding: 12px 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.back-button:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10001;
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  max-width: 400px;
  animation: slideIn 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification.success {
  background: rgba(76, 175, 80, 0.9);
}

.notification.error {
  background: rgba(244, 67, 54, 0.9);
}

.notification.info {
  background: rgba(33, 150, 243, 0.9);
}

.notification.warning {
  background: rgba(255, 152, 0, 0.9);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
