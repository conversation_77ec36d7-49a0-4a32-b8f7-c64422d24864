/**
 * 增强真实系统
 * 逐步集成真实地形、建筑和无人机
 */

import * as THREE from 'three';
import { ZHUHAI_BOUNDS, latLngToWorldCoords } from './zhuhaiTerrain.js';

/**
 * 创建增强的真实地形
 */
export function createEnhancedRealisticTerrain(scene, options = {}) {
  const {
    resolution = 300,
    scale = 10000,
    enableWater = true,
    enableBuildings = true
  } = options;

  const terrainGroup = new THREE.Group();

  // 创建基础地形
  const baseTerrain = createBaseTerrain(resolution, scale);
  terrainGroup.add(baseTerrain);

  // 添加水面
  if (enableWater) {
    const water = createEnhancedWater(scale);
    terrainGroup.add(water);
  }

  // 添加简化建筑
  if (enableBuildings) {
    const buildings = createSimplifiedBuildings(scale);
    terrainGroup.add(buildings);
  }

  scene.add(terrainGroup);

  return {
    group: terrainGroup,
    mainTerrain: baseTerrain,
    water: enableWater ? terrainGroup.children.find(child => child.userData.type === 'water') : null,
    buildings: enableBuildings ? terrainGroup.children.find(child => child.userData.type === 'buildings') : null
  };
}

/**
 * 创建基础地形
 */
function createBaseTerrain(resolution, scale) {
  const geometry = new THREE.PlaneGeometry(scale, scale, resolution - 1, resolution - 1);
  const vertices = geometry.attributes.position.array;

  // 生成高程数据
  const heightData = generateEnhancedHeightData(resolution, scale);

  // 应用高程数据
  for (let i = 0; i < heightData.length; i++) {
    vertices[i * 3 + 2] = heightData[i];
  }

  geometry.attributes.position.needsUpdate = true;
  geometry.computeVertexNormals();

  // 创建增强材质
  const material = new THREE.MeshLambertMaterial({
    vertexColors: true,
    wireframe: false
  });

  // 添加顶点颜色
  const colors = [];
  for (let i = 0; i < heightData.length; i++) {
    const height = heightData[i];
    const color = getEnhancedTerrainColor(height);
    colors.push(color.r / 255, color.g / 255, color.b / 255);
  }
  geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

  const mesh = new THREE.Mesh(geometry, material);
  mesh.rotation.x = -Math.PI / 2;
  mesh.receiveShadow = true;
  mesh.userData.type = 'terrain';

  return mesh;
}

/**
 * 生成增强高程数据
 */
function generateEnhancedHeightData(resolution, scale) {
  const heightData = [];

  for (let y = 0; y < resolution; y++) {
    for (let x = 0; x < resolution; x++) {
      const worldX = (x / resolution - 0.5) * scale;
      const worldZ = (y / resolution - 0.5) * scale;

      // 基础噪声
      let elevation = 0;

      // 多层噪声叠加
      elevation += Math.sin(worldX * 0.0005) * Math.cos(worldZ * 0.0005) * 50;
      elevation += Math.sin(worldX * 0.001) * Math.cos(worldZ * 0.001) * 25;
      elevation += Math.sin(worldX * 0.002) * Math.cos(worldZ * 0.002) * 12;

      // 山脉系统
      const mountainCenters = [
        { x: 2000, z: 2000, height: 400, radius: 2000 }, // 凤凰山
        { x: -1500, z: 3000, height: 500, radius: 2500 }  // 黄杨山
      ];

      mountainCenters.forEach(mountain => {
        const distance = Math.sqrt(
          Math.pow(worldX - mountain.x, 2) +
          Math.pow(worldZ - mountain.z, 2)
        );

        if (distance < mountain.radius) {
          const influence = Math.pow(1 - distance / mountain.radius, 2);
          elevation += mountain.height * influence;
        }
      });

      // 海岸线处理
      const coastDistance = Math.sqrt(worldX * worldX + worldZ * worldZ);
      if (coastDistance > scale * 0.4) {
        elevation = Math.max(-10, elevation * (1 - (coastDistance - scale * 0.4) / (scale * 0.1)));
      }

      heightData.push(Math.max(-15, elevation));
    }
  }

  return heightData;
}

/**
 * 获取增强地形颜色
 */
function getEnhancedTerrainColor(height) {
  if (height < -5) {
    return { r: 25, g: 25, b: 112 };      // 深海
  } else if (height < 0) {
    return { r: 64, g: 164, b: 223 };     // 浅海
  } else if (height < 10) {
    return { r: 238, g: 203, b: 173 };    // 海滩
  } else if (height < 30) {
    return { r: 85, g: 139, b: 47 };      // 低地
  } else if (height < 80) {
    return { r: 124, g: 252, b: 0 };      // 平原
  } else if (height < 150) {
    return { r: 107, g: 142, b: 35 };     // 丘陵
  } else if (height < 250) {
    return { r: 160, g: 82, b: 45 };      // 低山
  } else if (height < 400) {
    return { r: 139, g: 69, b: 19 };      // 中山
  } else {
    return { r: 169, g: 169, b: 169 };    // 高山
  }
}

/**
 * 创建增强水面
 */
function createEnhancedWater(scale) {
  const waterGroup = new THREE.Group();
  waterGroup.userData.type = 'water';

  const waterGeometry = new THREE.PlaneGeometry(scale * 1.2, scale * 1.2, 100, 100);
  const waterMaterial = new THREE.MeshLambertMaterial({
    color: 0x006994,
    transparent: true,
    opacity: 0.7,
    shininess: 100
  });

  const waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
  waterMesh.rotation.x = -Math.PI / 2;
  waterMesh.position.y = -2;
  waterGroup.add(waterMesh);

  // 存储动画数据
  waterMesh.userData.animate = (time) => {
    const vertices = waterGeometry.attributes.position.array;
    for (let i = 0; i < vertices.length; i += 3) {
      const x = vertices[i];
      const z = vertices[i + 1];
      vertices[i + 2] = Math.sin(time * 0.001 + x * 0.01) * 1.5 +
                        Math.cos(time * 0.0015 + z * 0.008) * 1;
    }
    waterGeometry.attributes.position.needsUpdate = true;
  };

  return waterGroup;
}

/**
 * 创建详细建筑群
 */
function createSimplifiedBuildings(scale) {
  const buildingGroup = new THREE.Group();
  buildingGroup.userData.type = 'buildings';

  // 创建极简建筑群 - 测试用
  const cityAreas = [
    // 只创建一个小区域测试
    {
      name: '测试区域',
      center: { x: 0, z: 0 },
      count: 5,
      maxHeight: 100,
      minHeight: 50,
      density: 'low',
      type: 'commercial'
    }
  ];

  // 为每个区域创建建筑群
  cityAreas.forEach(area => {
    const areaBuildings = createAreaBuildings(area);
    areaBuildings.forEach(building => buildingGroup.add(building));
  });

  // 暂时移除地标和工业区建筑进行测试

  return buildingGroup;
}

/**
 * 创建区域建筑群
 */
function createAreaBuildings(area) {
  const buildings = [];
  const spreadRadius = area.density === 'high' ? 600 : area.density === 'medium' ? 900 : 1200;

  for (let i = 0; i < area.count; i++) {
    const building = createDetailedBuilding(area);

    // 根据密度分布建筑
    let x, z;
    if (area.density === 'high') {
      // 高密度：网格分布
      const gridSize = Math.ceil(Math.sqrt(area.count));
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;
      x = area.center.x + (col - gridSize/2) * 80 + (Math.random() - 0.5) * 40;
      z = area.center.z + (row - gridSize/2) * 80 + (Math.random() - 0.5) * 40;
    } else {
      // 中低密度：随机分布
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * spreadRadius;
      x = area.center.x + Math.cos(angle) * distance;
      z = area.center.z + Math.sin(angle) * distance;
    }

    building.position.set(x, building.geometry.parameters.height / 2, z);
    buildings.push(building);
  }

  return buildings;
}

/**
 * 创建简单建筑
 */
function createDetailedBuilding(area) {
  const height = area.minHeight + Math.random() * (area.maxHeight - area.minHeight);

  // 创建简单的立方体建筑
  const width = 20 + Math.random() * 30;
  const depth = 15 + Math.random() * 25;

  const geometry = new THREE.BoxGeometry(width, height, depth);
  const material = new THREE.MeshLambertMaterial({
    color: Math.random() * 0xffffff
  });

  const building = new THREE.Mesh(geometry, material);
  building.castShadow = true;
  building.receiveShadow = true;
  building.userData.area = area.name;

  // 创建一个group来保持接口一致性
  const group = new THREE.Group();
  group.add(building);
  group.geometry = { parameters: { height } };

  return group;
}

/**
 * 获取建筑类型
 */
function getBuildingType(areaType) {
  const types = {
    'commercial': ['skyscraper', 'office', 'commercial'],
    'office': ['skyscraper', 'office'],
    'residential': ['residential'],
    'mixed': ['office', 'residential', 'commercial']
  };

  const availableTypes = types[areaType] || ['office'];
  return availableTypes[Math.floor(Math.random() * availableTypes.length)];
}

/**
 * 创建摩天大楼
 */
function createSkyscraper(height) {
  const group = new THREE.Group();

  // 主体建筑
  const width = 25 + Math.random() * 15;
  const depth = 20 + Math.random() * 10;
  const geometry = new THREE.BoxGeometry(width, height, depth);

  const material = new THREE.MeshLambertMaterial({
    color: 0x2C3E50,
    transparent: true,
    opacity: 0.9
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  // 玻璃幕墙
  const glassGeometry = new THREE.BoxGeometry(width + 0.5, height + 0.5, depth + 0.5);
  const glassMaterial = new THREE.MeshLambertMaterial({
    color: 0x87CEEB,
    transparent: true,
    opacity: 0.3
  });
  const glass = new THREE.Mesh(glassGeometry, glassMaterial);
  group.add(glass);

  // 窗户已简化

  // 顶部天线
  if (Math.random() > 0.7) {
    const antennaGeometry = new THREE.CylinderGeometry(0.5, 0.5, 20);
    const antennaMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 });
    const antenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
    antenna.position.y = height / 2 + 10;
    group.add(antenna);
  }

  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建办公楼
 */
function createOfficeBuilding(height) {
  const group = new THREE.Group();

  const width = 30 + Math.random() * 20;
  const depth = 25 + Math.random() * 15;
  const geometry = new THREE.BoxGeometry(width, height, depth);

  const material = new THREE.MeshLambertMaterial({
    color: 0x34495E
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  // 窗户已简化

  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建住宅楼
 */
function createResidentialBuilding(height) {
  const group = new THREE.Group();

  const width = 15 + Math.random() * 10;
  const depth = 12 + Math.random() * 8;
  const geometry = new THREE.BoxGeometry(width, height, depth);

  const colors = [0xE74C3C, 0xF39C12, 0x27AE60, 0x3498DB, 0x9B59B6];
  const material = new THREE.MeshLambertMaterial({
    color: colors[Math.floor(Math.random() * colors.length)]
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  // 阳台已简化

  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建商业建筑
 */
function createCommercialBuilding(height) {
  const group = new THREE.Group();

  const width = 40 + Math.random() * 20;
  const depth = 30 + Math.random() * 15;
  const geometry = new THREE.BoxGeometry(width, height, depth);

  const material = new THREE.MeshLambertMaterial({
    color: 0x95A5A6
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  // 标识已简化

  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建通用建筑
 */
function createGenericBuilding(height, type) {
  const group = new THREE.Group();

  const width = 20 + Math.random() * 25;
  const depth = 15 + Math.random() * 20;
  const geometry = new THREE.BoxGeometry(width, height, depth);

  const material = new THREE.MeshLambertMaterial({
    color: 0xBDC3C7
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  // 窗户已简化

  group.geometry = { parameters: { height } };
  return group;
}
// 装饰函数已移除以提高性能

/**
 * 创建简化工业区
 */
function createSimpleIndustrialArea() {
  const buildings = [];

  // 工业区位置
  const industrialCenter = { x: -3000, z: -2500 };

  // 少量工厂建筑
  for (let i = 0; i < 5; i++) {
    const factory = createSimpleFactory();
    factory.position.set(
      industrialCenter.x + (Math.random() - 0.5) * 800,
      factory.geometry.parameters.height / 2,
      industrialCenter.z + (Math.random() - 0.5) * 600
    );
    buildings.push(factory);
  }

  return buildings;
}

/**
 * 创建简化工厂
 */
function createSimpleFactory() {
  const group = new THREE.Group();

  const width = 40 + Math.random() * 20;
  const height = 15 + Math.random() * 10;
  const depth = 30 + Math.random() * 15;

  const geometry = new THREE.BoxGeometry(width, height, depth);
  const material = new THREE.MeshLambertMaterial({
    color: 0x7F8C8D
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建仓库
 */
function createWarehouse() {
  const group = new THREE.Group();

  const width = 80 + Math.random() * 50;
  const height = 12 + Math.random() * 8;
  const depth = 50 + Math.random() * 30;

  const geometry = new THREE.BoxGeometry(width, height, depth);
  const material = new THREE.MeshLambertMaterial({
    color: 0x95A5A6
  });

  const building = new THREE.Mesh(geometry, material);
  group.add(building);

  group.geometry = { parameters: { height } };
  return group;
}

/**
 * 创建地标建筑
 */
function createLandmarkBuildings(scale) {
  const landmarks = [];

  // 珠海机场
  const airport = createAirportComplex();
  airport.position.set(-3000, 0, -2000);
  landmarks.push(airport);

  // 港珠澳大桥
  const bridge = createBridgeStructure();
  bridge.position.set(1000, 0, 2000);
  landmarks.push(bridge);

  // 珠海中心大厦
  const centerTower = createCenterTower();
  centerTower.position.set(0, 0, 0);
  landmarks.push(centerTower);

  return landmarks;
}

/**
 * 创建机场建筑群
 */
function createAirportComplex() {
  const airportGroup = new THREE.Group();

  // 航站楼
  const terminalGeometry = new THREE.BoxGeometry(150, 25, 60);
  const terminalMaterial = new THREE.MeshLambertMaterial({ color: 0xBDC3C7 });
  const terminal = new THREE.Mesh(terminalGeometry, terminalMaterial);
  terminal.position.y = 12.5;
  airportGroup.add(terminal);

  // 控制塔
  const towerGeometry = new THREE.CylinderGeometry(5, 8, 60, 8);
  const towerMaterial = new THREE.MeshLambertMaterial({ color: 0x34495E });
  const tower = new THREE.Mesh(towerGeometry, towerMaterial);
  tower.position.set(60, 30, 0);
  airportGroup.add(tower);

  // 跑道
  const runwayGeometry = new THREE.BoxGeometry(800, 1, 30);
  const runwayMaterial = new THREE.MeshLambertMaterial({ color: 0x2C3E50 });
  const runway = new THREE.Mesh(runwayGeometry, runwayMaterial);
  runway.position.set(0, 0.5, 100);
  airportGroup.add(runway);

  return airportGroup;
}

/**
 * 创建桥梁结构
 */
function createBridgeStructure() {
  const bridgeGroup = new THREE.Group();

  // 桥面
  const deckGeometry = new THREE.BoxGeometry(1500, 5, 30);
  const deckMaterial = new THREE.MeshLambertMaterial({ color: 0x95A5A6 });
  const deck = new THREE.Mesh(deckGeometry, deckMaterial);
  deck.position.y = 40;
  bridgeGroup.add(deck);

  // 桥塔
  for (let i = -2; i <= 2; i++) {
    if (i !== 0) {
      const towerGeometry = new THREE.BoxGeometry(8, 100, 8);
      const towerMaterial = new THREE.MeshLambertMaterial({ color: 0x34495E });
      const tower = new THREE.Mesh(towerGeometry, towerMaterial);
      tower.position.set(i * 300, 50, 0);
      bridgeGroup.add(tower);
    }
  }

  return bridgeGroup;
}

/**
 * 创建中心大厦
 */
function createCenterTower() {
  const towerGroup = new THREE.Group();

  const towerGeometry = new THREE.BoxGeometry(30, 180, 25);
  const towerMaterial = new THREE.MeshLambertMaterial({ color: 0x4A90E2 });
  const tower = new THREE.Mesh(towerGeometry, towerMaterial);
  tower.position.y = 90;
  towerGroup.add(tower);

  // 添加玻璃幕墙效果
  const glassGeometry = new THREE.BoxGeometry(32, 182, 27);
  const glassMaterial = new THREE.MeshLambertMaterial({
    color: 0x87CEEB,
    transparent: true,
    opacity: 0.3
  });
  const glass = new THREE.Mesh(glassGeometry, glassMaterial);
  glass.position.y = 90;
  towerGroup.add(glass);

  return towerGroup;
}

// 复杂系统已移除以提高性能

/**
 * 更新水面动画
 */
export function updateEnhancedWater(terrainObject, time) {
  if (terrainObject && terrainObject.water) {
    const waterMesh = terrainObject.water.children[0];
    if (waterMesh && waterMesh.userData.animate) {
      waterMesh.userData.animate(time);
    }
  }
}
