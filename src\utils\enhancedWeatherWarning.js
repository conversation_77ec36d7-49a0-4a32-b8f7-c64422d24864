/**
 * 增强气象预警系统
 * 包含大风、暴雨、强风强降雨等专业气象预警
 */

// 风力等级定义（蒲福风级）
export const WIND_SCALE = {
  0: { name: '无风', range: [0, 0.2], level: 'safe', color: 0x87CEEB },
  1: { name: '软风', range: [0.3, 1.5], level: 'safe', color: 0x90EE90 },
  2: { name: '轻风', range: [1.6, 3.3], level: 'safe', color: 0x98FB98 },
  3: { name: '微风', range: [3.4, 5.4], level: 'safe', color: 0xADFF2F },
  4: { name: '和风', range: [5.5, 7.9], level: 'safe', color: 0xFFFF00 },
  5: { name: '清劲风', range: [8.0, 10.7], level: 'caution', color: 0xFFA500 },
  6: { name: '强风', range: [10.8, 13.8], level: 'warning', color: 0xFF8C00 },
  7: { name: '疾风', range: [13.9, 17.1], level: 'warning', color: 0xFF6347 },
  8: { name: '大风', range: [17.2, 20.7], level: 'danger', color: 0xFF4500 },
  9: { name: '烈风', range: [20.8, 24.4], level: 'danger', color: 0xFF0000 },
  10: { name: '狂风', range: [24.5, 28.4], level: 'extreme', color: 0xDC143C },
  11: { name: '暴风', range: [28.5, 32.6], level: 'extreme', color: 0x8B0000 },
  12: { name: '飓风', range: [32.7, Infinity], level: 'extreme', color: 0x800000 }
};

// 降雨等级定义
export const RAIN_SCALE = {
  0: { name: '无雨', range: [0, 0.1], level: 'safe', color: 0x87CEEB },
  1: { name: '小雨', range: [0.1, 2.5], level: 'safe', color: 0x87CEFA },
  2: { name: '中雨', range: [2.5, 8.0], level: 'caution', color: 0x4682B4 },
  3: { name: '大雨', range: [8.0, 20.0], level: 'warning', color: 0x1E90FF },
  4: { name: '暴雨', range: [20.0, 50.0], level: 'danger', color: 0x0000FF },
  5: { name: '大暴雨', range: [50.0, 100.0], level: 'extreme', color: 0x0000CD },
  6: { name: '特大暴雨', range: [100.0, Infinity], level: 'extreme', color: 0x000080 }
};

// 预警等级定义
export const WARNING_LEVELS = {
  blue: { 
    name: '蓝色预警', 
    priority: 1, 
    color: 0x0088ff,
    description: '天气状况可能对飞行活动产生一定影响'
  },
  yellow: { 
    name: '黄色预警', 
    priority: 2, 
    color: 0xffff00,
    description: '天气状况将对飞行活动产生较大影响'
  },
  orange: { 
    name: '橙色预警', 
    priority: 3, 
    color: 0xff8800,
    description: '天气状况将对飞行活动产生严重影响'
  },
  red: { 
    name: '红色预警', 
    priority: 4, 
    color: 0xff0000,
    description: '天气状况将对飞行活动产生极其严重影响'
  }
};

// 复合天气类型
export const COMPOSITE_WEATHER_TYPES = {
  thunderstorm: {
    name: '雷暴',
    factors: ['强风', '强降雨', '雷电'],
    minLevel: 'orange',
    description: '雷暴天气，严禁飞行'
  },
  typhoon: {
    name: '台风',
    factors: ['极强风', '暴雨'],
    minLevel: 'red',
    description: '台风影响，所有飞行活动停止'
  },
  squall: {
    name: '飑线',
    factors: ['阵风', '急降雨'],
    minLevel: 'yellow',
    description: '飑线天气，注意阵风影响'
  },
  fog: {
    name: '大雾',
    factors: ['低能见度', '高湿度'],
    minLevel: 'yellow',
    description: '大雾天气，能见度严重受限'
  }
};

/**
 * 获取风力等级
 * @param {number} windSpeed - 风速 (m/s)
 * @returns {object} 风力等级信息
 */
export function getWindScale(windSpeed) {
  for (let level = 12; level >= 0; level--) {
    const scale = WIND_SCALE[level];
    if (windSpeed >= scale.range[0] && windSpeed <= scale.range[1]) {
      return { level, ...scale };
    }
  }
  return { level: 0, ...WIND_SCALE[0] };
}

/**
 * 获取降雨等级
 * @param {number} rainfall - 降雨量 (mm/h)
 * @returns {object} 降雨等级信息
 */
export function getRainScale(rainfall) {
  for (let level = 6; level >= 0; level--) {
    const scale = RAIN_SCALE[level];
    if (rainfall >= scale.range[0] && rainfall <= scale.range[1]) {
      return { level, ...scale };
    }
  }
  return { level: 0, ...RAIN_SCALE[0] };
}

/**
 * 生成增强的天气预警
 * @param {Array} windData - 风场数据
 * @param {Array} temperatureData - 温度数据
 * @param {Array} humidityData - 湿度数据
 * @param {Array} precipitationData - 降水数据
 * @returns {Array} 预警数组
 */
export function generateEnhancedWeatherWarnings(windData, temperatureData, humidityData, precipitationData) {
  const warnings = [];

  // 大风预警
  const windWarnings = generateWindWarnings(windData);
  warnings.push(...windWarnings);

  // 暴雨预警
  const rainWarnings = generateRainWarnings(precipitationData);
  warnings.push(...rainWarnings);

  // 复合天气预警
  const compositeWarnings = generateCompositeWarnings(windData, precipitationData, humidityData);
  warnings.push(...compositeWarnings);

  // 特殊天气预警
  const specialWarnings = generateSpecialWarnings(windData, temperatureData, humidityData);
  warnings.push(...specialWarnings);

  return warnings;
}

/**
 * 生成大风预警
 * @param {Array} windData - 风场数据
 * @returns {Array} 大风预警数组
 */
function generateWindWarnings(windData) {
  const warnings = [];

  windData.forEach(point => {
    const windScale = getWindScale(point.speed);
    
    if (windScale.level >= 6) { // 6级以上强风
      let warningLevel = 'blue';
      let description = `${windScale.name}预警`;
      
      if (windScale.level >= 8) {
        warningLevel = 'yellow';
        description = `大风预警 - ${windScale.name}`;
      }
      if (windScale.level >= 10) {
        warningLevel = 'orange';
        description = `强风预警 - ${windScale.name}`;
      }
      if (windScale.level >= 12) {
        warningLevel = 'red';
        description = `极端大风预警 - ${windScale.name}`;
      }

      warnings.push({
        id: `wind_${point.x}_${point.z}_${Date.now()}`,
        type: '大风预警',
        subType: windScale.name,
        level: warningLevel,
        x: point.x,
        y: point.y,
        z: point.z,
        radius: 300 + windScale.level * 50,
        height: 200 + windScale.level * 30,
        description: `${description}，风速 ${point.speed.toFixed(1)}m/s`,
        windSpeed: point.speed,
        windScale: windScale.level,
        time: new Date(),
        severity: windScale.level >= 10 ? 'extreme' : windScale.level >= 8 ? 'severe' : 'moderate',
        recommendations: getWindRecommendations(windScale.level)
      });
    }
  });

  return warnings;
}

/**
 * 生成暴雨预警
 * @param {Array} precipitationData - 降水数据
 * @returns {Array} 暴雨预警数组
 */
function generateRainWarnings(precipitationData) {
  const warnings = [];

  precipitationData.forEach(point => {
    const rainScale = getRainScale(point.intensity);
    
    if (rainScale.level >= 3) { // 大雨以上
      let warningLevel = 'blue';
      let description = `${rainScale.name}预警`;
      
      if (rainScale.level >= 4) {
        warningLevel = 'yellow';
        description = `暴雨预警 - ${rainScale.name}`;
      }
      if (rainScale.level >= 5) {
        warningLevel = 'orange';
        description = `大暴雨预警 - ${rainScale.name}`;
      }
      if (rainScale.level >= 6) {
        warningLevel = 'red';
        description = `特大暴雨预警 - ${rainScale.name}`;
      }

      warnings.push({
        id: `rain_${point.x}_${point.z}_${Date.now()}`,
        type: '暴雨预警',
        subType: rainScale.name,
        level: warningLevel,
        x: point.x,
        y: point.y,
        z: point.z,
        radius: 400 + rainScale.level * 100,
        height: 150 + rainScale.level * 25,
        description: `${description}，降雨量 ${point.intensity.toFixed(1)}mm/h`,
        rainfall: point.intensity,
        rainScale: rainScale.level,
        visibility: point.visibility,
        time: new Date(),
        severity: rainScale.level >= 5 ? 'extreme' : rainScale.level >= 4 ? 'severe' : 'moderate',
        recommendations: getRainRecommendations(rainScale.level)
      });
    }
  });

  return warnings;
}

/**
 * 生成复合天气预警
 * @param {Array} windData - 风场数据
 * @param {Array} precipitationData - 降水数据
 * @param {Array} humidityData - 湿度数据
 * @returns {Array} 复合天气预警数组
 */
function generateCompositeWarnings(windData, precipitationData, humidityData) {
  const warnings = [];
  const threshold = 200; // 影响范围阈值（米）

  windData.forEach(windPoint => {
    const windScale = getWindScale(windPoint.speed);
    
    if (windScale.level >= 7) { // 疾风以上
      // 查找附近的强降雨
      const nearbyRain = precipitationData.find(rainPoint => {
        const distance = Math.sqrt(
          (windPoint.x - rainPoint.x) ** 2 + 
          (windPoint.z - rainPoint.z) ** 2
        );
        return distance < threshold && rainPoint.intensity > 10; // 大雨以上
      });

      if (nearbyRain) {
        const rainScale = getRainScale(nearbyRain.intensity);
        
        // 判断复合天气类型
        let weatherType = 'squall';
        let warningLevel = 'yellow';
        
        if (windScale.level >= 9 && rainScale.level >= 4) {
          weatherType = 'thunderstorm';
          warningLevel = 'orange';
        }
        
        if (windScale.level >= 11 && rainScale.level >= 5) {
          weatherType = 'typhoon';
          warningLevel = 'red';
        }

        const compositeType = COMPOSITE_WEATHER_TYPES[weatherType];
        
        warnings.push({
          id: `composite_${windPoint.x}_${windPoint.z}_${Date.now()}`,
          type: '复合天气预警',
          subType: compositeType.name,
          level: warningLevel,
          x: windPoint.x,
          y: windPoint.y,
          z: windPoint.z,
          radius: 500 + (windScale.level + rainScale.level) * 50,
          height: 300 + (windScale.level + rainScale.level) * 30,
          description: `${compositeType.description}，风速${windPoint.speed.toFixed(1)}m/s，降雨${nearbyRain.intensity.toFixed(1)}mm/h`,
          windSpeed: windPoint.speed,
          rainfall: nearbyRain.intensity,
          factors: compositeType.factors,
          time: new Date(),
          severity: 'extreme',
          recommendations: getCompositeRecommendations(weatherType)
        });
      }
    }
  });

  return warnings;
}

/**
 * 生成特殊天气预警
 * @param {Array} windData - 风场数据
 * @param {Array} temperatureData - 温度数据
 * @param {Array} humidityData - 湿度数据
 * @returns {Array} 特殊天气预警数组
 */
function generateSpecialWarnings(windData, temperatureData, humidityData) {
  const warnings = [];

  // 大雾预警
  humidityData.forEach(point => {
    if (point.humidity > 95 && point.cloudCover > 80) {
      warnings.push({
        id: `fog_${point.x}_${point.z}_${Date.now()}`,
        type: '大雾预警',
        subType: '浓雾',
        level: 'yellow',
        x: point.x,
        y: point.y,
        z: point.z,
        radius: 600,
        height: 100,
        description: `大雾预警，湿度${point.humidity.toFixed(1)}%，能见度严重受限`,
        humidity: point.humidity,
        visibility: Math.max(50, 1000 - point.humidity * 10),
        time: new Date(),
        severity: 'moderate',
        recommendations: ['暂停低空飞行', '启用仪表飞行', '增加安全间距']
      });
    }
  });

  // 结冰预警
  temperatureData.forEach(point => {
    if (point.temperature < 2 && point.temperature > -10) {
      const nearbyHumidity = humidityData.find(h => 
        Math.sqrt((h.x - point.x) ** 2 + (h.z - point.z) ** 2) < 100
      );
      
      if (nearbyHumidity && nearbyHumidity.humidity > 80) {
        warnings.push({
          id: `ice_${point.x}_${point.z}_${Date.now()}`,
          type: '结冰预警',
          subType: '机体结冰',
          level: 'orange',
          x: point.x,
          y: point.y,
          z: point.z,
          radius: 300,
          height: 200,
          description: `结冰预警，温度${point.temperature.toFixed(1)}°C，湿度${nearbyHumidity.humidity.toFixed(1)}%`,
          temperature: point.temperature,
          humidity: nearbyHumidity.humidity,
          time: new Date(),
          severity: 'severe',
          recommendations: ['避免该区域飞行', '检查除冰设备', '降低飞行高度']
        });
      }
    }
  });

  return warnings;
}

/**
 * 获取大风预警建议
 * @param {number} windLevel - 风力等级
 * @returns {Array} 建议数组
 */
function getWindRecommendations(windLevel) {
  if (windLevel >= 12) {
    return ['立即停止所有飞行活动', '无人机紧急降落', '人员撤离到安全区域'];
  } else if (windLevel >= 10) {
    return ['停止低空飞行', '大型无人机谨慎飞行', '增加安全监控'];
  } else if (windLevel >= 8) {
    return ['小型无人机停飞', '降低飞行高度', '缩短飞行时间'];
  } else if (windLevel >= 6) {
    return ['注意阵风影响', '增加飞行间距', '准备应急预案'];
  }
  return ['正常飞行，注意风向变化'];
}

/**
 * 获取暴雨预警建议
 * @param {number} rainLevel - 降雨等级
 * @returns {Array} 建议数组
 */
function getRainRecommendations(rainLevel) {
  if (rainLevel >= 6) {
    return ['停止所有飞行活动', '设备防水保护', '人员安全撤离'];
  } else if (rainLevel >= 5) {
    return ['停止低空飞行', '无人机返航', '加强设备保护'];
  } else if (rainLevel >= 4) {
    return ['缩短飞行时间', '避开降雨中心', '准备紧急降落'];
  } else if (rainLevel >= 3) {
    return ['注意能见度变化', '降低飞行速度', '保持通讯畅通'];
  }
  return ['正常飞行，注意降雨变化'];
}

/**
 * 获取复合天气预警建议
 * @param {string} weatherType - 天气类型
 * @returns {Array} 建议数组
 */
function getCompositeRecommendations(weatherType) {
  switch (weatherType) {
    case 'typhoon':
      return ['立即停止所有飞行', '设备加固保护', '人员紧急撤离', '启动应急预案'];
    case 'thunderstorm':
      return ['禁止飞行活动', '防雷电保护', '远离雷暴区域', '监控天气变化'];
    case 'squall':
      return ['暂停低空飞行', '注意阵风影响', '准备紧急避让', '加强监控'];
    case 'fog':
      return ['启用仪表飞行', '增加安全间距', '降低飞行速度', '保持通讯'];
    default:
      return ['密切关注天气变化', '做好应急准备'];
  }
}

/**
 * 计算预警优先级
 * @param {object} warning - 预警对象
 * @returns {number} 优先级分数
 */
export function calculateWarningPriority(warning) {
  let priority = WARNING_LEVELS[warning.level].priority * 10;
  
  // 复合天气优先级更高
  if (warning.type === '复合天气预警') {
    priority += 20;
  }
  
  // 极端天气优先级最高
  if (warning.severity === 'extreme') {
    priority += 30;
  }
  
  return priority;
}

/**
 * 过滤和排序预警
 * @param {Array} warnings - 预警数组
 * @returns {Array} 排序后的预警数组
 */
export function sortWarningsByPriority(warnings) {
  return warnings.sort((a, b) => {
    return calculateWarningPriority(b) - calculateWarningPriority(a);
  });
}
