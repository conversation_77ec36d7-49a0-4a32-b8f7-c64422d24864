<template>
  <div class="info-panel">
    <!-- 预警信息 -->
    <div class="info-section">
      <h3>🚨 当前预警</h3>
      <div v-if="warnings.length === 0" class="no-warnings">
        <span class="status-safe">✅ 暂无预警</span>
      </div>
      <div v-else class="warnings-list">
        <div 
          v-for="(warning, index) in warnings" 
          :key="index"
          class="warning-item"
          :class="`warning-${warning.level}`"
        >
          <div class="warning-header">
            <span class="warning-level">{{ getWarningLevelText(warning.level) }}</span>
            <span class="warning-time">{{ formatTime(warning.time) }}</span>
          </div>
          <div class="warning-content">
            <p><strong>类型:</strong> {{ warning.type }}</p>
            <p><strong>位置:</strong> ({{ warning.x.toFixed(0) }}, {{ warning.z.toFixed(0) }})</p>
            <p><strong>影响范围:</strong> {{ warning.radius }}m</p>
            <p><strong>描述:</strong> {{ warning.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中点信息 -->
    <div v-if="selectedPoint" class="info-section">
      <h3>📍 选中位置信息</h3>
      <div class="selected-point-info">
        <div class="coordinate-info">
          <p><strong>坐标:</strong></p>
          <p>X: {{ selectedPoint.x.toFixed(2) }}m</p>
          <p>Y: {{ selectedPoint.y.toFixed(2) }}m</p>
          <p>Z: {{ selectedPoint.z.toFixed(2) }}m</p>
        </div>
        <!-- 地标建筑信息 -->
        <div class="landmark-info" v-if="selectedPoint.landmark">
          <p><strong>🏛️ 地标建筑:</strong></p>
          <div class="landmark-details">
            <div class="landmark-item">
              <span class="landmark-icon">🏢</span>
              <span><strong>{{ selectedPoint.landmark.name }}</strong></span>
            </div>
            <div class="landmark-item">
              <span class="landmark-icon">🏷️</span>
              <span>类型: {{ getLandmarkTypeText(selectedPoint.landmark.type) }}</span>
            </div>
            <div class="landmark-item">
              <span class="landmark-icon">📝</span>
              <span>{{ selectedPoint.landmark.description }}</span>
            </div>
          </div>
        </div>

        <div class="weather-info" v-if="selectedPoint.weather">
          <p><strong>天气数据:</strong></p>
          <div class="weather-item">
            <span class="weather-icon">🌡️</span>
            <span>温度: {{ selectedPoint.weather.temperature.toFixed(1) }}°C</span>
          </div>
          <div class="weather-item">
            <span class="weather-icon">💨</span>
            <span>风速: {{ selectedPoint.weather.windSpeed.toFixed(1) }}m/s</span>
          </div>
          <div class="weather-item">
            <span class="weather-icon">💧</span>
            <span>湿度: {{ selectedPoint.weather.humidity.toFixed(1) }}%</span>
          </div>
          <div class="weather-item">
            <span class="weather-icon">📊</span>
            <span>气压: {{ selectedPoint.weather.pressure.toFixed(1) }}hPa</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 整体天气概况 -->
    <div class="info-section">
      <h3>🌤️ 天气概况</h3>
      <div class="weather-overview">
        <div class="overview-item">
          <span class="overview-label">平均温度:</span>
          <span class="overview-value">{{ getAverageTemperature() }}°C</span>
        </div>
        <div class="overview-item">
          <span class="overview-label">平均风速:</span>
          <span class="overview-value">{{ getAverageWindSpeed() }}m/s</span>
        </div>
        <div class="overview-item">
          <span class="overview-label">最高温度:</span>
          <span class="overview-value">{{ getMaxTemperature() }}°C</span>
        </div>
        <div class="overview-item">
          <span class="overview-label">最大风速:</span>
          <span class="overview-value">{{ getMaxWindSpeed() }}m/s</span>
        </div>
        <div class="overview-item">
          <span class="overview-label">预警数量:</span>
          <span class="overview-value">{{ warnings.length }}</span>
        </div>
      </div>
    </div>

    <!-- 无人机状态 -->
    <div v-if="droneStatus.length > 0" class="info-section">
      <h3>🚁 无人机状态</h3>
      <div class="drone-status-list">
        <div
          v-for="drone in droneStatus"
          :key="drone.id"
          class="drone-item"
          :class="`status-${drone.status}`"
        >
          <div class="drone-header">
            <span class="drone-id">{{ drone.id }}</span>
            <span class="drone-type">{{ drone.type }}</span>
          </div>
          <div class="drone-info">
            <div class="info-row">
              <span>状态: {{ getStatusText(drone.status) }}</span>
              <span>电量: {{ drone.battery.toFixed(0) }}%</span>
            </div>
            <div class="info-row">
              <span>高度: {{ drone.altitude.toFixed(0) }}m</span>
              <span>速度: {{ drone.speed.toFixed(1) }}m/s</span>
            </div>
            <div v-if="drone.warnings.length > 0" class="drone-warnings">
              <span v-for="warning in drone.warnings" :key="warning.id" class="warning-tag">
                {{ warning.message }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="info-section">
      <h3>⚙️ 系统状态</h3>
      <div class="system-status">
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>数据连接: 正常</span>
        </div>
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>渲染引擎: 运行中</span>
        </div>
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>预警系统: 激活</span>
        </div>
        <div class="status-item">
          <span class="status-indicator online"></span>
          <span>无人机: {{ activeDroneCount }}/{{ droneStatus.length }} 在线</span>
        </div>
        <div class="status-item">
          <span class="status-indicator"></span>
          <span>最后更新: {{ getLastUpdateTime() }}</span>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="info-section">
      <h3>⚡ 快速操作</h3>
      <div class="quick-actions">
        <button @click="centerView" class="action-btn">
          🎯 居中视图
        </button>
        <button @click="resetView" class="action-btn">
          🔄 重置视角
        </button>
        <button @click="takeScreenshot" class="action-btn">
          📸 截图
        </button>
        <button @click="generateReport" class="action-btn">
          📋 生成报告
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 接收props
const props = defineProps({
  warnings: {
    type: Array,
    default: () => []
  },
  weatherData: {
    type: Object,
    default: () => ({})
  },
  selectedPoint: {
    type: Object,
    default: null
  },
  droneStatus: {
    type: Array,
    default: () => []
  }
});

// 方法
function getWarningLevelText(level) {
  const levelMap = {
    'red': '🔴 红色预警',
    'orange': '🟠 橙色预警', 
    'yellow': '🟡 黄色预警',
    'blue': '🔵 蓝色预警'
  };
  return levelMap[level] || '⚪ 未知预警';
}

function formatTime(time) {
  if (!time) return '--:--';
  return new Date(time).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

function getAverageTemperature() {
  if (!props.weatherData.temperature || props.weatherData.temperature.length === 0) {
    return '--';
  }
  const avg = props.weatherData.temperature.reduce((sum, item) => sum + item.temperature, 0) / props.weatherData.temperature.length;
  return avg.toFixed(1);
}

function getAverageWindSpeed() {
  if (!props.weatherData.wind || props.weatherData.wind.length === 0) {
    return '--';
  }
  const avg = props.weatherData.wind.reduce((sum, item) => sum + item.speed, 0) / props.weatherData.wind.length;
  return avg.toFixed(1);
}

function getMaxTemperature() {
  if (!props.weatherData.temperature || props.weatherData.temperature.length === 0) {
    return '--';
  }
  const max = Math.max(...props.weatherData.temperature.map(item => item.temperature));
  return max.toFixed(1);
}

function getMaxWindSpeed() {
  if (!props.weatherData.wind || props.weatherData.wind.length === 0) {
    return '--';
  }
  const max = Math.max(...props.weatherData.wind.map(item => item.speed));
  return max.toFixed(1);
}

function getLastUpdateTime() {
  return new Date().toLocaleTimeString('zh-CN');
}

// 无人机相关方法
function getStatusText(status) {
  const statusMap = {
    'idle': '待机',
    'flying': '飞行中',
    'returning': '返航中',
    'emergency': '紧急状态',
    'maintenance': '维护中'
  };
  return statusMap[status] || '未知';
}

function getLandmarkTypeText(type) {
  const typeMap = {
    'cultural': '文化建筑',
    'monument': '纪念建筑',
    'airport': '机场设施',
    'bridge': '桥梁口岸',
    'border': '边境口岸',
    'exhibition': '会展中心',
    'commercial': '商业建筑',
    'office': '办公建筑'
  };
  return typeMap[type] || '其他建筑';
}

// 计算属性
const activeDroneCount = computed(() => {
  return props.droneStatus.filter(drone => drone.status === 'flying').length;
});

// 快速操作方法
function centerView() {
  console.log('Centering view...');
  // 这里可以发送事件给父组件
}

function resetView() {
  console.log('Resetting view...');
  // 这里可以发送事件给父组件
}

function takeScreenshot() {
  console.log('Taking screenshot...');
  // 实现截图功能
}

function generateReport() {
  console.log('Generating report...');
  // 生成天气报告
  const report = {
    timestamp: new Date().toISOString(),
    warnings: props.warnings,
    weatherSummary: {
      avgTemperature: getAverageTemperature(),
      avgWindSpeed: getAverageWindSpeed(),
      maxTemperature: getMaxTemperature(),
      maxWindSpeed: getMaxWindSpeed()
    }
  };
  
  const blob = new Blob([JSON.stringify(report, null, 2)], {
    type: 'application/json'
  });
  
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `weather-report-${Date.now()}.json`;
  a.click();
  
  URL.revokeObjectURL(url);
}
</script>

<style scoped>
.info-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 350px;
  max-height: calc(100vh - 200px);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  overflow-y: auto;
}

.info-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-section h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #88ccff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-warnings {
  text-align: center;
  padding: 15px;
}

.status-safe {
  color: #00ff88;
  font-weight: bold;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.warning-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid;
}

.warning-red {
  border-left-color: #ff0000;
}

.warning-orange {
  border-left-color: #ff8800;
}

.warning-yellow {
  border-left-color: #ffff00;
}

.warning-blue {
  border-left-color: #0088ff;
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.warning-level {
  font-weight: bold;
  font-size: 12px;
}

.warning-time {
  font-size: 11px;
  color: #ccc;
}

.warning-content p {
  margin: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.selected-point-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.coordinate-info p,
.weather-info p {
  margin: 4px 0;
  font-size: 12px;
}

.weather-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 6px 0;
  font-size: 12px;
}

.weather-icon {
  font-size: 14px;
}

.landmark-info {
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  border-left: 3px solid #FFD700;
}

.landmark-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.landmark-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  line-height: 1.4;
}

.landmark-icon {
  font-size: 14px;
  min-width: 16px;
}

.weather-overview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.overview-label {
  color: #ccc;
}

.overview-value {
  font-weight: bold;
  color: #00ff88;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
}

.status-indicator.online {
  background: #00ff88;
  box-shadow: 0 0 6px #00ff88;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #555;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 11px;
  text-align: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #00ff88;
  transform: translateY(-2px);
}

/* 滚动条样式 */
.info-panel::-webkit-scrollbar {
  width: 6px;
}

.info-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb {
  background: #00ff88;
  border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb:hover {
  background: #00cc6a;
}

/* 无人机状态样式 */
.drone-status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.drone-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 10px;
  border-left: 3px solid;
}

.drone-item.status-flying {
  border-left-color: #00ff88;
}

.drone-item.status-returning {
  border-left-color: #ffff00;
}

.drone-item.status-emergency {
  border-left-color: #ff0000;
}

.drone-item.status-idle {
  border-left-color: #888888;
}

.drone-item.status-maintenance {
  border-left-color: #ff8800;
}

.drone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.drone-id {
  font-weight: bold;
  font-size: 11px;
  color: #00ff88;
}

.drone-type {
  font-size: 10px;
  color: #ccc;
}

.drone-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
}

.drone-warnings {
  margin-top: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.warning-tag {
  background: rgba(255, 0, 0, 0.2);
  color: #ff6666;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
}
</style>
