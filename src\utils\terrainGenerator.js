/**
 * 地形生成工具
 * 用于生成三维地形数据
 */

/**
 * 生成地形高度数据
 * @param {number} width - 地形宽度（网格数）
 * @param {number} height - 地形高度（网格数）
 * @param {object} options - 生成选项
 * @returns {Array} 高度数据数组
 */
export function generateTerrainData(width = 100, height = 100, options = {}) {
  const {
    scale = 0.05,           // 噪声缩放
    amplitude = 1.0,        // 高度幅度
    octaves = 4,            // 噪声层数
    persistence = 0.5,      // 持续性
    lacunarity = 2.0,       // 间隙性
    seed = Math.random()    // 随机种子
  } = options;

  const heightData = [];
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let elevation = 0;
      let frequency = scale;
      let currentAmplitude = amplitude;
      
      // 多层噪声叠加
      for (let i = 0; i < octaves; i++) {
        const noiseValue = noise2D(x * frequency + seed, y * frequency + seed);
        elevation += noiseValue * currentAmplitude;
        
        frequency *= lacunarity;
        currentAmplitude *= persistence;
      }
      
      // 添加一些特殊地形特征
      elevation += addTerrainFeatures(x, y, width, height);
      
      heightData.push(Math.max(0, elevation));
    }
  }
  
  return heightData;
}

/**
 * 简单的2D噪声函数（基于正弦波）
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @returns {number} 噪声值 (-1 到 1)
 */
function noise2D(x, y) {
  // 使用多个正弦波组合生成噪声
  const n1 = Math.sin(x * 0.1) * Math.cos(y * 0.1);
  const n2 = Math.sin(x * 0.05 + 1.5) * Math.cos(y * 0.05 + 1.5) * 0.5;
  const n3 = Math.sin(x * 0.2 + 3.0) * Math.cos(y * 0.2 + 3.0) * 0.25;
  
  return (n1 + n2 + n3) / 1.75;
}

/**
 * 添加特殊地形特征
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {number} width - 地形宽度
 * @param {number} height - 地形高度
 * @returns {number} 额外高度值
 */
function addTerrainFeatures(x, y, width, height) {
  let extraHeight = 0;
  
  // 中心山峰
  const centerX = width / 2;
  const centerY = height / 2;
  const distanceFromCenter = Math.sqrt(
    Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
  );
  
  if (distanceFromCenter < 20) {
    extraHeight += (20 - distanceFromCenter) * 0.1;
  }
  
  // 河谷（低地）
  const riverPath = Math.abs(y - (height * 0.3 + Math.sin(x * 0.1) * 10));
  if (riverPath < 5) {
    extraHeight -= (5 - riverPath) * 0.05;
  }
  
  // 山脊
  if (Math.abs(x - width * 0.7) < 3) {
    extraHeight += 0.3;
  }
  
  return extraHeight;
}

/**
 * 生成地形网格几何体数据
 * @param {Array} heightData - 高度数据
 * @param {number} width - 网格宽度
 * @param {number} height - 网格高度
 * @param {number} scale - 缩放比例
 * @returns {object} 包含顶点、法线、UV的几何体数据
 */
export function generateTerrainGeometry(heightData, width, height, scale = 20) {
  const vertices = [];
  const normals = [];
  const uvs = [];
  const indices = [];
  
  // 生成顶点
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const index = y * width + x;
      const elevation = heightData[index] * scale;
      
      vertices.push(
        (x - width / 2) * scale,   // X
        elevation,                 // Y (高度)
        (y - height / 2) * scale   // Z
      );
      
      // UV坐标
      uvs.push(x / (width - 1), y / (height - 1));
    }
  }
  
  // 计算法线
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const normal = calculateNormal(heightData, x, y, width, height, scale);
      normals.push(normal.x, normal.y, normal.z);
    }
  }
  
  // 生成索引
  for (let y = 0; y < height - 1; y++) {
    for (let x = 0; x < width - 1; x++) {
      const topLeft = y * width + x;
      const topRight = topLeft + 1;
      const bottomLeft = (y + 1) * width + x;
      const bottomRight = bottomLeft + 1;
      
      // 第一个三角形
      indices.push(topLeft, bottomLeft, topRight);
      // 第二个三角形
      indices.push(topRight, bottomLeft, bottomRight);
    }
  }
  
  return {
    vertices: new Float32Array(vertices),
    normals: new Float32Array(normals),
    uvs: new Float32Array(uvs),
    indices: new Uint16Array(indices)
  };
}

/**
 * 计算顶点法线
 * @param {Array} heightData - 高度数据
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {number} width - 网格宽度
 * @param {number} height - 网格高度
 * @param {number} scale - 缩放比例
 * @returns {object} 法线向量
 */
function calculateNormal(heightData, x, y, width, height, scale) {
  const getHeight = (px, py) => {
    if (px < 0 || px >= width || py < 0 || py >= height) return 0;
    return heightData[py * width + px] * scale;
  };
  
  const left = getHeight(x - 1, y);
  const right = getHeight(x + 1, y);
  const up = getHeight(x, y - 1);
  const down = getHeight(x, y + 1);
  
  const normal = {
    x: (left - right) / (2 * scale),
    y: 1.0,
    z: (up - down) / (2 * scale)
  };
  
  // 归一化
  const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
  normal.x /= length;
  normal.y /= length;
  normal.z /= length;
  
  return normal;
}

/**
 * 生成地形纹理数据
 * @param {Array} heightData - 高度数据
 * @param {number} width - 纹理宽度
 * @param {number} height - 纹理高度
 * @returns {ImageData} 纹理数据
 */
export function generateTerrainTexture(heightData, width, height) {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');
  const imageData = ctx.createImageData(width, height);
  
  const maxHeight = Math.max(...heightData);
  const minHeight = Math.min(...heightData);
  const heightRange = maxHeight - minHeight;
  
  for (let i = 0; i < heightData.length; i++) {
    const normalizedHeight = (heightData[i] - minHeight) / heightRange;
    const color = getTerrainColor(normalizedHeight);
    
    const pixelIndex = i * 4;
    imageData.data[pixelIndex] = color.r;     // Red
    imageData.data[pixelIndex + 1] = color.g; // Green
    imageData.data[pixelIndex + 2] = color.b; // Blue
    imageData.data[pixelIndex + 3] = 255;     // Alpha
  }
  
  return imageData;
}

/**
 * 根据高度获取地形颜色
 * @param {number} height - 归一化高度 (0-1)
 * @returns {object} RGB颜色值
 */
function getTerrainColor(height) {
  if (height < 0.2) {
    // 水面/低地 - 蓝绿色
    return { r: 64, g: 164, b: 223 };
  } else if (height < 0.4) {
    // 平原 - 绿色
    return { r: 144, g: 238, b: 144 };
  } else if (height < 0.6) {
    // 丘陵 - 黄绿色
    return { r: 173, g: 255, b: 47 };
  } else if (height < 0.8) {
    // 山地 - 棕色
    return { r: 139, g: 69, b: 19 };
  } else {
    // 高山 - 灰白色
    return { r: 220, g: 220, b: 220 };
  }
}

/**
 * 获取指定位置的地形高度
 * @param {Array} heightData - 高度数据
 * @param {number} x - 世界X坐标
 * @param {number} z - 世界Z坐标
 * @param {number} width - 地形宽度
 * @param {number} height - 地形高度
 * @param {number} scale - 缩放比例
 * @returns {number} 地形高度
 */
export function getTerrainHeightAtPosition(heightData, x, z, width, height, scale = 20) {
  // 将世界坐标转换为网格坐标
  const gridX = Math.floor((x / scale) + width / 2);
  const gridZ = Math.floor((z / scale) + height / 2);
  
  // 边界检查
  if (gridX < 0 || gridX >= width || gridZ < 0 || gridZ >= height) {
    return 0;
  }
  
  const index = gridZ * width + gridX;
  return heightData[index] * scale;
}

/**
 * 生成地形LOD（细节层次）数据
 * @param {Array} heightData - 原始高度数据
 * @param {number} width - 原始宽度
 * @param {number} height - 原始高度
 * @param {number} lodLevel - LOD等级 (1, 2, 4, 8...)
 * @returns {object} LOD地形数据
 */
export function generateTerrainLOD(heightData, width, height, lodLevel = 2) {
  const newWidth = Math.floor(width / lodLevel);
  const newHeight = Math.floor(height / lodLevel);
  const lodHeightData = [];
  
  for (let y = 0; y < newHeight; y++) {
    for (let x = 0; x < newWidth; x++) {
      const originalX = x * lodLevel;
      const originalY = y * lodLevel;
      const index = originalY * width + originalX;
      
      lodHeightData.push(heightData[index]);
    }
  }
  
  return {
    heightData: lodHeightData,
    width: newWidth,
    height: newHeight
  };
}
