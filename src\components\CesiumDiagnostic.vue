<template>
  <div class="cesium-diagnostic">
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <div class="diagnostic-panel">
      <h3>🔧 Cesium 诊断工具</h3>
      
      <div class="status-section">
        <h4>📊 系统状态</h4>
        <div class="status-item">
          <span class="label">Cesium模块:</span>
          <span class="value" :class="status.cesium.class">{{ status.cesium.text }}</span>
        </div>
        <div class="status-item">
          <span class="label">WebGL支持:</span>
          <span class="value" :class="status.webgl.class">{{ status.webgl.text }}</span>
        </div>
        <div class="status-item">
          <span class="label">地球创建:</span>
          <span class="value" :class="status.viewer.class">{{ status.viewer.text }}</span>
        </div>
        <div class="status-item">
          <span class="label">网络连接:</span>
          <span class="value" :class="status.network.class">{{ status.network.text }}</span>
        </div>
      </div>

      <div class="test-section">
        <h4>🧪 测试功能</h4>
        <button @click="testCesiumImport" class="test-btn">测试 Cesium 导入</button>
        <button @click="testWebGL" class="test-btn">测试 WebGL</button>
        <button @click="testViewer" class="test-btn">创建地球</button>
        <button @click="testZhuhai" class="test-btn">定位珠海</button>
        <button @click="testEntity" class="test-btn">添加实体</button>
      </div>

      <div class="log-section">
        <h4>📝 诊断日志</h4>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

let Cesium = null;
let viewer = null;

const cesiumContainer = ref(null);
const logs = ref([]);

const status = ref({
  cesium: { text: '未测试', class: 'pending' },
  webgl: { text: '未测试', class: 'pending' },
  viewer: { text: '未测试', class: 'pending' },
  network: { text: '未测试', class: 'pending' }
});

onMounted(() => {
  addLog('info', '🚀 开始 Cesium 诊断...');
  testWebGL();
});

onUnmounted(() => {
  if (viewer) {
    viewer.destroy();
  }
});

function addLog(type, message) {
  logs.value.push({
    type,
    time: new Date().toLocaleTimeString(),
    message
  });
  
  // 保持最多20条日志
  if (logs.value.length > 20) {
    logs.value.shift();
  }
}

async function testCesiumImport() {
  try {
    addLog('info', '开始导入 Cesium 模块...');
    status.value.cesium = { text: '导入中...', class: 'loading' };

    Cesium = await import('cesium');

    addLog('success', `✅ Cesium 导入成功，版本: ${Cesium.VERSION || '未知'}`);
    status.value.cesium = { text: '✅ 导入成功', class: 'success' };
    
    // 设置令牌
    if (Cesium.Ion) {
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJlYWE1OWUxNy1mMWZiLTQzYjYtYTQ0OS1kMWFjYmFkNjc5YzciLCJpZCI6NTc3MzMsImlhdCI6MTYyNzg0NTE4Mn0.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxk';
      addLog('info', '🔑 Cesium Ion 令牌设置成功');
    }
    
  } catch (error) {
    addLog('error', `❌ Cesium 导入失败: ${error.message}`);
    status.value.cesium = { text: '❌ 导入失败', class: 'error' };
  }
}

function testWebGL() {
  try {
    addLog('info', '检测 WebGL 支持...');
    
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (gl) {
      const renderer = gl.getParameter(gl.RENDERER);
      const vendor = gl.getParameter(gl.VENDOR);
      
      addLog('success', `✅ WebGL 支持正常`);
      addLog('info', `GPU: ${renderer}`);
      addLog('info', `厂商: ${vendor}`);
      status.value.webgl = { text: '✅ 支持', class: 'success' };
    } else {
      addLog('error', '❌ WebGL 不支持');
      status.value.webgl = { text: '❌ 不支持', class: 'error' };
    }
  } catch (error) {
    addLog('error', `❌ WebGL 检测失败: ${error.message}`);
    status.value.webgl = { text: '❌ 检测失败', class: 'error' };
  }
}

async function testViewer() {
  if (!Cesium) {
    addLog('warning', '⚠️ 请先测试 Cesium 导入');
    return;
  }
  
  try {
    addLog('info', '创建 Cesium Viewer...');
    status.value.viewer = { text: '创建中...', class: 'loading' };

    if (viewer) {
      viewer.destroy();
    }

    viewer = new Cesium.Viewer(cesiumContainer.value, {
      animation: false,
      baseLayerPicker: false,
      fullscreenButton: false,
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false
    });

    addLog('success', '✅ Cesium Viewer 创建成功');
    status.value.viewer = { text: '✅ 创建成功', class: 'success' };

    // 测试网络连接
    testNetwork();

  } catch (error) {
    addLog('error', `❌ Viewer 创建失败: ${error.message}`);
    status.value.viewer = { text: '❌ 创建失败', class: 'error' };
  }
}

function testNetwork() {
  addLog('info', '测试网络连接...');
  status.value.network = { text: '测试中...', class: 'loading' };

  // 测试能否访问 Cesium Ion 服务
  fetch('https://api.cesium.com/v1/assets/1/endpoint', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${Cesium.Ion.defaultAccessToken}`
    }
  })
  .then(response => {
    if (response.ok) {
      addLog('success', '✅ 网络连接正常');
      status.value.network = { text: '✅ 连接正常', class: 'success' };
    } else {
      addLog('warning', '⚠️ 网络连接异常，但可能仍能工作');
      status.value.network = { text: '⚠️ 连接异常', class: 'warning' };
    }
  })
  .catch(error => {
    addLog('warning', '⚠️ 网络测试失败，可能影响地图加载');
    status.value.network = { text: '⚠️ 测试失败', class: 'warning' };
  });
}

function testZhuhai() {
  if (!viewer) {
    addLog('warning', '⚠️ 请先创建 Cesium Viewer');
    return;
  }
  
  try {
    addLog('info', '定位到珠海市...');
    
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2711, 25000),
      orientation: {
        heading: 0.0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });
    
    addLog('success', '✅ 成功定位到珠海市');
    
  } catch (error) {
    addLog('error', `❌ 珠海定位失败: ${error.message}`);
  }
}

function testEntity() {
  if (!viewer) {
    addLog('warning', '⚠️ 请先创建 Cesium Viewer');
    return;
  }
  
  try {
    addLog('info', '添加测试实体...');
    
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(113.5767, 22.2711, 1000),
      point: {
        pixelSize: 20,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '珠海测试点',
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50)
      }
    });
    
    addLog('success', '✅ 测试实体添加成功');
    
  } catch (error) {
    addLog('error', `❌ 实体添加失败: ${error.message}`);
  }
}
</script>

<style scoped>
.cesium-diagnostic {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
}

.cesium-container {
  width: 100%;
  height: 100%;
  background: #000;
}

.diagnostic-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 10px;
  min-width: 350px;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
}

.diagnostic-panel h3 {
  margin: 0 0 20px 0;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.diagnostic-panel h4 {
  margin: 15px 0 10px 0;
  color: #87CEEB;
  font-size: 14px;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.status-section {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.label {
  color: #ccc;
}

.value {
  font-weight: bold;
}

.value.pending { color: #999; }
.value.loading { color: #FFA500; }
.value.success { color: #90EE90; }
.value.warning { color: #FFD700; }
.value.error { color: #FF6B6B; }

.test-section {
  margin-bottom: 20px;
}

.test-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  background: #4CAF50;
  color: white;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.3s;
}

.test-btn:hover {
  background: #45a049;
}

.log-section {
  margin-bottom: 10px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
  padding: 10px;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 10px;
  line-height: 1.4;
}

.log-time {
  color: #888;
  min-width: 60px;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message { color: #87CEEB; }
.log-item.success .log-message { color: #90EE90; }
.log-item.warning .log-message { color: #FFD700; }
.log-item.error .log-message { color: #FF6B6B; }

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}
</style>
