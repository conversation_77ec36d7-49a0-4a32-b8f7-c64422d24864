/**
 * 珠海市DEM（数字高程模型）工具
 * 用于生成珠海地区的真实地形数据
 */

// 珠海市地理边界
export const ZHUHAI_BOUNDS = {
  north: 22.5167,   // 北纬22°31'
  south: 21.9833,   // 北纬21°59'
  east: 114.0167,   // 东经114°01'
  west: 113.0333,   // 东经113°02'
  center: {
    latitude: 22.2707,
    longitude: 113.5767,
    height: 50000
  }
};

// 珠海主要山峰和地标高程数据
export const ZHUHAI_ELEVATIONS = {
  // 主要山峰
  mountains: [
    {
      name: '黄杨山',
      lat: 22.1833,
      lng: 113.4167,
      height: 583,
      radius: 3000,
      type: 'mountain'
    },
    {
      name: '板樟山',
      lat: 22.2500,
      lng: 113.3833,
      height: 532,
      radius: 2500,
      type: 'mountain'
    },
    {
      name: '凤凰山',
      lat: 22.2167,
      lng: 113.5333,
      height: 437,
      radius: 2000,
      type: 'mountain'
    },
    {
      name: '将军山',
      lat: 22.1667,
      lng: 113.4500,
      height: 320,
      radius: 1500,
      type: 'hill'
    },
    {
      name: '狮山',
      lat: 22.2833,
      lng: 113.5167,
      height: 298,
      radius: 1200,
      type: 'hill'
    }
  ],

  // 主要区域平均海拔
  regions: [
    {
      name: '香洲区',
      lat: 22.2707,
      lng: 113.5767,
      height: 15,
      radius: 5000,
      type: 'urban'
    },
    {
      name: '金湾区',
      lat: 22.1400,
      lng: 113.3600,
      height: 8,
      radius: 4000,
      type: 'coastal'
    },
    {
      name: '斗门区',
      lat: 22.2100,
      lng: 113.2900,
      height: 12,
      radius: 6000,
      type: 'rural'
    },
    {
      name: '横琴新区',
      lat: 22.1300,
      lng: 113.4200,
      height: 5,
      radius: 3000,
      type: 'island'
    }
  ],

  // 海岸线关键点
  coastline: [
    { lat: 22.3000, lng: 113.5500, height: 0 },
    { lat: 22.2800, lng: 113.5800, height: 0 },
    { lat: 22.2500, lng: 113.6000, height: 0 },
    { lat: 22.2200, lng: 113.6200, height: 0 },
    { lat: 22.2000, lng: 113.5800, height: 0 },
    { lat: 22.1800, lng: 113.5500, height: 0 },
    { lat: 22.1500, lng: 113.5200, height: 0 },
    { lat: 22.1200, lng: 113.4800, height: 0 },
    { lat: 22.1000, lng: 113.4500, height: 0 },
    { lat: 22.0800, lng: 113.4200, height: 0 }
  ]
};

/**
 * 创建CesiumJS自定义地形提供商
 * @param {object} options - 配置选项
 * @returns {object} Cesium地形提供商
 */
export function createZhuhaiTerrainProvider(options = {}) {
  const {
    resolution = 65,        // 地形分辨率
    exaggeration = 1.0,     // 高程夸张系数
    enableWater = true,     // 启用水面
    enableSmoothing = true  // 启用平滑
  } = options;

  // 使用Cesium的CustomHeightmapTerrainProvider
  return new Promise((resolve, reject) => {
    try {
      const terrainProvider = {
        requestTileGeometry: function(x, y, level, request) {
          return new Promise((resolve) => {
            const heightData = generateHeightmapTile(x, y, level, {
              resolution,
              exaggeration,
              enableWater,
              enableSmoothing
            });
            resolve(heightData);
          });
        },
        
        getLevelMaximumGeometricError: function(level) {
          return 40075017 / (1 << level) / 32;
        },
        
        getTileDataAvailable: function(x, y, level) {
          return true;
        },
        
        hasWaterMask: enableWater,
        hasVertexNormals: true
      };

      resolve(terrainProvider);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 生成指定瓦片的高程数据
 * @param {number} x - 瓦片X坐标
 * @param {number} y - 瓦片Y坐标
 * @param {number} level - 缩放级别
 * @param {object} options - 生成选项
 * @returns {Uint16Array} 高程数据
 */
function generateHeightmapTile(x, y, level, options = {}) {
  const {
    resolution = 65,
    exaggeration = 1.0,
    enableWater = true,
    enableSmoothing = true
  } = options;

  // 计算瓦片的地理边界
  const tileSize = 360 / Math.pow(2, level);
  const west = -180 + x * tileSize;
  const east = west + tileSize;
  const north = 85.05112878 - y * tileSize;
  const south = north - tileSize;

  // 检查瓦片是否与珠海区域相交
  if (east < ZHUHAI_BOUNDS.west || west > ZHUHAI_BOUNDS.east ||
      north < ZHUHAI_BOUNDS.south || south > ZHUHAI_BOUNDS.north) {
    // 不在珠海区域，返回海平面
    return new Uint16Array(resolution * resolution).fill(0);
  }

  const heightData = new Uint16Array(resolution * resolution);
  
  for (let row = 0; row < resolution; row++) {
    for (let col = 0; col < resolution; col++) {
      const index = row * resolution + col;
      
      // 计算当前点的经纬度
      const lng = west + (col / (resolution - 1)) * (east - west);
      const lat = north - (row / (resolution - 1)) * (north - south);
      
      // 计算高程
      let elevation = calculateElevationAtPoint(lat, lng);
      
      // 应用高程夸张
      elevation *= exaggeration;
      
      // 应用平滑处理
      if (enableSmoothing && row > 0 && col > 0 && row < resolution - 1 && col < resolution - 1) {
        elevation = applySmoothingFilter(heightData, row, col, resolution, elevation);
      }
      
      // 转换为Cesium高程格式（0-65535对应0-8848米）
      heightData[index] = Math.max(0, Math.min(65535, Math.floor(elevation * 7.5)));
    }
  }

  return heightData;
}

/**
 * 计算指定经纬度点的高程
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @returns {number} 高程（米）
 */
function calculateElevationAtPoint(lat, lng) {
  let elevation = 0;

  // 基础海拔（根据区域类型）
  elevation += getBaseElevation(lat, lng);

  // 添加山峰影响
  elevation += getMountainInfluence(lat, lng);

  // 添加海岸线影响
  elevation += getCoastlineInfluence(lat, lng);

  // 添加地形噪声
  elevation += getTerrainNoise(lat, lng);

  return Math.max(0, elevation);
}

/**
 * 获取基础海拔
 */
function getBaseElevation(lat, lng) {
  let baseElevation = 10; // 默认平原高度

  ZHUHAI_ELEVATIONS.regions.forEach(region => {
    const distance = calculateDistance(lat, lng, region.lat, region.lng);
    if (distance < region.radius) {
      const influence = 1 - (distance / region.radius);
      baseElevation = Math.max(baseElevation, region.height * influence);
    }
  });

  return baseElevation;
}

/**
 * 获取山峰影响
 */
function getMountainInfluence(lat, lng) {
  let mountainElevation = 0;

  ZHUHAI_ELEVATIONS.mountains.forEach(mountain => {
    const distance = calculateDistance(lat, lng, mountain.lat, mountain.lng);
    if (distance < mountain.radius) {
      const influence = Math.pow(1 - (distance / mountain.radius), 2);
      mountainElevation += mountain.height * influence;
    }
  });

  return mountainElevation;
}

/**
 * 获取海岸线影响
 */
function getCoastlineInfluence(lat, lng) {
  let minDistance = Infinity;
  
  ZHUHAI_ELEVATIONS.coastline.forEach(point => {
    const distance = calculateDistance(lat, lng, point.lat, point.lng);
    minDistance = Math.min(minDistance, distance);
  });

  // 距离海岸线越近，高程越低
  if (minDistance < 5000) { // 5公里内受影响
    const influence = 1 - (minDistance / 5000);
    return -influence * 15; // 最多降低15米
  }

  return 0;
}

/**
 * 获取地形噪声
 */
function getTerrainNoise(lat, lng) {
  // 使用简单的正弦波生成地形噪声
  const noise1 = Math.sin(lat * 1000) * Math.cos(lng * 1000) * 3;
  const noise2 = Math.sin(lat * 2000) * Math.cos(lng * 2000) * 1.5;
  const noise3 = Math.sin(lat * 4000) * Math.cos(lng * 4000) * 0.75;
  
  return noise1 + noise2 + noise3;
}

/**
 * 计算两点间距离（米）
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * 应用平滑滤波器
 */
function applySmoothingFilter(heightData, row, col, resolution, currentElevation) {
  // 简单的3x3平均滤波
  let sum = currentElevation;
  let count = 1;

  for (let dr = -1; dr <= 1; dr++) {
    for (let dc = -1; dc <= 1; dc++) {
      if (dr === 0 && dc === 0) continue;
      
      const newRow = row + dr;
      const newCol = col + dc;
      
      if (newRow >= 0 && newRow < resolution && newCol >= 0 && newCol < resolution) {
        const index = newRow * resolution + newCol;
        if (heightData[index] > 0) {
          sum += heightData[index] / 7.5; // 转换回米
          count++;
        }
      }
    }
  }

  return sum / count;
}

/**
 * 导出珠海地形数据为GeoTIFF格式（用于外部DEM工具）
 * @param {number} resolution - 分辨率
 * @returns {object} 地形数据对象
 */
export function exportZhuhaiDEM(resolution = 1000) {
  const heightData = [];
  const bounds = ZHUHAI_BOUNDS;
  
  const latStep = (bounds.north - bounds.south) / resolution;
  const lngStep = (bounds.east - bounds.west) / resolution;

  for (let row = 0; row < resolution; row++) {
    for (let col = 0; col < resolution; col++) {
      const lat = bounds.north - row * latStep;
      const lng = bounds.west + col * lngStep;
      const elevation = calculateElevationAtPoint(lat, lng);
      heightData.push(elevation);
    }
  }

  return {
    data: heightData,
    width: resolution,
    height: resolution,
    bounds: bounds,
    metadata: {
      title: '珠海市数字高程模型',
      description: '基于真实地理数据生成的珠海地区DEM',
      resolution: resolution,
      unit: 'meters',
      datum: 'WGS84',
      projection: 'EPSG:4326'
    }
  };
}
