/**
 * 珠海市真实地理数据
 * 基于实际地理坐标和建筑信息
 */

// 珠海市边界坐标 (WGS84)
export const ZHUHAI_BOUNDS = {
  north: 22.5159,
  south: 21.7098,
  east: 113.7759,
  west: 113.0354,
  center: {
    lat: 22.2711,
    lng: 113.5767
  }
};

// 坐标转换函数 - 将经纬度转换为Three.js世界坐标
export function latLngToWorld(lat, lng, scale = 10000) {
  const centerLat = ZHUHAI_BOUNDS.center.lat;
  const centerLng = ZHUHAI_BOUNDS.center.lng;
  
  // 简化的墨卡托投影
  const x = (lng - centerLng) * scale * 100;
  const z = -(lat - centerLat) * scale * 111; // 纬度1度约111km
  
  return { x, z };
}

// 珠海市主要区域数据
export const ZHUHAI_DISTRICTS = [
  {
    name: '香洲区',
    center: { lat: 22.2711, lng: 113.5767 },
    bounds: {
      north: 22.3200, south: 22.2200,
      east: 113.6200, west: 113.5300
    },
    type: 'commercial',
    population: 1200000,
    buildingDensity: 'high'
  },
  {
    name: '金湾区',
    center: { lat: 22.1300, lng: 113.3600 },
    bounds: {
      north: 22.2000, south: 22.0600,
      east: 113.4200, west: 113.3000
    },
    type: 'industrial',
    population: 300000,
    buildingDensity: 'medium'
  },
  {
    name: '斗门区',
    center: { lat: 22.2100, lng: 113.2900 },
    bounds: {
      north: 22.3000, south: 22.1200,
      east: 113.3500, west: 113.2300
    },
    type: 'residential',
    population: 400000,
    buildingDensity: 'low'
  },
  {
    name: '横琴新区',
    center: { lat: 22.1300, lng: 113.5200 },
    bounds: {
      north: 22.1600, south: 22.1000,
      east: 113.5600, west: 113.4800
    },
    type: 'financial',
    population: 50000,
    buildingDensity: 'high'
  }
];

// 珠海市重要地标建筑
export const ZHUHAI_LANDMARKS = [
  {
    name: '珠海大剧院',
    lat: 22.2751,
    lng: 113.5751,
    height: 90,
    type: 'cultural',
    description: '扇贝造型的标志性建筑'
  },
  {
    name: '珠海渔女雕像',
    lat: 22.2500,
    lng: 113.5900,
    height: 15,
    type: 'monument',
    description: '珠海市标志性雕像'
  },
  {
    name: '珠海机场',
    lat: 22.0064,
    lng: 113.3761,
    height: 25,
    type: 'airport',
    description: '珠海金湾机场'
  },
  {
    name: '港珠澳大桥珠海口岸',
    lat: 22.2100,
    lng: 113.5400,
    height: 30,
    type: 'bridge',
    description: '港珠澳大桥珠海连接点'
  },
  {
    name: '拱北口岸',
    lat: 22.2200,
    lng: 113.5500,
    height: 20,
    type: 'border',
    description: '连接澳门的重要口岸'
  },
  {
    name: '珠海国际会展中心',
    lat: 22.2600,
    lng: 113.5800,
    height: 40,
    type: 'exhibition',
    description: '大型会展中心'
  },
  {
    name: '华发商都',
    lat: 22.2700,
    lng: 113.5700,
    height: 120,
    type: 'commercial',
    description: '大型购物中心'
  },
  {
    name: '珠海中心大厦',
    lat: 22.2750,
    lng: 113.5750,
    height: 180,
    type: 'office',
    description: '珠海最高建筑之一'
  }
];

// 珠海市主要道路网络
export const ZHUHAI_ROADS = [
  {
    name: '情侣路',
    type: 'coastal',
    width: 20,
    points: [
      { lat: 22.2200, lng: 113.5300 },
      { lat: 22.2400, lng: 113.5500 },
      { lat: 22.2600, lng: 113.5700 },
      { lat: 22.2800, lng: 113.5900 }
    ]
  },
  {
    name: '珠海大道',
    type: 'main',
    width: 18,
    points: [
      { lat: 22.2000, lng: 113.3000 },
      { lat: 22.2200, lng: 113.4000 },
      { lat: 22.2400, lng: 113.5000 },
      { lat: 22.2600, lng: 113.6000 }
    ]
  },
  {
    name: '迎宾大道',
    type: 'main',
    width: 16,
    points: [
      { lat: 22.0064, lng: 113.3761 },
      { lat: 22.1000, lng: 113.4500 },
      { lat: 22.2000, lng: 113.5200 }
    ]
  },
  {
    name: '港珠澳大桥连接线',
    type: 'highway',
    width: 24,
    points: [
      { lat: 22.2100, lng: 113.5400 },
      { lat: 22.2200, lng: 113.5500 },
      { lat: 22.2300, lng: 113.5600 }
    ]
  }
];

// 珠海海岸线数据
export const ZHUHAI_COASTLINE = [
  { lat: 22.1800, lng: 113.5200 },
  { lat: 22.2000, lng: 113.5400 },
  { lat: 22.2200, lng: 113.5600 },
  { lat: 22.2400, lng: 113.5800 },
  { lat: 22.2600, lng: 113.6000 },
  { lat: 22.2800, lng: 113.6200 },
  { lat: 22.3000, lng: 113.6100 },
  { lat: 22.3200, lng: 113.5900 },
  { lat: 22.3100, lng: 113.5700 },
  { lat: 22.2900, lng: 113.5500 },
  { lat: 22.2700, lng: 113.5300 },
  { lat: 22.2500, lng: 113.5100 },
  { lat: 22.2300, lng: 113.4900 },
  { lat: 22.2100, lng: 113.5000 },
  { lat: 22.1900, lng: 113.5100 }
];

// 珠海主要岛屿
export const ZHUHAI_ISLANDS = [
  {
    name: '横琴岛',
    center: { lat: 22.1300, lng: 113.5200 },
    area: 106.46, // 平方公里
    type: 'development'
  },
  {
    name: '淇澳岛',
    center: { lat: 22.4200, lng: 113.6500 },
    area: 23.8,
    type: 'ecological'
  },
  {
    name: '万山岛',
    center: { lat: 21.9100, lng: 113.7400 },
    area: 8.1,
    type: 'tourism'
  },
  {
    name: '外伶仃岛',
    center: { lat: 22.1100, lng: 113.7400 },
    area: 4.23,
    type: 'tourism'
  }
];

// 建筑高度分布数据
export const BUILDING_HEIGHT_ZONES = [
  {
    name: '香洲CBD',
    center: { lat: 22.2711, lng: 113.5767 },
    radius: 2000, // 米
    avgHeight: 80,
    maxHeight: 200,
    density: 0.8
  },
  {
    name: '拱北商圈',
    center: { lat: 22.2200, lng: 113.5500 },
    radius: 1500,
    avgHeight: 60,
    maxHeight: 150,
    density: 0.7
  },
  {
    name: '横琴金融区',
    center: { lat: 22.1300, lng: 113.5200 },
    radius: 3000,
    avgHeight: 100,
    maxHeight: 250,
    density: 0.6
  },
  {
    name: '金湾工业区',
    center: { lat: 22.1300, lng: 113.3600 },
    radius: 4000,
    avgHeight: 25,
    maxHeight: 80,
    density: 0.4
  }
];
