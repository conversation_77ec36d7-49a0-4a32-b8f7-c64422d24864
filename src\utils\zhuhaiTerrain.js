/**
 * 珠海市地形数据生成器
 * 基于珠海市真实地理数据生成三维地形
 */

// 珠海市地理边界 (WGS84坐标系)
export const ZHUHAI_BOUNDS = {
  north: 22.5608,   // 北纬
  south: 21.9484,   // 南纬  
  east: 113.7759,   // 东经
  west: 113.0353    // 西经
};

// 珠海市主要地标坐标
export const ZHUHAI_LANDMARKS = {
  // 主要城区
  xiangzhou: { lat: 22.2769, lng: 113.5678, name: '香洲区', elevation: 5 },
  jinwan: { lat: 22.1234, lng: 113.3456, name: '金湾区', elevation: 8 },
  doumen: { lat: 22.2089, lng: 113.1456, name: '斗门区', elevation: 12 },
  
  // 重要地标
  zhuhai_airport: { lat: 22.0064, lng: 113.3761, name: '珠海机场', elevation: 3 },
  gongbei: { lat: 22.2242, lng: 113.5397, name: '拱北口岸', elevation: 2 },
  hengqin: { lat: 22.1333, lng: 113.5333, name: '横琴新区', elevation: 5 },
  zhuhai_bridge: { lat: 22.2133, lng: 113.5822, name: '港珠澳大桥', elevation: 0 },
  
  // 山峰
  fenghuang_mountain: { lat: 22.3167, lng: 113.4167, name: '凤凰山', elevation: 437 },
  huangyang_mountain: { lat: 22.4167, lng: 113.3833, name: '黄杨山', elevation: 583 },
  
  // 海岛
  wailingding: { lat: 22.1167, lng: 113.7333, name: '外伶仃岛', elevation: 311 },
  dong_ao: { lat: 22.1833, lng: 113.7500, name: '东澳岛', elevation: 401 }
};

// 珠海市海岸线数据（简化）
export const ZHUHAI_COASTLINE = [
  { lat: 22.2769, lng: 113.5678 }, // 香洲
  { lat: 22.2500, lng: 113.6000 }, 
  { lat: 22.2000, lng: 113.6200 },
  { lat: 22.1500, lng: 113.6000 },
  { lat: 22.1000, lng: 113.5500 },
  { lat: 22.0800, lng: 113.5000 },
  { lat: 22.1000, lng: 113.4500 },
  { lat: 22.1500, lng: 113.4000 },
  { lat: 22.2000, lng: 113.3800 },
  { lat: 22.2500, lng: 113.4000 },
  { lat: 22.2769, lng: 113.4500 }
];

/**
 * 将经纬度坐标转换为Three.js世界坐标
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @param {object} bounds - 地理边界
 * @param {number} scale - 缩放比例
 * @returns {object} Three.js坐标 {x, z}
 */
export function latLngToWorldCoords(lat, lng, bounds = ZHUHAI_BOUNDS, scale = 10000) {
  const latRange = bounds.north - bounds.south;
  const lngRange = bounds.east - bounds.west;
  
  const x = ((lng - bounds.west) / lngRange - 0.5) * scale;
  const z = ((bounds.north - lat) / latRange - 0.5) * scale;
  
  return { x, z };
}

/**
 * 将Three.js世界坐标转换为经纬度
 * @param {number} x - X坐标
 * @param {number} z - Z坐标
 * @param {object} bounds - 地理边界
 * @param {number} scale - 缩放比例
 * @returns {object} 经纬度坐标 {lat, lng}
 */
export function worldCoordsToLatLng(x, z, bounds = ZHUHAI_BOUNDS, scale = 10000) {
  const latRange = bounds.north - bounds.south;
  const lngRange = bounds.east - bounds.west;
  
  const lng = bounds.west + (x / scale + 0.5) * lngRange;
  const lat = bounds.north - (z / scale + 0.5) * latRange;
  
  return { lat, lng };
}

/**
 * 生成珠海市地形高度数据
 * @param {number} width - 网格宽度
 * @param {number} height - 网格高度
 * @param {object} options - 生成选项
 * @returns {Array} 高度数据数组
 */
export function generateZhuhaiTerrain(width = 200, height = 200, options = {}) {
  const {
    scale = 10000,           // 地图缩放比例
    maxElevation = 600,      // 最大海拔（米）
    seaLevel = 0             // 海平面高度
  } = options;

  const heightData = [];
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      // 将网格坐标转换为世界坐标
      const worldX = (x - width / 2) * (scale / width);
      const worldZ = (y - height / 2) * (scale / height);
      
      // 转换为经纬度
      const coords = worldCoordsToLatLng(worldX, worldZ, ZHUHAI_BOUNDS, scale);
      
      // 计算该点的海拔高度
      let elevation = calculateElevationAtPoint(coords.lat, coords.lng);
      
      // 添加海岸线效果
      elevation = applyCoastlineEffect(coords.lat, coords.lng, elevation);
      
      // 添加山峰效果
      elevation = applyMountainEffect(coords.lat, coords.lng, elevation);
      
      // 确保海拔在合理范围内
      elevation = Math.max(seaLevel, Math.min(maxElevation, elevation));
      
      heightData.push(elevation / maxElevation); // 归一化到0-1
    }
  }
  
  return heightData;
}

/**
 * 计算指定经纬度点的基础海拔
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @returns {number} 海拔高度（米）
 */
function calculateElevationAtPoint(lat, lng) {
  // 基础海拔（大部分珠海地区为平原）
  let elevation = 5;
  
  // 使用噪声函数模拟地形起伏
  const noiseScale = 0.01;
  const noise1 = Math.sin(lat * noiseScale * 100) * Math.cos(lng * noiseScale * 100);
  const noise2 = Math.sin(lat * noiseScale * 200) * Math.cos(lng * noiseScale * 200) * 0.5;
  const noise3 = Math.sin(lat * noiseScale * 400) * Math.cos(lng * noiseScale * 400) * 0.25;
  
  elevation += (noise1 + noise2 + noise3) * 20;
  
  return Math.max(0, elevation);
}

/**
 * 应用海岸线效果
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @param {number} baseElevation - 基础海拔
 * @returns {number} 调整后的海拔
 */
function applyCoastlineEffect(lat, lng, baseElevation) {
  // 计算到最近海岸线的距离
  let minDistance = Infinity;
  
  ZHUHAI_COASTLINE.forEach(point => {
    const distance = Math.sqrt(
      Math.pow((lat - point.lat) * 111000, 2) + 
      Math.pow((lng - point.lng) * 111000 * Math.cos(lat * Math.PI / 180), 2)
    );
    minDistance = Math.min(minDistance, distance);
  });
  
  // 距离海岸线越近，海拔越低
  const coastalEffect = Math.max(0, 1 - minDistance / 5000); // 5km范围内受影响
  const elevation = baseElevation * (1 - coastalEffect * 0.8);
  
  // 海面区域
  if (minDistance < 500) { // 500米内为海面
    return -2; // 海面以下
  }
  
  return elevation;
}

/**
 * 应用山峰效果
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @param {number} baseElevation - 基础海拔
 * @returns {number} 调整后的海拔
 */
function applyMountainEffect(lat, lng, baseElevation) {
  let elevation = baseElevation;
  
  // 遍历所有山峰，计算影响
  Object.values(ZHUHAI_LANDMARKS).forEach(landmark => {
    if (landmark.elevation > 100) { // 只考虑较高的山峰
      const distance = Math.sqrt(
        Math.pow((lat - landmark.lat) * 111000, 2) + 
        Math.pow((lng - landmark.lng) * 111000 * Math.cos(lat * Math.PI / 180), 2)
      );
      
      // 山峰影响范围（根据高度调整）
      const influenceRadius = landmark.elevation * 10; // 米
      
      if (distance < influenceRadius) {
        const influence = 1 - (distance / influenceRadius);
        const mountainHeight = landmark.elevation * influence * influence; // 平方衰减
        elevation = Math.max(elevation, baseElevation + mountainHeight);
      }
    }
  });
  
  return elevation;
}

/**
 * 生成珠海市地标标记
 * @param {number} scale - 缩放比例
 * @returns {Array} 地标数据数组
 */
export function generateZhuhaiLandmarks(scale = 10000) {
  const landmarks = [];
  
  Object.entries(ZHUHAI_LANDMARKS).forEach(([key, landmark]) => {
    const worldCoords = latLngToWorldCoords(landmark.lat, landmark.lng, ZHUHAI_BOUNDS, scale);
    
    landmarks.push({
      id: key,
      name: landmark.name,
      x: worldCoords.x,
      z: worldCoords.z,
      y: landmark.elevation,
      lat: landmark.lat,
      lng: landmark.lng,
      type: getLandmarkType(key),
      elevation: landmark.elevation
    });
  });
  
  return landmarks;
}

/**
 * 获取地标类型
 * @param {string} landmarkKey - 地标键名
 * @returns {string} 地标类型
 */
function getLandmarkType(landmarkKey) {
  if (landmarkKey.includes('mountain')) return 'mountain';
  if (landmarkKey.includes('airport')) return 'airport';
  if (landmarkKey.includes('bridge')) return 'bridge';
  if (landmarkKey.includes('island')) return 'island';
  if (landmarkKey.includes('gongbei')) return 'port';
  return 'city';
}

/**
 * 生成珠海市海岸线几何体
 * @param {number} scale - 缩放比例
 * @returns {Array} 海岸线点数组
 */
export function generateZhuhaiCoastline(scale = 10000) {
  const coastlinePoints = [];
  
  ZHUHAI_COASTLINE.forEach(point => {
    const worldCoords = latLngToWorldCoords(point.lat, point.lng, ZHUHAI_BOUNDS, scale);
    coastlinePoints.push({
      x: worldCoords.x,
      z: worldCoords.z,
      y: 1, // 略高于海平面
      lat: point.lat,
      lng: point.lng
    });
  });
  
  return coastlinePoints;
}

/**
 * 检查坐标是否在珠海市范围内
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @returns {boolean} 是否在范围内
 */
export function isInZhuhaiArea(lat, lng) {
  return lat >= ZHUHAI_BOUNDS.south && 
         lat <= ZHUHAI_BOUNDS.north && 
         lng >= ZHUHAI_BOUNDS.west && 
         lng <= ZHUHAI_BOUNDS.east;
}

/**
 * 获取珠海市区域信息
 * @param {number} lat - 纬度
 * @param {number} lng - 经度
 * @returns {string} 区域名称
 */
export function getZhuhaiDistrict(lat, lng) {
  // 简化的区域判断
  if (lat > 22.25 && lng > 113.5) return '香洲区';
  if (lat < 22.15 && lng < 113.4) return '金湾区';
  if (lat > 22.18 && lng < 113.2) return '斗门区';
  if (lat < 22.15 && lng > 113.5) return '横琴新区';
  
  return '珠海市';
}

/**
 * 计算两个经纬度点之间的距离（米）
 * @param {number} lat1 - 点1纬度
 * @param {number} lng1 - 点1经度
 * @param {number} lat2 - 点2纬度
 * @param {number} lng2 - 点2经度
 * @returns {number} 距离（米）
 */
export function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
