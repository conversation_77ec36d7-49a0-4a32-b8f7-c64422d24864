<template>
  <div class="reliable-cesium-system">
    <!-- Cesium地图容器 -->
    <div ref="cesiumContainer" class="cesium-container"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市3D气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatus.class">{{ systemStatus.text }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Cesium版本:</span>
          <span class="status-value" :class="cesiumVersion ? 'online' : 'error'">{{ cesiumVersion || '未加载' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">错误信息:</span>
          <span class="status-value error" v-if="errorMessage">{{ errorMessage }}</span>
          <span class="status-value online" v-else>无错误</span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 3)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="flyToWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate" :disabled="!isSystemReady">
          ➕ 生成新预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="flyToOverview" class="control-btn overview" :disabled="!isSystemReady">
          🌍 总览视图
        </button>
        <button @click="retryInitialization" class="control-btn retry" v-if="systemStatus.class === 'error'">
          🔄 重试初始化
        </button>
      </div>

      <!-- 调试信息 -->
      <div class="debug-section" v-if="showDebug">
        <h4>🔧 调试信息</h4>
        <div class="debug-info">
          <div>WebGL支持: {{ webglSupported ? '✅' : '❌' }}</div>
          <div>Cesium模块: {{ cesiumLoaded ? '✅' : '❌' }}</div>
          <div>Viewer创建: {{ viewerCreated ? '✅' : '❌' }}</div>
          <div>实体数量: {{ entityCount }}</div>
        </div>
        <button @click="toggleDebugMode" class="control-btn debug">
          {{ showDebug ? '隐藏调试' : '显示调试' }}
        </button>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="flyToArea(area)"
          :disabled="!isSystemReady"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 动态导入Cesium
let Cesium = null;
let viewer = null;

// 响应式数据
const cesiumContainer = ref(null);
const activeWarnings = ref([]);
const notification = ref(null);
const cesiumVersion = ref('');
const errorMessage = ref('');

// 调试相关
const showDebug = ref(true);
const webglSupported = ref(false);
const cesiumLoaded = ref(false);
const viewerCreated = ref(false);
const entityCount = ref(0);

// 系统状态
const systemStatus = ref({ text: '准备初始化...', class: 'loading' });
const isSystemReady = computed(() => systemStatus.value.class === 'online');

// 系统变量
let weatherEntities = [];
let warningIdCounter = 0;

// 珠海市地理坐标
const ZHUHAI_BOUNDS = {
  center: {
    longitude: 113.5767,
    latitude: 22.2711,
    height: 15000
  }
};

// 珠海市重要区域
const zhuhaiAreas = ref([
  {
    name: '香洲区',
    icon: '🏢',
    longitude: 113.5767,
    latitude: 22.2711,
    description: '珠海市中心区域'
  },
  {
    name: '拱北口岸',
    icon: '🚪',
    longitude: 113.5500,
    latitude: 22.2200,
    description: '连接澳门的重要口岸'
  },
  {
    name: '横琴新区',
    icon: '🏗️',
    longitude: 113.5200,
    latitude: 22.1300,
    description: '珠海经济特区'
  },
  {
    name: '金湾区',
    icon: '✈️',
    longitude: 113.3761,
    latitude: 22.0064,
    description: '珠海机场所在地'
  },
  {
    name: '斗门区',
    icon: '🌾',
    longitude: 113.2500,
    latitude: 22.2000,
    description: '珠海农业区'
  }
]);

onMounted(async () => {
  console.log('🚀 开始初始化可靠版CesiumJS系统...');
  await initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 检查WebGL支持
function checkWebGLSupport() {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    webglSupported.value = !!gl;
    console.log('WebGL支持检查:', webglSupported.value);
    return webglSupported.value;
  } catch (e) {
    console.error('WebGL检查失败:', e);
    webglSupported.value = false;
    return false;
  }
}

// 初始化系统
async function initializeSystem() {
  try {
    errorMessage.value = '';
    
    // 步骤1: 检查WebGL支持
    systemStatus.value = { text: '检查WebGL支持...', class: 'loading' };
    if (!checkWebGLSupport()) {
      throw new Error('您的浏览器不支持WebGL，无法运行3D地球');
    }
    
    // 步骤2: 加载Cesium模块
    systemStatus.value = { text: '加载Cesium模块...', class: 'loading' };
    await loadCesiumModule();
    
    // 步骤3: 初始化Cesium Viewer
    systemStatus.value = { text: '初始化3D地球...', class: 'loading' };
    await initializeCesiumViewer();
    
    // 步骤4: 创建基础模型
    systemStatus.value = { text: '创建珠海模型...', class: 'loading' };
    await createBasicModel();
    
    // 系统就绪
    systemStatus.value = { text: '🟢 系统就绪', class: 'online' };
    
    console.log('✅ 可靠版CesiumJS系统初始化完成');
    showNotification('✅ 3D地球系统初始化成功', 'success');
    
    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 2000);
    
  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    errorMessage.value = error.message;
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    showNotification(`❌ 初始化失败: ${error.message}`, 'error');
  }
}

// 加载Cesium模块
async function loadCesiumModule() {
  try {
    console.log('开始导入Cesium模块...');
    
    // 使用动态导入
    const cesiumModule = await import('cesium');
    Cesium = cesiumModule;
    cesiumVersion.value = Cesium.VERSION;
    cesiumLoaded.value = true;
    
    console.log('Cesium模块导入成功，版本:', Cesium.VERSION);
    
    // 设置基本配置
    if (Cesium.Ion) {
      // 使用默认设置，不设置访问令牌
      console.log('Cesium Ion 可用，使用默认配置');
    }
    
    console.log('✅ Cesium模块加载成功');
  } catch (error) {
    cesiumLoaded.value = false;
    console.error('Cesium模块加载详细错误:', error);
    throw new Error(`Cesium模块加载失败: ${error.message}`);
  }
}

// 初始化Cesium Viewer
async function initializeCesiumViewer() {
  try {
    console.log('开始创建Cesium Viewer...');
    
    // 确保容器存在
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到');
    }
    
    // 创建最简单的Cesium Viewer配置
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 禁用所有可能导致网络请求的功能
      baseLayerPicker: false,
      geocoder: false,
      homeButton: false,
      sceneModePicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      vrButton: false,
      
      // 使用最基本的地形和影像
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),
      imageryProvider: false, // 不使用任何影像
      
      // 性能优化
      requestRenderMode: false,
      maximumRenderTimeChange: Infinity
    });
    
    viewerCreated.value = true;
    
    // 设置地球背景色
    viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#2563eb');
    viewer.scene.backgroundColor = Cesium.Color.fromCssColorString('#1e40af');
    
    // 设置相机初始位置到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        ZHUHAI_BOUNDS.center.longitude,
        ZHUHAI_BOUNDS.center.latitude,
        ZHUHAI_BOUNDS.center.height
      ),
      orientation: {
        heading: 0.0,
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });
    
    // 禁用一些可能导致问题的功能
    viewer.scene.globe.enableLighting = false;
    viewer.scene.skyAtmosphere.show = false;
    viewer.scene.fog.enabled = false;
    
    console.log('✅ Cesium Viewer 创建成功');
  } catch (error) {
    viewerCreated.value = false;
    console.error('Cesium Viewer 创建失败:', error);
    throw new Error(`3D地球初始化失败: ${error.message}`);
  }
}

// 创建基础模型
async function createBasicModel() {
  try {
    console.log('创建基础珠海模型...');

    // 创建珠海陆地
    createZhuhaiLand();

    // 创建基础建筑
    createBasicBuildings();

    // 创建区域标记
    createAreaMarkers();

    // 更新实体计数
    updateEntityCount();

    console.log('✅ 基础模型创建完成');
  } catch (error) {
    console.error('基础模型创建失败:', error);
    throw new Error(`模型创建失败: ${error.message}`);
  }
}

// 创建珠海陆地
function createZhuhaiLand() {
  // 珠海市简化轮廓
  const zhuhaiOutline = [
    113.2, 22.0,
    113.7, 22.0,
    113.7, 22.4,
    113.2, 22.4
  ];

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(zhuhaiOutline),
      material: Cesium.Color.fromCssColorString('#16a34a').withAlpha(0.8),
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString('#fbbf24'),
      outlineWidth: 2,
      height: 0,
      extrudedHeight: 10
    }
  });
}

// 创建基础建筑
function createBasicBuildings() {
  const buildings = [
    { lng: 113.5767, lat: 22.2711, height: 150, name: '香洲中心', color: '#4a5568' },
    { lng: 113.5200, lat: 22.1300, height: 200, name: '横琴中心', color: '#2d3748' },
    { lng: 113.3761, lat: 22.0064, height: 60, name: '金湾机场', color: '#7c3aed' },
    { lng: 113.2500, lat: 22.2000, height: 40, name: '斗门中心', color: '#166534' },
    { lng: 113.5500, lat: 22.2200, height: 80, name: '拱北口岸', color: '#dc2626' }
  ];

  buildings.forEach(building => {
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat),
      box: {
        dimensions: new Cesium.Cartesian3(50, 50, building.height),
        material: Cesium.Color.fromCssColorString(building.color).withAlpha(0.8),
        outline: true,
        outlineColor: Cesium.Color.WHITE.withAlpha(0.5)
      },
      label: {
        text: building.name,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -building.height/2 - 30),
        scale: 0.7
      }
    });
  });
}

// 创建区域标记
function createAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(area.longitude, area.latitude),
      point: {
        pixelSize: 12,
        color: Cesium.Color.CYAN,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: `${area.icon} ${area.name}`,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -25),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        scale: 0.8
      }
    });
  });
}

// 更新实体计数
function updateEntityCount() {
  entityCount.value = viewer.entities.values.length;
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 2;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 1000);
  }
}

// 生成新预警
function generateNewWarning() {
  if (!isSystemReady.value) return;

  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetLng = (Math.random() - 0.5) * 0.02;
  const offsetLat = (Math.random() - 0.5) * 0.02;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    longitude: selectedArea.longitude + offsetLng,
    latitude: selectedArea.latitude + offsetLat,
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  createWeatherEntity(warning);
  updateEntityCount();

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 8) {
    const oldWarning = activeWarnings.value.shift();
    removeWeatherEntity(oldWarning.id);
  }
}

// 创建预警实体
function createWeatherEntity(warning) {
  if (!viewer) return;

  const color = getWarningColor(warning.level);
  const height = warning.intensity * 200 + 300;

  const entity = viewer.entities.add({
    id: `warning-${warning.id}`,
    position: Cesium.Cartesian3.fromDegrees(warning.longitude, warning.latitude),
    cylinder: {
      length: height,
      topRadius: 300,
      bottomRadius: 500,
      material: color.withAlpha(0.6),
      outline: true,
      outlineColor: color,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    label: {
      text: `${getWarningIcon(warning.type)} ${warning.type}预警\n${getWarningLevelText(warning.level)}`,
      font: '12pt Microsoft YaHei',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -height/2 - 30),
      scale: 0.8
    }
  });

  weatherEntities.push(entity);
}

// 移除预警实体
function removeWeatherEntity(warningId) {
  if (!viewer) return;

  const entityId = `warning-${warningId}`;
  const entity = viewer.entities.getById(entityId);
  if (entity) {
    viewer.entities.remove(entity);
  }

  weatherEntities = weatherEntities.filter(e => e.id !== entityId);
  updateEntityCount();
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];

  weatherEntities.forEach(entity => {
    if (viewer) {
      viewer.entities.remove(entity);
    }
  });
  weatherEntities = [];
  updateEntityCount();

  showNotification('🗑️ 已清除所有预警', 'info');
}

// 飞行到预警位置
function flyToWarning(warning) {
  if (!viewer || !isSystemReady.value) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      warning.longitude,
      warning.latitude,
      3000
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-30),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行到 ${warning.location} ${warning.type}预警`, 'info');
}

// 飞行到区域
function flyToArea(area) {
  if (!viewer || !isSystemReady.value) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      area.longitude,
      area.latitude,
      5000
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 2.0
  });

  showNotification(`📍 飞行到 ${area.name}`, 'info');
}

// 飞行到总览
function flyToOverview() {
  if (!viewer || !isSystemReady.value) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      ZHUHAI_BOUNDS.center.longitude,
      ZHUHAI_BOUNDS.center.latitude,
      ZHUHAI_BOUNDS.center.height
    ),
    orientation: {
      heading: 0.0,
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3.0
  });

  showNotification('🌍 返回总览视图', 'info');
}

// 重试初始化
async function retryInitialization() {
  console.log('🔄 重试系统初始化...');

  // 清理现有资源
  cleanup();

  // 重置状态
  cesiumLoaded.value = false;
  viewerCreated.value = false;
  entityCount.value = 0;
  errorMessage.value = '';

  // 重新初始化
  await initializeSystem();
}

// 切换调试模式
function toggleDebugMode() {
  showDebug.value = !showDebug.value;
}

// 工具函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊'
  };
  return icons[type] || '⚠️';
}

function getWarningColor(level) {
  const colors = {
    'red': Cesium.Color.RED,
    'orange': Cesium.Color.ORANGE,
    'yellow': Cesium.Color.YELLOW,
    'blue': Cesium.Color.BLUE
  };
  return colors[level] || Cesium.Color.WHITE;
}

function getWarningLevelText(level) {
  const texts = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 清理函数
function cleanup() {
  console.log('🧹 清理系统资源...');

  if (viewer) {
    try {
      viewer.destroy();
    } catch (e) {
      console.warn('Viewer销毁时出现警告:', e);
    }
    viewer = null;
  }

  weatherEntities = [];
  viewerCreated.value = false;
  entityCount.value = 0;

  console.log('✅ 系统资源清理完成');
}
</script>

<style scoped>
.reliable-cesium-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.loading {
  color: #FFA500;
}

.status-value.online {
  color: #90EE90;
}

.status-value.error {
  color: #FF6B6B;
}

.debug-section {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.debug-info {
  font-size: 10px;
  margin-bottom: 10px;
}

.debug-info div {
  margin-bottom: 3px;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.level-red { 
  border-left: 4px solid #ff0000; 
  background: rgba(255, 0, 0, 0.1);
}
.level-orange { 
  border-left: 4px solid #ff8800; 
  background: rgba(255, 136, 0, 0.1);
}
.level-yellow { 
  border-left: 4px solid #ffff00; 
  background: rgba(255, 255, 0, 0.1);
}
.level-blue { 
  border-left: 4px solid #0088ff; 
  background: rgba(0, 136, 255, 0.1);
}

.warning-icon {
  font-size: 20px;
  min-width: 25px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 9px;
  color: #999;
}

.warning-control {
  margin-bottom: 20px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.overview {
  background: #2196F3;
  color: white;
}

.control-btn.overview:hover:not(:disabled) {
  background: #1976D2;
}

.control-btn.retry {
  background: #FF9800;
  color: white;
}

.control-btn.retry:hover:not(:disabled) {
  background: #F57C00;
}

.control-btn.debug {
  background: #9C27B0;
  color: white;
}

.control-btn.debug:hover:not(:disabled) {
  background: #7B1FA2;
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  max-width: 250px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 250px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 16px;
  min-width: 20px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 11px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 9px;
  color: #ccc;
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
