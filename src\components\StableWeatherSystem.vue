<template>
  <div class="stable-weather-system">
    <!-- 珠海市地图容器 -->
    <div class="map-container">
      <canvas ref="mapCanvas" class="map-canvas" @click="handleMapClick"></canvas>
      
      <!-- 预警覆盖层 -->
      <div class="warning-overlay">
        <div 
          v-for="warning in activeWarnings" 
          :key="warning.id"
          class="warning-circle"
          :class="`level-${warning.level}`"
          :style="getWarningStyle(warning)"
          @click="selectWarning(warning)"
        >
          <div class="warning-icon">{{ getWarningIcon(warning.type) }}</div>
          <div class="warning-label">{{ warning.type }}</div>
        </div>
      </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>🌍 珠海市低空三维空域动态气象预警系统</h3>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value online">🟢 系统就绪</span>
        </div>
        <div class="status-item">
          <span class="status-label">地图状态:</span>
          <span class="status-value online">🟢 地图就绪</span>
        </div>
      </div>
      
      <!-- 实时预警 -->
      <div class="warning-section">
        <h4>🚨 实时预警 ({{ activeWarnings.length }})</h4>
        <div v-if="activeWarnings.length === 0" class="no-warnings">
          ✅ 当前无预警信息
        </div>
        <div v-else class="warnings-list">
          <div 
            v-for="warning in activeWarnings.slice(0, 5)" 
            :key="warning.id"
            class="warning-item"
            :class="`level-${warning.level}`"
            @click="focusWarning(warning)"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <div class="warning-content">
              <div class="warning-title">{{ warning.type }}预警</div>
              <div class="warning-level">{{ getWarningLevelText(warning.level) }}</div>
              <div class="warning-location">{{ warning.location }}</div>
              <div class="warning-time">{{ warning.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预警统计 -->
      <div class="warning-stats">
        <h4>📊 预警统计</h4>
        <div class="stats-grid">
          <div class="stat-item red">
            <span class="stat-number">{{ getWarningCountByLevel('red') }}</span>
            <span class="stat-label">红色</span>
          </div>
          <div class="stat-item orange">
            <span class="stat-number">{{ getWarningCountByLevel('orange') }}</span>
            <span class="stat-label">橙色</span>
          </div>
          <div class="stat-item yellow">
            <span class="stat-number">{{ getWarningCountByLevel('yellow') }}</span>
            <span class="stat-label">黄色</span>
          </div>
          <div class="stat-item blue">
            <span class="stat-number">{{ getWarningCountByLevel('blue') }}</span>
            <span class="stat-label">蓝色</span>
          </div>
        </div>
      </div>

      <!-- 预警控制 -->
      <div class="warning-control">
        <h4>⚙️ 预警控制</h4>
        <button @click="generateNewWarning" class="control-btn generate">
          ➕ 生成新预警
        </button>
        <button @click="generateSevereWarning" class="control-btn severe">
          🚨 生成严重预警
        </button>
        <button @click="clearAllWarnings" class="control-btn clear" :disabled="activeWarnings.length === 0">
          🗑️ 清除所有预警
        </button>
        <button @click="toggleAutoUpdate" class="control-btn" :class="{ active: autoUpdate }">
          {{ autoUpdate ? '⏸️ 暂停自动更新' : '▶️ 开启自动更新' }}
        </button>
        <button @click="exportWarningData" class="control-btn export" :disabled="activeWarnings.length === 0">
          📊 导出预警数据
        </button>
      </div>
    </div>

    <!-- 珠海区域面板 -->
    <div class="zhuhai-info-panel">
      <h4>📍 珠海市区域</h4>
      <div class="area-list">
        <div 
          v-for="area in zhuhaiAreas" 
          :key="area.name"
          class="area-item"
          @click="focusArea(area)"
        >
          <span class="area-icon">{{ area.icon }}</span>
          <div class="area-info">
            <div class="area-name">{{ area.name }}</div>
            <div class="area-warnings">预警: {{ getAreaWarningCount(area) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions-panel">
      <h4>⚡ 快速操作</h4>
      <div class="quick-actions-grid">
        <button @click="resetView" class="quick-btn overview">
          🌍 总览
        </button>
        <button @click="focusOnSevereWarnings" class="quick-btn severe" :disabled="!hasSevereWarnings">
          🚨 严重预警
        </button>
        <button @click="toggleLabels" class="quick-btn layers">
          👁️ {{ showLabels ? '隐藏标签' : '显示标签' }}
        </button>
        <button @click="generateRandomWarnings" class="quick-btn reset">
          🎲 随机预警
        </button>
      </div>

      <!-- 地图控制 -->
      <div class="map-controls">
        <h5>🗺️ 地图控制</h5>
        <div class="map-control-grid">
          <button @click="toggleBuildings" class="map-btn" :class="{ active: showBuildings }">
            🏢 建筑
          </button>
          <button @click="toggleRoads" class="map-btn" :class="{ active: showRoads }">
            🛣️ 道路
          </button>
          <button @click="toggleLandmarks" class="map-btn" :class="{ active: showLandmarks }">
            🏛️ 地标
          </button>
          <button @click="toggleAirspace" class="map-btn" :class="{ active: showAirspace }">
            ✈️ 空域
          </button>
        </div>
      </div>
    </div>

    <!-- 系统状态面板 -->
    <div class="status-panel">
      <div class="status-item">
        <span class="status-label">更新时间:</span>
        <span class="status-value">{{ lastUpdateTime }}</span>
      </div>
      <div class="status-item">
        <span class="status-label">活跃预警:</span>
        <span class="status-value">{{ activeWarnings.length }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">严重预警:</span>
        <span class="status-value severe-count">{{ severeWarningsCount }} 个</span>
      </div>
      <div class="status-item">
        <span class="status-label">覆盖区域:</span>
        <span class="status-value">珠海市全域</span>
      </div>
    </div>

    <!-- 地图图例 -->
    <div class="map-legend">
      <h4>🗺️ 地图图例</h4>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color" style="background: #16a34a;"></div>
          <span>陆地区域</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #1e40af;"></div>
          <span>海洋区域</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #fbbf24;"></div>
          <span>海岸线</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #f1f5f9;"></div>
          <span>主要道路</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: rgba(6, 182, 212, 0.5); border: 2px dashed #06b6d4;"></div>
          <span>空域管制</span>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background: #06b6d4;"></div>
          <span>重要区域</span>
        </div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 响应式数据
const mapCanvas = ref(null);
const activeWarnings = ref([]);
const lastUpdateTime = ref('');
const autoUpdate = ref(false);
const showLabels = ref(true);
const notification = ref(null);

// 地图显示控制
const showBuildings = ref(true);
const showRoads = ref(true);
const showLandmarks = ref(true);
const showAirspace = ref(true);

// 地图相关
let ctx = null;
let updateInterval = null;
let warningIdCounter = 0;

// 计算属性
const severeWarningsCount = computed(() => 
  activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange').length
);

const hasSevereWarnings = computed(() => severeWarningsCount.value > 0);

// 珠海市重要区域（像素坐标）
const zhuhaiAreas = ref([
  { name: '香洲区', icon: '🏢', x: 400, y: 300, lng: 113.5767, lat: 22.2711 },
  { name: '拱北口岸', icon: '🚪', x: 350, y: 350, lng: 113.5500, lat: 22.2200 },
  { name: '横琴新区', icon: '🏗️', x: 300, y: 400, lng: 113.5200, lat: 22.1300 },
  { name: '金湾区', icon: '✈️', x: 200, y: 450, lng: 113.3761, lat: 22.0064 },
  { name: '斗门区', icon: '🌾', x: 150, y: 300, lng: 113.2500, lat: 22.2000 },
  { name: '港珠澳大桥', icon: '🌉', x: 380, y: 320, lng: 113.5400, lat: 22.2100 }
]);

onMounted(() => {
  console.log('🚀 启动稳定版珠海气象预警系统...');
  initializeSystem();
});

onUnmounted(() => {
  cleanup();
});

// 初始化系统
function initializeSystem() {
  try {
    // 初始化画布
    initializeCanvas();
    
    // 绘制地图
    drawMap();
    
    // 更新时间
    updateLastUpdateTime();
    
    // 显示成功通知
    showNotification('✅ 系统初始化成功', 'success');
    
    // 生成初始预警
    setTimeout(() => {
      generateInitialWarnings();
    }, 1000);
    
    console.log('✅ 稳定版气象预警系统初始化完成');
  } catch (error) {
    console.error('❌ 系统初始化失败:', error);
    showNotification('❌ 系统初始化失败', 'error');
  }
}

// 初始化画布
function initializeCanvas() {
  const canvas = mapCanvas.value;
  canvas.width = 800;
  canvas.height = 600;
  ctx = canvas.getContext('2d');
  
  console.log('✅ 画布初始化完成');
}

// 绘制地图
function drawMap() {
  if (!ctx) return;

  // 清空画布
  ctx.clearRect(0, 0, 800, 600);

  // 绘制海洋背景
  const oceanGradient = ctx.createRadialGradient(400, 300, 0, 400, 300, 400);
  oceanGradient.addColorStop(0, '#1e40af');
  oceanGradient.addColorStop(0.7, '#1e3a8a');
  oceanGradient.addColorStop(1, '#1a202c');
  ctx.fillStyle = oceanGradient;
  ctx.fillRect(0, 0, 800, 600);

  // 绘制珠海市详细轮廓
  drawZhuhaiMainland();

  // 绘制岛屿
  drawIslands();

  // 绘制建筑群
  if (showBuildings.value) {
    drawBuildingClusters();
  }

  // 绘制道路网络
  if (showRoads.value) {
    drawRoadNetwork();
  }

  // 绘制港口和码头
  drawPortsAndDocks();

  // 绘制空域边界
  if (showAirspace.value) {
    drawAirspaceZones();
  }

  // 绘制地标建筑
  if (showLandmarks.value) {
    drawLandmarks();
  }

  // 绘制区域标记
  drawAreaMarkers();

  console.log('✅ 详细地图绘制完成');
}

// 绘制珠海市主体
function drawZhuhaiMainland() {
  // 香洲区主体
  ctx.fillStyle = '#16a34a';
  ctx.beginPath();
  ctx.moveTo(300, 180);
  ctx.lineTo(550, 180);
  ctx.lineTo(580, 220);
  ctx.lineTo(600, 280);
  ctx.lineTo(580, 350);
  ctx.lineTo(520, 380);
  ctx.lineTo(450, 390);
  ctx.lineTo(350, 380);
  ctx.lineTo(280, 350);
  ctx.lineTo(260, 280);
  ctx.lineTo(280, 220);
  ctx.closePath();
  ctx.fill();

  // 金湾区
  ctx.fillStyle = '#15803d';
  ctx.beginPath();
  ctx.moveTo(120, 380);
  ctx.lineTo(280, 380);
  ctx.lineTo(300, 420);
  ctx.lineTo(280, 480);
  ctx.lineTo(200, 500);
  ctx.lineTo(120, 480);
  ctx.lineTo(100, 440);
  ctx.closePath();
  ctx.fill();

  // 斗门区
  ctx.fillStyle = '#166534';
  ctx.beginPath();
  ctx.moveTo(80, 250);
  ctx.lineTo(260, 250);
  ctx.lineTo(280, 300);
  ctx.lineTo(260, 350);
  ctx.lineTo(180, 370);
  ctx.lineTo(100, 350);
  ctx.lineTo(60, 300);
  ctx.closePath();
  ctx.fill();

  // 绘制海岸线
  ctx.strokeStyle = '#fbbf24';
  ctx.lineWidth = 4;
  ctx.setLineDash([]);

  // 香洲区海岸线
  ctx.beginPath();
  ctx.moveTo(300, 180);
  ctx.lineTo(550, 180);
  ctx.lineTo(580, 220);
  ctx.lineTo(600, 280);
  ctx.lineTo(580, 350);
  ctx.lineTo(520, 380);
  ctx.stroke();

  // 金湾区海岸线
  ctx.beginPath();
  ctx.moveTo(120, 380);
  ctx.lineTo(200, 500);
  ctx.lineTo(280, 480);
  ctx.stroke();
}

// 绘制岛屿
function drawIslands() {
  // 横琴岛
  ctx.fillStyle = '#22c55e';
  ctx.beginPath();
  ctx.ellipse(320, 420, 60, 40, 0, 0, 2 * Math.PI);
  ctx.fill();

  ctx.strokeStyle = '#fbbf24';
  ctx.lineWidth = 2;
  ctx.stroke();

  // 万山群岛（简化）
  const islands = [
    {x: 650, y: 350, w: 25, h: 15},
    {x: 680, y: 380, w: 20, h: 12},
    {x: 700, y: 320, w: 15, h: 10},
    {x: 720, y: 360, w: 18, h: 12}
  ];

  ctx.fillStyle = '#15803d';
  islands.forEach(island => {
    ctx.beginPath();
    ctx.ellipse(island.x, island.y, island.w, island.h, 0, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
  });
}

// 绘制建筑群
function drawBuildingClusters() {
  // 香洲区建筑群
  drawBuildingCluster(420, 280, 15, '#4a5568', '香洲商务区');

  // 拱北建筑群
  drawBuildingCluster(380, 320, 10, '#2d3748', '拱北商圈');

  // 横琴建筑群
  drawBuildingCluster(320, 420, 12, '#1a202c', '横琴新区');

  // 金湾建筑群
  drawBuildingCluster(200, 440, 8, '#2d3748', '金湾中心');

  // 斗门建筑群
  drawBuildingCluster(170, 310, 6, '#4a5568', '斗门区');
}

// 绘制单个建筑群
function drawBuildingCluster(centerX, centerY, count, color, name) {
  ctx.fillStyle = color;

  for (let i = 0; i < count; i++) {
    const angle = (i / count) * 2 * Math.PI;
    const radius = 20 + Math.random() * 30;
    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;
    const width = 3 + Math.random() * 6;
    const height = 8 + Math.random() * 20;

    ctx.fillRect(x - width/2, y - height, width, height);

    // 添加建筑顶部高光
    ctx.fillStyle = '#e2e8f0';
    ctx.fillRect(x - width/2, y - height, width, 2);
    ctx.fillStyle = color;
  }

  // 绘制建筑群标签
  if (showLabels.value) {
    ctx.fillStyle = 'white';
    ctx.font = '10px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText(name, centerX, centerY + 40);
  }
}

// 绘制道路网络
function drawRoadNetwork() {
  ctx.strokeStyle = '#f1f5f9';
  ctx.lineWidth = 3;
  ctx.setLineDash([]);

  // 主要道路
  const majorRoads = [
    // 珠海大道
    [[150, 300], [500, 300]],
    // 情侣路
    [[300, 180], [580, 350]],
    // 港珠澳大桥连接线
    [[380, 320], [450, 280]],
    // 横琴大桥
    [[280, 380], [320, 420]],
    // 金湾大道
    [[120, 440], [280, 440]]
  ];

  majorRoads.forEach(road => {
    ctx.beginPath();
    ctx.moveTo(road[0][0], road[0][1]);
    ctx.lineTo(road[1][0], road[1][1]);
    ctx.stroke();
  });

  // 次要道路
  ctx.strokeStyle = '#cbd5e1';
  ctx.lineWidth = 2;

  const minorRoads = [
    [[350, 250], [450, 250]],
    [[400, 200], [400, 350]],
    [[200, 350], [200, 480]],
    [[150, 280], [250, 280]]
  ];

  minorRoads.forEach(road => {
    ctx.beginPath();
    ctx.moveTo(road[0][0], road[0][1]);
    ctx.lineTo(road[1][0], road[1][1]);
    ctx.stroke();
  });
}

// 绘制港口和码头
function drawPortsAndDocks() {
  // 拱北口岸
  ctx.fillStyle = '#dc2626';
  ctx.fillRect(375, 315, 15, 10);
  ctx.fillStyle = 'white';
  ctx.font = '8px Microsoft YaHei';
  ctx.textAlign = 'center';
  ctx.fillText('口岸', 382, 322);

  // 九洲港
  ctx.fillStyle = '#1d4ed8';
  ctx.fillRect(480, 360, 12, 8);
  ctx.fillStyle = 'white';
  ctx.fillText('港口', 486, 366);

  // 横琴口岸
  ctx.fillStyle = '#dc2626';
  ctx.fillRect(315, 415, 12, 8);
  ctx.fillStyle = 'white';
  ctx.fillText('口岸', 321, 421);

  // 金湾机场
  ctx.fillStyle = '#7c3aed';
  ctx.beginPath();
  ctx.moveTo(190, 435);
  ctx.lineTo(210, 435);
  ctx.lineTo(205, 445);
  ctx.lineTo(195, 445);
  ctx.closePath();
  ctx.fill();

  ctx.fillStyle = 'white';
  ctx.fillText('机场', 200, 442);
}

// 绘制地标建筑
function drawLandmarks() {
  // 珠海大剧院
  ctx.fillStyle = '#8b5cf6';
  ctx.beginPath();
  ctx.ellipse(450, 300, 12, 8, 0, 0, 2 * Math.PI);
  ctx.fill();
  ctx.strokeStyle = 'white';
  ctx.lineWidth = 1;
  ctx.stroke();

  if (showLabels.value) {
    ctx.fillStyle = 'white';
    ctx.font = '8px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText('🎭 大剧院', 450, 290);
  }

  // 珠海渔女
  ctx.fillStyle = '#06b6d4';
  ctx.beginPath();
  ctx.arc(500, 340, 6, 0, 2 * Math.PI);
  ctx.fill();

  if (showLabels.value) {
    ctx.fillStyle = 'white';
    ctx.font = '8px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText('🧜‍♀️ 渔女', 500, 330);
  }

  // 圆明新园
  ctx.fillStyle = '#f59e0b';
  ctx.fillRect(340, 390, 10, 8);

  if (showLabels.value) {
    ctx.fillStyle = 'white';
    ctx.font = '8px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText('🏛️ 圆明新园', 345, 380);
  }

  // 长隆海洋王国
  ctx.fillStyle = '#ec4899';
  ctx.beginPath();
  ctx.moveTo(320, 440);
  ctx.lineTo(330, 435);
  ctx.lineTo(340, 440);
  ctx.lineTo(335, 450);
  ctx.lineTo(325, 450);
  ctx.closePath();
  ctx.fill();

  if (showLabels.value) {
    ctx.fillStyle = 'white';
    ctx.font = '8px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText('🐋 长隆', 330, 430);
  }

  // 珠海中心大厦
  ctx.fillStyle = '#1f2937';
  ctx.fillRect(415, 270, 6, 20);
  ctx.fillStyle = '#374151';
  ctx.fillRect(416, 270, 4, 18);

  if (showLabels.value) {
    ctx.fillStyle = 'white';
    ctx.font = '8px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText('🏢 中心大厦', 418, 260);
  }

  // 情侣路
  ctx.strokeStyle = '#f472b6';
  ctx.lineWidth = 4;
  ctx.setLineDash([10, 5]);
  ctx.beginPath();
  ctx.moveTo(300, 180);
  ctx.quadraticCurveTo(400, 250, 500, 340);
  ctx.stroke();
  ctx.setLineDash([]);

  if (showLabels.value) {
    ctx.fillStyle = '#f472b6';
    ctx.font = 'bold 10px Microsoft YaHei';
    ctx.textAlign = 'center';
    ctx.fillText('💕 情侣路', 400, 270);
  }
}

// 绘制空域
function drawAirspaceZones() {
  // 珠海机场管制区
  ctx.fillStyle = 'rgba(6, 182, 212, 0.2)';
  ctx.fillRect(150, 400, 150, 100);
  ctx.strokeStyle = '#06b6d4';
  ctx.setLineDash([8, 4]);
  ctx.lineWidth = 3;
  ctx.strokeRect(150, 400, 150, 100);

  // 添加空域标签
  ctx.fillStyle = '#06b6d4';
  ctx.font = 'bold 12px Microsoft YaHei';
  ctx.textAlign = 'center';
  ctx.fillText('✈️ 机场管制区', 225, 420);
  ctx.font = '10px Microsoft YaHei';
  ctx.fillText('高度限制: 3000米', 225, 435);

  // 港珠澳大桥空域
  ctx.fillStyle = 'rgba(251, 191, 36, 0.2)';
  ctx.fillRect(350, 280, 100, 80);
  ctx.strokeStyle = '#fbbf24';
  ctx.setLineDash([6, 3]);
  ctx.lineWidth = 3;
  ctx.strokeRect(350, 280, 100, 80);

  ctx.fillStyle = '#fbbf24';
  ctx.font = 'bold 11px Microsoft YaHei';
  ctx.textAlign = 'center';
  ctx.fillText('🌉 大桥空域', 400, 300);
  ctx.font = '9px Microsoft YaHei';
  ctx.fillText('高度限制: 1500米', 400, 312);

  // 横琴新区限制区
  ctx.fillStyle = 'rgba(249, 115, 22, 0.2)';
  ctx.fillRect(250, 350, 120, 100);
  ctx.strokeStyle = '#f97316';
  ctx.setLineDash([5, 5]);
  ctx.lineWidth = 3;
  ctx.strokeRect(250, 350, 120, 100);

  ctx.fillStyle = '#f97316';
  ctx.font = 'bold 11px Microsoft YaHei';
  ctx.textAlign = 'center';
  ctx.fillText('🏗️ 横琴限制区', 310, 380);
  ctx.font = '9px Microsoft YaHei';
  ctx.fillText('高度限制: 2000米', 310, 392);

  ctx.setLineDash([]);
}

// 绘制区域标记
function drawAreaMarkers() {
  zhuhaiAreas.value.forEach(area => {
    // 绘制区域背景圆
    ctx.fillStyle = 'rgba(6, 182, 212, 0.3)';
    ctx.beginPath();
    ctx.arc(area.x, area.y, 15, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制区域圆点
    ctx.fillStyle = '#06b6d4';
    ctx.beginPath();
    ctx.arc(area.x, area.y, 10, 0, 2 * Math.PI);
    ctx.fill();

    // 绘制区域边框
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    ctx.stroke();

    // 绘制区域标签
    if (showLabels.value) {
      // 标签背景
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      const textWidth = ctx.measureText(`${area.icon} ${area.name}`).width;
      ctx.fillRect(area.x - textWidth/2 - 5, area.y - 35, textWidth + 10, 20);

      // 标签文字
      ctx.fillStyle = 'white';
      ctx.font = 'bold 12px Microsoft YaHei';
      ctx.textAlign = 'center';
      ctx.fillText(`${area.icon} ${area.name}`, area.x, area.y - 20);

      // 预警数量
      const warningCount = getAreaWarningCount(area);
      if (warningCount > 0) {
        ctx.fillStyle = '#ef4444';
        ctx.beginPath();
        ctx.arc(area.x + 12, area.y - 8, 8, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = 'white';
        ctx.font = 'bold 10px Microsoft YaHei';
        ctx.fillText(warningCount.toString(), area.x + 12, area.y - 4);
      }
    }
  });
}

// 生成初始预警
function generateInitialWarnings() {
  console.log('🚨 生成初始预警...');

  const initialCount = Math.floor(Math.random() * 3) + 3;
  for (let i = 0; i < initialCount; i++) {
    setTimeout(() => {
      generateNewWarning();
    }, i * 500);
  }
}

// 生成新预警
function generateNewWarning() {
  const warningTypes = ['大风', '暴雨', '雷电', '大雾', '冰雹', '龙卷风'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetX = (Math.random() - 0.5) * 100;
  const offsetY = (Math.random() - 0.5) * 100;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    x: selectedArea.x + offsetX,
    y: selectedArea.y + offsetY,
    intensity: Math.floor(Math.random() * 3) + 1,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  updateLastUpdateTime();

  console.log(`🚨 新增${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 新增${warning.type}预警 - ${warning.location}`, 'info');

  if (activeWarnings.value.length > 10) {
    activeWarnings.value.shift();
  }
}

// 生成严重预警
function generateSevereWarning() {
  const warningTypes = ['龙卷风', '冰雹', '暴雨', '大风'];
  const warningLevels = ['red', 'orange'];
  const areas = zhuhaiAreas.value;

  const selectedArea = areas[Math.floor(Math.random() * areas.length)];
  const warningType = warningTypes[Math.floor(Math.random() * warningTypes.length)];
  const warningLevel = warningLevels[Math.floor(Math.random() * warningLevels.length)];

  const offsetX = (Math.random() - 0.5) * 80;
  const offsetY = (Math.random() - 0.5) * 80;

  const warning = {
    id: ++warningIdCounter,
    type: warningType,
    level: warningLevel,
    location: selectedArea.name,
    x: selectedArea.x + offsetX,
    y: selectedArea.y + offsetY,
    intensity: Math.floor(Math.random() * 2) + 2,
    time: new Date().toLocaleTimeString(),
    timestamp: Date.now()
  };

  activeWarnings.value.push(warning);
  updateLastUpdateTime();

  console.log(`🚨 新增严重${warning.type}预警 - ${warning.location} (${getWarningLevelText(warning.level)})`);
  showNotification(`🚨 严重${warning.type}预警 - ${warning.location}`, 'error');

  setTimeout(() => {
    focusWarning(warning);
  }, 500);

  if (activeWarnings.value.length > 10) {
    activeWarnings.value.shift();
  }
}

// 清除所有预警
function clearAllWarnings() {
  activeWarnings.value = [];
  updateLastUpdateTime();
  showNotification('🗑️ 已清除所有预警', 'success');
  console.log('🗑️ 已清除所有预警');
}

// 聚焦预警
function focusWarning(warning) {
  console.log(`🎯 聚焦到${warning.type}预警 - ${warning.location}`);
  showNotification(`🎯 聚焦到${warning.type}预警`, 'info');

  // 这里可以添加地图缩放到预警位置的逻辑
}

// 聚焦区域
function focusArea(area) {
  console.log(`🗺️ 聚焦到${area.name}`);
  showNotification(`🗺️ 聚焦到${area.name}`, 'info');

  // 这里可以添加地图缩放到区域的逻辑
}

// 聚焦严重预警
function focusOnSevereWarnings() {
  const severeWarnings = activeWarnings.value.filter(w => w.level === 'red' || w.level === 'orange');

  if (severeWarnings.length === 0) return;

  console.log(`🚨 聚焦到 ${severeWarnings.length} 个严重预警`);
  showNotification(`🚨 聚焦到 ${severeWarnings.length} 个严重预警`, 'info');
}

// 重置视图
function resetView() {
  drawMap();
  showNotification('🔄 视图已重置', 'success');
  console.log('🔄 视图已重置');
}

// 切换标签
function toggleLabels() {
  showLabels.value = !showLabels.value;
  drawMap();
  showNotification(`👁️ 标签已${showLabels.value ? '显示' : '隐藏'}`, 'info');
}

// 生成随机预警
function generateRandomWarnings() {
  const count = Math.floor(Math.random() * 3) + 2;
  for (let i = 0; i < count; i++) {
    setTimeout(() => {
      if (Math.random() < 0.3) {
        generateSevereWarning();
      } else {
        generateNewWarning();
      }
    }, i * 300);
  }
  showNotification(`🎲 生成 ${count} 个随机预警`, 'info');
}

// 切换自动更新
function toggleAutoUpdate() {
  autoUpdate.value = !autoUpdate.value;

  if (autoUpdate.value) {
    updateInterval = setInterval(() => {
      if (Math.random() < 0.3) {
        generateNewWarning();
      }
    }, 8000);
    showNotification('▶️ 自动更新已开启', 'success');
    console.log('▶️ 自动更新已开启');
  } else {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
    showNotification('⏸️ 自动更新已暂停', 'info');
    console.log('⏸️ 自动更新已暂停');
  }
}

// 导出预警数据
function exportWarningData() {
  const exportData = {
    exportTime: new Date().toISOString(),
    totalWarnings: activeWarnings.value.length,
    warningsByLevel: {
      red: getWarningCountByLevel('red'),
      orange: getWarningCountByLevel('orange'),
      yellow: getWarningCountByLevel('yellow'),
      blue: getWarningCountByLevel('blue')
    },
    allWarnings: activeWarnings.value
  };

  const dataStr = JSON.stringify(exportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `珠海气象预警数据_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  showNotification('📊 预警数据导出完成', 'success');
  console.log('📊 预警数据导出完成');
}

// 处理地图点击
function handleMapClick(event) {
  const rect = mapCanvas.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  console.log(`地图点击位置: (${x}, ${y})`);
}

// 选择预警
function selectWarning(warning) {
  console.log(`选择预警: ${warning.type} - ${warning.location}`);
  showNotification(`选择了${warning.type}预警`, 'info');
}

// 获取预警样式
function getWarningStyle(warning) {
  const size = 40 + warning.intensity * 20;
  return {
    left: `${(warning.x / 800) * 100}%`,
    top: `${(warning.y / 600) * 100}%`,
    width: `${size}px`,
    height: `${size}px`,
    marginLeft: `-${size/2}px`,
    marginTop: `-${size/2}px`
  };
}

// 工具函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️',
    '冰雹': '🧊',
    '龙卷风': '🌪️'
  };
  return icons[type] || '⚠️';
}

function getWarningLevelText(level) {
  const texts = {
    red: '红色预警',
    orange: '橙色预警',
    yellow: '黄色预警',
    blue: '蓝色预警'
  };
  return texts[level] || '未知预警';
}

function getWarningCountByLevel(level) {
  return activeWarnings.value.filter(w => w.level === level).length;
}

function getAreaWarningCount(area) {
  return activeWarnings.value.filter(w => w.location === area.name).length;
}

function updateLastUpdateTime() {
  lastUpdateTime.value = new Date().toLocaleTimeString();
}

function showNotification(message, type = 'info') {
  notification.value = { message, type };
  setTimeout(() => {
    notification.value = null;
  }, 3000);
}

// 地图控制函数
function toggleBuildings() {
  showBuildings.value = !showBuildings.value;
  drawMap();
  showNotification(`🏢 建筑${showBuildings.value ? '显示' : '隐藏'}`, 'info');
}

function toggleRoads() {
  showRoads.value = !showRoads.value;
  drawMap();
  showNotification(`🛣️ 道路${showRoads.value ? '显示' : '隐藏'}`, 'info');
}

function toggleLandmarks() {
  showLandmarks.value = !showLandmarks.value;
  drawMap();
  showNotification(`🏛️ 地标${showLandmarks.value ? '显示' : '隐藏'}`, 'info');
}

function toggleAirspace() {
  showAirspace.value = !showAirspace.value;
  drawMap();
  showNotification(`✈️ 空域${showAirspace.value ? '显示' : '隐藏'}`, 'info');
}

function cleanup() {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
}
</script>

<style scoped>
.stable-weather-system {
  position: relative;
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  overflow: hidden;
}

.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
  background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
}

.warning-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.warning-circle {
  position: absolute;
  border-radius: 50%;
  pointer-events: all;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.warning-circle:hover {
  transform: scale(1.2);
  z-index: 100;
}

.level-red { 
  background: radial-gradient(circle, rgba(255,0,0,0.8) 0%, rgba(255,0,0,0.3) 70%, transparent 100%);
  border: 3px solid #ff0000;
}
.level-orange { 
  background: radial-gradient(circle, rgba(255,136,0,0.8) 0%, rgba(255,136,0,0.3) 70%, transparent 100%);
  border: 3px solid #ff8800;
}
.level-yellow { 
  background: radial-gradient(circle, rgba(255,255,0,0.8) 0%, rgba(255,255,0,0.3) 70%, transparent 100%);
  border: 3px solid #ffff00;
}
.level-blue { 
  background: radial-gradient(circle, rgba(0,136,255,0.8) 0%, rgba(0,136,255,0.3) 70%, transparent 100%);
  border: 3px solid #0088ff;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
}

.warning-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.warning-label {
  font-size: 10px;
  color: white;
  text-shadow: 1px 1px 2px black;
  font-weight: bold;
}

/* 控制面板样式 */
.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 12px;
  min-width: 320px;
  max-width: 380px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #FFD700;
  text-align: center;
  border-bottom: 2px solid #FFD700;
  padding-bottom: 10px;
}

.control-panel h4 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #87CEEB;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.system-status {
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 11px;
}

.status-label {
  color: #ccc;
}

.status-value {
  font-weight: bold;
}

.status-value.online {
  color: #90EE90;
}

.status-value.severe-count {
  color: #FF6B6B;
  font-weight: bold;
}

.warning-section {
  margin-bottom: 20px;
}

.no-warnings {
  color: #90EE90;
  text-align: center;
  padding: 15px;
  font-size: 12px;
  background: rgba(0, 255, 0, 0.1);
  border-radius: 5px;
}

.warnings-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.warning-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.warning-item.level-red { border-left: 4px solid #ff0000; background: rgba(255, 0, 0, 0.1); }
.warning-item.level-orange { border-left: 4px solid #ff8800; background: rgba(255, 136, 0, 0.1); }
.warning-item.level-yellow { border-left: 4px solid #ffff00; background: rgba(255, 255, 0, 0.1); }
.warning-item.level-blue { border-left: 4px solid #0088ff; background: rgba(0, 136, 255, 0.1); }

.warning-item .warning-icon {
  font-size: 24px;
  min-width: 30px;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 2px;
}

.warning-level {
  font-size: 11px;
  color: #ccc;
  margin-bottom: 2px;
}

.warning-location {
  font-size: 10px;
  color: #999;
  margin-bottom: 2px;
}

.warning-time {
  font-size: 9px;
  color: #666;
}

.warning-stats {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  font-size: 11px;
}

.stat-item.red { background: rgba(255, 0, 0, 0.2); }
.stat-item.orange { background: rgba(255, 136, 0, 0.2); }
.stat-item.yellow { background: rgba(255, 255, 0, 0.2); }
.stat-item.blue { background: rgba(0, 136, 255, 0.2); }

.stat-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 9px;
  opacity: 0.8;
}

.warning-control {
  margin-bottom: 20px;
}

.control-btn {
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.generate {
  background: #4CAF50;
  color: white;
}

.control-btn.generate:hover:not(:disabled) {
  background: #45a049;
}

.control-btn.severe {
  background: #FF6B35;
  color: white;
}

.control-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.control-btn.clear {
  background: #f44336;
  color: white;
}

.control-btn.clear:hover:not(:disabled) {
  background: #da190b;
}

.control-btn.export {
  background: #2196F3;
  color: white;
}

.control-btn.export:hover:not(:disabled) {
  background: #1976D2;
}

.control-btn.active {
  background: #2196F3;
  color: white;
}

.control-btn:not(.active) {
  background: #666;
  color: white;
}

.zhuhai-info-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  max-width: 250px;
}

.zhuhai-info-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.area-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.area-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.area-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.area-icon {
  font-size: 18px;
  min-width: 25px;
}

.area-info {
  flex: 1;
}

.area-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.area-warnings {
  font-size: 10px;
  color: #ccc;
}

.quick-actions-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.quick-actions-panel h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.quick-btn {
  padding: 8px 6px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-btn.overview {
  background: #4CAF50;
  color: white;
}

.quick-btn.overview:hover:not(:disabled) {
  background: #45a049;
}

.quick-btn.severe {
  background: #FF6B35;
  color: white;
}

.quick-btn.severe:hover:not(:disabled) {
  background: #E55A2B;
}

.quick-btn.layers {
  background: #2196F3;
  color: white;
}

.quick-btn.layers:hover:not(:disabled) {
  background: #1976D2;
}

.quick-btn.reset {
  background: #9C27B0;
  color: white;
}

.quick-btn.reset:hover:not(:disabled) {
  background: #7B1FA2;
}

.map-controls {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #444;
}

.map-controls h5 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #87CEEB;
  text-align: center;
}

.map-control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.map-btn {
  padding: 6px 4px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 9px;
  transition: all 0.3s ease;
  text-align: center;
  background: #4a5568;
  color: white;
}

.map-btn:hover {
  background: #2d3748;
}

.map-btn.active {
  background: #3182ce;
  color: white;
  box-shadow: 0 0 8px rgba(49, 130, 206, 0.5);
}

.map-legend {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 180px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.map-legend h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #87CEEB;
  text-align: center;
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 11px;
}

.legend-color {
  width: 20px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.status-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 15px;
  border-radius: 10px;
  min-width: 220px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.notification {
  position: fixed;
  top: 20px;
  right: 50%;
  transform: translateX(50%);
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #4CAF50;
}

.notification.error {
  background: #f44336;
}

.notification.info {
  background: #2196F3;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(50%) translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
