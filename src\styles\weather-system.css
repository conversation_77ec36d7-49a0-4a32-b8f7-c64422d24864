/* 低空三维空域动态气象预警系统样式 */

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  overflow: hidden;
}

/* 加载动画 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid #00ff88;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  max-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

/* 通知系统 */
.notification-container {
  position: fixed;
  top: 20px;
  right: 380px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  border-left: 4px solid;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  max-width: 300px;
}

.notification.warning {
  border-left-color: #ffff00;
}

.notification.danger {
  border-left-color: #ff0000;
}

.notification.info {
  border-left-color: #0088ff;
}

.notification.success {
  border-left-color: #00ff88;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 100;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff88;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    width: 280px;
    font-size: 12px;
  }
  
  .info-panel {
    width: 300px;
    font-size: 11px;
  }
  
  .legend {
    width: 180px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .control-panel {
    width: 250px;
    left: 10px;
    top: 10px;
  }
  
  .info-panel {
    width: 250px;
    right: 10px;
    bottom: 10px;
    max-height: 300px;
  }
  
  .legend {
    width: 150px;
    right: 10px;
    top: 10px;
    font-size: 10px;
  }
  
  .notification-container {
    right: 10px;
    top: 180px;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #00ff88;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00cc6a;
}

/* 按钮悬停效果 */
.btn-hover-effect {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* 数据可视化增强 */
.data-visualization {
  position: relative;
}

.data-point {
  transition: all 0.3s ease;
  cursor: pointer;
}

.data-point:hover {
  transform: scale(1.2);
  filter: brightness(1.3);
}

/* 警告闪烁效果 */
.warning-blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, 
    rgba(30, 60, 114, 0.9) 0%, 
    rgba(42, 82, 152, 0.9) 50%, 
    rgba(74, 144, 226, 0.9) 100%);
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 阴影效果 */
.shadow-soft {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.shadow-medium {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.shadow-strong {
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
}

/* 文字效果 */
.text-glow {
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.text-outline {
  text-shadow: 
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .control-panel,
  .info-panel,
  .legend {
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid white;
  }
  
  .status-indicator {
    background: black;
    border: 1px solid white;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
