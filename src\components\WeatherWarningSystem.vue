<template>
  <div class="weather-warning-system">
    <!-- 三维场景容器 -->
    <div ref="threeContainer" class="three-container"></div>

    <!-- 简化的信息面板 -->
    <div class="simple-info-panel">
      <h3>🌤️ 珠海低空气象预警系统</h3>

      <!-- 当前预警信息 -->
      <div class="warning-section">
        <h4>🚨 当前预警</h4>
        <div v-if="currentWarnings.length === 0" class="no-warnings">
          ✅ 暂无预警
        </div>
        <div v-else class="warnings-list">
          <div
            v-for="(warning, index) in currentWarnings.slice(0, 3)"
            :key="index"
            class="warning-item"
            :class="`warning-${warning.level}`"
          >
            <span class="warning-icon">{{ getWarningIcon(warning.type) }}</span>
            <span class="warning-text">{{ warning.type }} - {{ getWarningLevelText(warning.level) }}</span>
          </div>
        </div>
      </div>

      <!-- 预警图例 -->
      <div class="legend-section">
        <h4>☁️ 云团预警等级</h4>
        <div class="legend-item">
          <span class="cloud-icon" style="color: #ff0000;">☁️</span>
          <span>红色 - 极危险</span>
        </div>
        <div class="legend-item">
          <span class="cloud-icon" style="color: #ff8800;">☁️</span>
          <span>橙色 - 高危险</span>
        </div>
        <div class="legend-item">
          <span class="cloud-icon" style="color: #ffff00;">☁️</span>
          <span>黄色 - 中等危险</span>
        </div>
        <div class="legend-item">
          <span class="cloud-icon" style="color: #0088ff;">☁️</span>
          <span>蓝色 - 低危险</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

// 响应式数据
const threeContainer = ref(null);
const currentWarnings = ref([]);

// Three.js 相关变量
let scene, camera, renderer, controls;
let cloudGroup, buildingGroup;
let animationId;

onMounted(() => {
  console.log('组件挂载开始...');
  try {
    initThreeJS();
    createSimpleTerrain();
    createConeBuildings();
    createWarningClouds();
    initEventListeners();
    startAnimation();

    // 模拟预警数据更新
    updateWarningData();
    setInterval(updateWarningData, 3000); // 每3秒更新一次预警

    console.log('简化系统初始化完成');
  } catch (error) {
    console.error('初始化过程中出错:', error);
  }
});

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  if (renderer && threeContainer.value) {
    threeContainer.value.removeChild(renderer.domElement);
  }
});

// 初始化Three.js场景
function initThreeJS() {
  // 创建场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0x87CEEB); // 天空蓝色背景

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    window.innerWidth / window.innerHeight,
    0.1,
    10000
  );
  camera.position.set(0, 800, 1200);

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  threeContainer.value.appendChild(renderer.domElement);

  // 添加轨道控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.maxPolarAngle = Math.PI / 2;

  // 添加光照
  const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
  directionalLight.position.set(500, 800, 300);
  directionalLight.castShadow = true;
  directionalLight.shadow.mapSize.width = 2048;
  directionalLight.shadow.mapSize.height = 2048;
  scene.add(directionalLight);
}

// 创建简单地形
function createSimpleTerrain() {
  // 地面
  const groundGeometry = new THREE.PlaneGeometry(2000, 2000);
  const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
  const ground = new THREE.Mesh(groundGeometry, groundMaterial);
  ground.rotation.x = -Math.PI / 2;
  ground.receiveShadow = true;
  scene.add(ground);

  // 海水区域
  const waterGeometry = new THREE.PlaneGeometry(2500, 2500);
  const waterMaterial = new THREE.MeshLambertMaterial({
    color: 0x006994,
    transparent: true,
    opacity: 0.7
  });
  const water = new THREE.Mesh(waterGeometry, waterMaterial);
  water.rotation.x = -Math.PI / 2;
  water.position.y = -5;
  scene.add(water);
}

// 创建圆锥体建筑群
function createConeBuildings() {
  buildingGroup = new THREE.Group();

  // 珠海主要区域的圆锥体建筑
  const buildingAreas = [
    { name: '香洲区', center: { x: 0, z: 0 }, count: 15, color: 0x4A90E2 },
    { name: '拱北区', center: { x: 300, z: 300 }, count: 10, color: 0x2C3E50 },
    { name: '金湾区', center: { x: -400, z: -200 }, count: 12, color: 0x27AE60 },
    { name: '横琴区', center: { x: 500, z: -400 }, count: 18, color: 0x8E44AD }
  ];

  buildingAreas.forEach(area => {
    for (let i = 0; i < area.count; i++) {
      // 在区域内随机分布
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 150;
      const x = area.center.x + Math.cos(angle) * distance;
      const z = area.center.z + Math.sin(angle) * distance;

      // 创建圆锥体建筑
      const radius = 15 + Math.random() * 20;
      const height = 40 + Math.random() * 100;
      const geometry = new THREE.ConeGeometry(radius, height, 8);
      const material = new THREE.MeshLambertMaterial({ color: area.color });

      const building = new THREE.Mesh(geometry, material);
      building.position.set(x, height / 2, z);
      building.castShadow = true;
      building.receiveShadow = true;

      buildingGroup.add(building);
    }
  });

  scene.add(buildingGroup);
}

// 创建预警云团
function createWarningClouds() {
  cloudGroup = new THREE.Group();
  scene.add(cloudGroup);
}

// 更新预警数据
function updateWarningData() {
  // 清除现有云团
  cloudGroup.clear();

  // 生成随机预警数据
  const warnings = [];
  const warningTypes = ['大风', '暴雨', '雷电', '大雾'];
  const warningLevels = ['blue', 'yellow', 'orange', 'red'];

  // 随机生成3-8个预警
  const warningCount = 3 + Math.floor(Math.random() * 6);

  for (let i = 0; i < warningCount; i++) {
    const warning = {
      id: i,
      type: warningTypes[Math.floor(Math.random() * warningTypes.length)],
      level: warningLevels[Math.floor(Math.random() * warningLevels.length)],
      x: (Math.random() - 0.5) * 1500,
      z: (Math.random() - 0.5) * 1500,
      intensity: Math.random()
    };
    warnings.push(warning);

    // 创建云团
    createWarningCloud(warning);
  }

  currentWarnings.value = warnings;
}

// 创建单个预警云团
function createWarningCloud(warning) {
  const cloudSize = 50 + warning.intensity * 100;
  const cloudHeight = 100 + warning.intensity * 150;

  // 根据预警等级选择颜色
  let cloudColor;
  switch (warning.level) {
    case 'red': cloudColor = 0xff0000; break;
    case 'orange': cloudColor = 0xff8800; break;
    case 'yellow': cloudColor = 0xffff00; break;
    case 'blue': cloudColor = 0x0088ff; break;
    default: cloudColor = 0x888888;
  }

  // 创建云团几何体（使用多个球体组合）
  const warningCloudGroup = new THREE.Group();

  for (let i = 0; i < 8; i++) {
    const sphereGeometry = new THREE.SphereGeometry(
      cloudSize * (0.5 + Math.random() * 0.5),
      16,
      12
    );
    const sphereMaterial = new THREE.MeshLambertMaterial({
      color: cloudColor,
      transparent: true,
      opacity: 0.6 + warning.intensity * 0.3
    });

    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    sphere.position.set(
      (Math.random() - 0.5) * cloudSize,
      (Math.random() - 0.5) * cloudSize * 0.5,
      (Math.random() - 0.5) * cloudSize
    );

    warningCloudGroup.add(sphere);
  }

  warningCloudGroup.position.set(warning.x, cloudHeight, warning.z);
  warningCloudGroup.userData = warning;

  // 添加轻微的浮动动画
  warningCloudGroup.userData.originalY = cloudHeight;
  warningCloudGroup.userData.animationOffset = Math.random() * Math.PI * 2;

  cloudGroup.add(warningCloudGroup);
}

// 事件监听器
function initEventListeners() {
  window.addEventListener('resize', onWindowResize);
  renderer.domElement.addEventListener('click', onMouseClick);
}

function onWindowResize() {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
}

function onMouseClick(event) {
  // 简化的点击处理
  console.log('点击了场景');
}

// 动画循环
function startAnimation() {
  function animate() {
    animationId = requestAnimationFrame(animate);

    controls.update();

    // 云团浮动动画
    if (cloudGroup) {
      cloudGroup.children.forEach(cloud => {
        if (cloud.userData.originalY) {
          const time = Date.now() * 0.001;
          cloud.position.y = cloud.userData.originalY +
            Math.sin(time + cloud.userData.animationOffset) * 10;
        }
      });
    }

    renderer.render(scene, camera);
  }
  animate();
}

// 辅助函数
function getWarningIcon(type) {
  const icons = {
    '大风': '💨',
    '暴雨': '🌧️',
    '雷电': '⚡',
    '大雾': '🌫️'
  };
  return icons[type] || '⚠️';
}

function getWarningLevelText(level) {
  const levelMap = {
    'red': '红色预警',
    'orange': '橙色预警',
    'yellow': '黄色预警',
    'blue': '蓝色预警'
  };
  return levelMap[level] || '未知预警';
}









// 初始化增强无人机系统
function initEnhancedDroneSystem() {
  droneManager = new EnhancedDroneManager(scene);

  // 添加不同类型的受控无人机
  droneManager.addControlledDrone('patrol_01', 'patrol', { x: 0, y: 100, z: 0 }, 'coastal');
  droneManager.addControlledDrone('weather_01', 'weather', { x: 1000, y: 200, z: 1000 }, 'mountain');
  droneManager.addControlledDrone('emergency_01', 'emergency', { x: -1000, y: 80, z: -500 }, 'airport');
  droneManager.addControlledDrone('bridge_patrol', 'patrol', { x: 500, y: 150, z: 800 }, 'bridge');

  // 启动巡航
  setTimeout(() => {
    droneManager.startAllPatrols();
  }, 2000);
}

// 更新天气数据
function updateWeatherData() {
  const weatherData = generateWeatherData({
    gridSize: 30,
    areaSize: 10000,
    timeOffset: isPlaying.value ? (Date.now() - currentTime.value.getTime()) / 1000 : 0
  });

  // 使用增强的预警系统
  const enhancedWarnings = generateEnhancedWeatherWarnings(
    weatherData.wind,
    weatherData.temperature,
    weatherData.humidity,
    weatherData.precipitation
  );

  // 按优先级排序预警
  const sortedWarnings = sortWarningsByPriority(enhancedWarnings);

  currentWeatherData.value = weatherData;
  currentWarnings.value = sortedWarnings;

  updateWindField(weatherData.wind);
  updateTemperatureField(weatherData.temperature);
  updateWarningZones(sortedWarnings);
}

// 更新无人机状态
function updateDroneStatus() {
  if (droneManager) {
    droneManager.update(1, currentWeatherData.value);
    droneStatus.value = droneManager.getAllStatus();
  }
}

// 更新风场可视化
function updateWindField(windData) {
  // 清除现有风场
  windField.clear();
  
  if (!weatherLayers.wind.visible) return;
  
  windData.forEach(point => {
    const arrow = createWindArrow(point);
    windField.add(arrow);
  });
}

// 创建风向箭头
function createWindArrow(windPoint) {
  const direction = new THREE.Vector3(
    Math.cos(windPoint.direction),
    0,
    Math.sin(windPoint.direction)
  );
  
  const origin = new THREE.Vector3(windPoint.x, windPoint.y + 50, windPoint.z);
  const length = windPoint.speed * 5;
  const color = getWindSpeedColor(windPoint.speed);
  
  const arrowHelper = new THREE.ArrowHelper(direction, origin, length, color);
  return arrowHelper;
}

// 根据风速获取颜色
function getWindSpeedColor(speed) {
  if (speed < 5) return 0x00ff00;      // 绿色 - 微风
  if (speed < 15) return 0xffff00;     // 黄色 - 轻风
  if (speed < 25) return 0xff8800;     // 橙色 - 强风
  return 0xff0000;                     // 红色 - 烈风
}

// 更新温度场
function updateTemperatureField(temperatureData) {
  temperatureField.clear();
  
  if (!weatherLayers.temperature.visible) return;
  
  // 创建温度热力图
  temperatureData.forEach(point => {
    const geometry = new THREE.SphereGeometry(20, 8, 6);
    const material = new THREE.MeshBasicMaterial({
      color: getTemperatureColor(point.temperature),
      transparent: true,
      opacity: 0.6
    });
    
    const sphere = new THREE.Mesh(geometry, material);
    sphere.position.set(point.x, point.y + 30, point.z);
    temperatureField.add(sphere);
  });
}

// 根据温度获取颜色
function getTemperatureColor(temperature) {
  if (temperature < 0) return 0x0000ff;    // 蓝色 - 严寒
  if (temperature < 10) return 0x00ffff;   // 青色 - 寒冷
  if (temperature < 20) return 0x00ff00;   // 绿色 - 凉爽
  if (temperature < 30) return 0xffff00;   // 黄色 - 温暖
  if (temperature < 40) return 0xff8800;   // 橙色 - 炎热
  return 0xff0000;                         // 红色 - 酷热
}

// 更新预警区域
function updateWarningZones(warnings) {
  warningZones.clear();
  
  if (!weatherLayers.warnings.visible) return;
  
  warnings.forEach(warning => {
    const geometry = new THREE.CylinderGeometry(
      warning.radius, warning.radius, warning.height, 16
    );
    const material = new THREE.MeshBasicMaterial({
      color: getWarningColor(warning.level),
      transparent: true,
      opacity: 0.4
    });
    
    const cylinder = new THREE.Mesh(geometry, material);
    cylinder.position.set(warning.x, warning.height / 2, warning.z);
    warningZones.add(cylinder);
  });
}

// 根据预警等级获取颜色
function getWarningColor(level) {
  switch (level) {
    case 'red': return 0xff0000;      // 红色预警
    case 'orange': return 0xff8800;   // 橙色预警
    case 'yellow': return 0xffff00;   // 黄色预警
    case 'blue': return 0x0088ff;     // 蓝色预警
    default: return 0x888888;         // 默认灰色
  }
}

// 事件监听器
function initEventListeners() {
  window.addEventListener('resize', onWindowResize);
  renderer.domElement.addEventListener('click', onMouseClick);
}

function onWindowResize() {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
}

function onMouseClick(event) {
  // 射线检测，获取点击的位置信息
  const mouse = new THREE.Vector2();
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

  const raycaster = new THREE.Raycaster();
  raycaster.setFromCamera(mouse, camera);

  // 检测所有场景对象
  const intersects = raycaster.intersectObjects(scene.children, true);

  if (intersects.length > 0) {
    const intersectedObject = intersects[0].object;
    const point = intersects[0].point;

    // 查找父级对象的用户数据
    let targetObject = intersectedObject;
    while (targetObject && !targetObject.userData.name) {
      targetObject = targetObject.parent;
    }

    if (targetObject && targetObject.userData.name) {
      // 点击了地标建筑
      selectedPoint.value = {
        x: point.x,
        y: point.y,
        z: point.z,
        landmark: {
          name: targetObject.userData.name,
          type: targetObject.userData.type,
          description: targetObject.userData.description
        },
        weather: getWeatherAtPoint(point)
      };
      console.log('点击了地标:', targetObject.userData.name);
    } else {
      // 点击了普通地面
      selectedPoint.value = {
        x: point.x,
        y: point.y,
        z: point.z,
        weather: getWeatherAtPoint(point)
      };
    }
  }
}

function getWeatherAtPoint(point) {
  // 模拟获取指定点的天气信息
  return {
    temperature: Math.random() * 40 - 10,
    windSpeed: Math.random() * 30,
    humidity: Math.random() * 100,
    pressure: 1000 + Math.random() * 50
  };
}

// 创建简单建筑群
function createSimpleBuildings() {
  // 珠海主要区域的建筑
  const buildingAreas = [
    { name: '香洲区', center: { x: 0, z: 0 }, count: 12, color: 0x4A90E2 },
    { name: '拱北区', center: { x: 300, z: 300 }, count: 8, color: 0x2C3E50 },
    { name: '金湾区', center: { x: -400, z: -200 }, count: 10, color: 0x27AE60 },
    { name: '横琴区', center: { x: 500, z: -400 }, count: 15, color: 0x8E44AD }
  ];

  buildingAreas.forEach(area => {
    for (let i = 0; i < area.count; i++) {
      // 创建不同高度的建筑
      const width = 20 + Math.random() * 25;
      const height = 40 + Math.random() * 120;
      const depth = 15 + Math.random() * 20;

      const geometry = new THREE.BoxGeometry(width, height, depth);
      const material = new THREE.MeshLambertMaterial({
        color: area.color,
        transparent: true,
        opacity: 0.9
      });

      const building = new THREE.Mesh(geometry, material);

      // 在区域中心周围随机分布
      const angle = Math.random() * Math.PI * 2;
      const distance = Math.random() * 150;
      const x = area.center.x + Math.cos(angle) * distance;
      const z = area.center.z + Math.sin(angle) * distance;

      building.position.set(x, height / 2, z);
      building.castShadow = true;
      building.receiveShadow = true;

      scene.add(building);

      // 为高层建筑添加简单的玻璃效果
      if (height > 80) {
        const glassGeometry = new THREE.BoxGeometry(width + 1, height + 1, depth + 1);
        const glassMaterial = new THREE.MeshLambertMaterial({
          color: 0x87CEEB,
          transparent: true,
          opacity: 0.3
        });
        const glass = new THREE.Mesh(glassGeometry, glassMaterial);
        glass.position.copy(building.position);
        scene.add(glass);
      }
    }
  });

  // 添加几个地标建筑
  createLandmarkBuildings();
}

// 创建地标建筑
function createLandmarkBuildings() {
  // 珠海中心大厦
  const centerTower = new THREE.Group();
  const towerGeometry = new THREE.BoxGeometry(25, 150, 20);
  const towerMaterial = new THREE.MeshLambertMaterial({ color: 0x1ABC9C });
  const tower = new THREE.Mesh(towerGeometry, towerMaterial);
  tower.position.y = 75;
  centerTower.add(tower);

  // 玻璃幕墙
  const glassGeometry = new THREE.BoxGeometry(27, 152, 22);
  const glassMaterial = new THREE.MeshLambertMaterial({
    color: 0x3498DB,
    transparent: true,
    opacity: 0.4
  });
  const glass = new THREE.Mesh(glassGeometry, glassMaterial);
  glass.position.y = 75;
  centerTower.add(glass);

  centerTower.position.set(0, 0, 0);
  scene.add(centerTower);

  // 机场建筑
  const airportGeometry = new THREE.BoxGeometry(120, 20, 50);
  const airportMaterial = new THREE.MeshLambertMaterial({ color: 0x95A5A6 });
  const airport = new THREE.Mesh(airportGeometry, airportMaterial);
  airport.position.set(-600, 10, -400);
  scene.add(airport);

  // 控制塔
  const towerControlGeometry = new THREE.CylinderGeometry(4, 6, 40, 8);
  const towerControlMaterial = new THREE.MeshLambertMaterial({ color: 0x34495E });
  const towerControl = new THREE.Mesh(towerControlGeometry, towerControlMaterial);
  towerControl.position.set(-550, 20, -400);
  scene.add(towerControl);
}

// 创建真实地面和水面
function createRealisticGround() {
  // 陆地区域
  const landGeometry = new THREE.PlaneGeometry(1500, 1500);
  const landMaterial = new THREE.MeshLambertMaterial({
    color: 0x8FBC8F,
    transparent: true,
    opacity: 0.9
  });
  const land = new THREE.Mesh(landGeometry, landMaterial);
  land.rotation.x = -Math.PI / 2;
  land.position.set(0, -2, 0);
  scene.add(land);

  // 海水区域
  const waterGeometry = new THREE.PlaneGeometry(2500, 2500);
  const waterMaterial = new THREE.MeshLambertMaterial({
    color: 0x006994,
    transparent: true,
    opacity: 0.7
  });
  const water = new THREE.Mesh(waterGeometry, waterMaterial);
  water.rotation.x = -Math.PI / 2;
  water.position.set(0, -5, 0);
  scene.add(water);

  // 添加一些小岛
  const islands = [
    { x: 400, z: 300, size: 80 },
    { x: -350, z: 400, size: 60 },
    { x: 600, z: -200, size: 50 }
  ];

  islands.forEach(island => {
    const islandGeometry = new THREE.CircleGeometry(island.size, 12);
    const islandMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
    const islandMesh = new THREE.Mesh(islandGeometry, islandMaterial);
    islandMesh.rotation.x = -Math.PI / 2;
    islandMesh.position.set(island.x, -1, island.z);
    scene.add(islandMesh);
  });

  // 添加一些道路
  createSimpleRoads();
}

// 创建简单道路
function createSimpleRoads() {
  const roads = [
    // 主要道路
    { start: { x: -600, z: 0 }, end: { x: 600, z: 0 }, width: 12 },
    { start: { x: 0, z: -600 }, end: { x: 0, z: 600 }, width: 10 },
    // 次要道路
    { start: { x: -400, z: -300 }, end: { x: 400, z: 300 }, width: 8 },
    { start: { x: -400, z: 300 }, end: { x: 400, z: -300 }, width: 8 }
  ];

  roads.forEach(roadData => {
    const { start, end, width } = roadData;
    const length = Math.sqrt(
      Math.pow(end.x - start.x, 2) + Math.pow(end.z - start.z, 2)
    );

    const roadGeometry = new THREE.BoxGeometry(length, 0.5, width);
    const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x2C3E50 });
    const road = new THREE.Mesh(roadGeometry, roadMaterial);

    const centerX = (start.x + end.x) / 2;
    const centerZ = (start.z + end.z) / 2;
    const angle = Math.atan2(end.z - start.z, end.x - start.x);

    road.position.set(centerX, 0, centerZ);
    road.rotation.y = angle;

    scene.add(road);
  });
}

// 动画循环
function startAnimation() {
  console.log('开始动画循环...');
  function animate() {
    animationId = requestAnimationFrame(animate);

    controls.update();

    // 更新时间（如果正在播放）
    if (isPlaying.value) {
      currentTime.value = new Date(currentTime.value.getTime() + 60000); // 每帧前进1分钟
    }

    // 更新水面动画
    if (terrainObject) {
      updateEnhancedWater(terrainObject, Date.now());
    }

    renderer.render(scene, camera);
  }
  animate();
  console.log('动画循环已启动');
}

// 组件方法
function toggleWeatherLayer(layerName) {
  weatherLayers[layerName].visible = !weatherLayers[layerName].visible;

  // 控制不同图层的显示/隐藏
  switch (layerName) {
    case 'landmarks':
      if (landmarkGroup) {
        landmarkGroup.visible = weatherLayers[layerName].visible;
      }
      break;
    case 'coastline':
      if (coastlineGroup) {
        coastlineGroup.visible = weatherLayers[layerName].visible;
      }
      break;
    case 'drones':
      if (droneManager && droneManager.droneGroup) {
        droneManager.droneGroup.visible = weatherLayers[layerName].visible;
      }
      break;
    default:
      updateWeatherData(); // 重新渲染天气数据
  }
}

function onTimeChange(newTime) {
  currentTime.value = newTime;
  updateWeatherData();
}

function togglePlayback() {
  isPlaying.value = !isPlaying.value;
}

function changeView(viewType) {
  switch (viewType) {
    case 'overview':
      camera.position.set(0, 3000, 4000);
      controls.target.set(0, 0, 0);
      break;
    case 'close':
      camera.position.set(0, 800, 1200);
      controls.target.set(0, 0, 0);
      break;
    case 'side':
      camera.position.set(4000, 1500, 0);
      controls.target.set(0, 0, 0);
      break;
    case 'airport':
      // 珠海机场视角
      camera.position.set(-1500, 500, -1000);
      controls.target.set(-1500, 0, -1000);
      break;
    case 'bridge':
      // 港珠澳大桥视角
      camera.position.set(800, 400, 1200);
      controls.target.set(800, 0, 1200);
      break;
  }
  controls.update();
}

// 无人机控制处理
function handleDroneControl(action) {
  if (!droneManager) return;

  switch (action) {
    case 'start-all':
      droneManager.startAllPatrols();
      break;
    case 'recall-all':
      droneManager.recallAllDrones();
      break;
    case 'emergency-stop':
      droneManager.emergencyStopAll();
      break;
  }
}

// 选择无人机
function selectDrone(droneId) {
  if (droneManager) {
    const drone = droneManager.selectDrone(droneId);
    selectedDroneData.value = drone ? drone.getDetailedStatus() : null;
  }
}

// 处理无人机指令
function handleDroneCommand(command, parameters) {
  if (droneManager) {
    droneManager.sendCommand(command, parameters);
  }
}

// 设置航点任务
function setWaypointMission(droneId, waypoints) {
  if (droneManager) {
    droneManager.setWaypointMission(droneId, waypoints);
  }
}
</script>

<style scoped>
.weather-warning-system {
  position: relative;
  width: 100%;
  height: 100%;
}

.three-container {
  width: 100%;
  height: 100%;
}

.legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 8px;
  min-width: 200px;
}

.legend h3 {
  margin-bottom: 10px;
  font-size: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.color-box {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-radius: 3px;
}

.icon {
  width: 20px;
  margin-right: 10px;
  text-align: center;
  font-size: 14px;
}
</style>
