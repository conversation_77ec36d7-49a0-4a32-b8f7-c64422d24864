<template>
  <div class="offline-smart-city">
    <!-- Cesium 3D地球容器 -->
    <div ref="cesiumContainer" class="cesium-viewer"></div>
    
    <!-- 智慧城市控制面板 -->
    <div class="smart-panel">
      <div class="panel-header">
        <h2>🏙️ 智慧珠海 (离线版)</h2>
        <div class="city-status">
          <span class="status-dot" :class="systemStatus.class"></span>
          <span class="status-text">{{ systemStatus.text }}</span>
        </div>
      </div>

      <!-- 城市概览 -->
      <div class="city-overview">
        <div class="overview-item">
          <div class="item-icon">🌡️</div>
          <div class="item-content">
            <div class="item-label">温度</div>
            <div class="item-value">{{ weatherData.temperature }}°C</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">💨</div>
          <div class="item-content">
            <div class="item-label">风速</div>
            <div class="item-value">{{ weatherData.windSpeed }}km/h</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">💧</div>
          <div class="item-content">
            <div class="item-label">湿度</div>
            <div class="item-value">{{ weatherData.humidity }}%</div>
          </div>
        </div>
        <div class="overview-item">
          <div class="item-icon">👥</div>
          <div class="item-content">
            <div class="item-label">人口</div>
            <div class="item-value">244.9万</div>
          </div>
        </div>
      </div>

      <!-- 智慧功能控制 -->
      <div class="smart-controls">
        <h3>🎛️ 智慧功能</h3>
        <div class="control-grid">
          <button @click="toggleBuildings" class="smart-btn" :class="{ active: showBuildings }">
            🏢 建筑物
          </button>
          <button @click="toggleTraffic" class="smart-btn" :class="{ active: showTraffic }">
            🚗 交通流
          </button>
          <button @click="toggleWeather" class="smart-btn" :class="{ active: showWeather }">
            🌦️ 天气层
          </button>
          <button @click="togglePOI" class="smart-btn" :class="{ active: showPOI }">
            📍 兴趣点
          </button>
          <button @click="toggleSensors" class="smart-btn" :class="{ active: showSensors }">
            📡 传感器
          </button>
          <button @click="toggleDrones" class="smart-btn" :class="{ active: showDrones }">
            🚁 无人机
          </button>
        </div>
      </div>

      <!-- 区域选择 -->
      <div class="area-selection">
        <h3>🗺️ 区域导航</h3>
        <div class="area-grid">
          <button @click="flyToArea('xiangzhou')" class="area-btn">🏢 香洲区</button>
          <button @click="flyToArea('jinwan')" class="area-btn">✈️ 金湾区</button>
          <button @click="flyToArea('doumen')" class="area-btn">🌾 斗门区</button>
          <button @click="flyToArea('hengqin')" class="area-btn">🏗️ 横琴新区</button>
        </div>
      </div>

      <!-- 实时数据 -->
      <div class="realtime-data">
        <h3>📊 实时数据</h3>
        <div class="data-list">
          <div class="data-item">
            <span class="data-label">空气质量:</span>
            <span class="data-value good">优</span>
          </div>
          <div class="data-item">
            <span class="data-label">交通状况:</span>
            <span class="data-value normal">畅通</span>
          </div>
          <div class="data-item">
            <span class="data-label">能耗状态:</span>
            <span class="data-value good">正常</span>
          </div>
          <div class="data-item">
            <span class="data-label">安全等级:</span>
            <span class="data-value good">安全</span>
          </div>
        </div>
      </div>

      <!-- 时间显示 -->
      <div class="time-display">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>
    </div>

    <!-- 通知系统 -->
    <div v-if="notification" class="notification" :class="notification.type">
      <div class="notification-icon">{{ getNotificationIcon(notification.type) }}</div>
      <div class="notification-content">
        <div class="notification-title">{{ notification.title }}</div>
        <div class="notification-message">{{ notification.message }}</div>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as Cesium from 'cesium';

// 响应式数据
const cesiumContainer = ref(null);
const systemStatus = ref({ text: '初始化中...', class: 'loading' });
const isLoading = ref(true);
const loadingText = ref('正在加载离线智慧城市系统...');
const notification = ref(null);
const currentTime = ref('');
const currentDate = ref('');

// 功能开关
const showBuildings = ref(true);
const showTraffic = ref(false);
const showWeather = ref(true);
const showPOI = ref(true);
const showSensors = ref(false);
const showDrones = ref(false);

// 城市数据
const weatherData = ref({
  temperature: 24,
  windSpeed: 12,
  humidity: 68
});

// 系统变量
let viewer = null;
let timeInterval = null;
let weatherInterval = null;
let entities = {
  buildings: [],
  traffic: [],
  weather: [],
  poi: [],
  sensors: [],
  drones: []
};

// 珠海区域数据
const zhuhaiAreas = {
  xiangzhou: {
    name: '香洲区',
    position: [113.5767, 22.2707, 50000],
    description: '珠海市政治、经济、文化中心'
  },
  jinwan: {
    name: '金湾区',
    position: [113.3600, 22.1400, 30000],
    description: '珠海国际机场所在地'
  },
  doumen: {
    name: '斗门区',
    position: [113.2900, 22.2100, 40000],
    description: '农业和生态旅游区'
  },
  hengqin: {
    name: '横琴新区',
    position: [113.4200, 22.1300, 25000],
    description: '粤港澳大湾区重要平台'
  }
};

// 组件挂载
onMounted(async () => {
  try {
    await initializeOfflineSmartCity();
    startTimeUpdate();
    startWeatherUpdate();
  } catch (error) {
    console.error('离线智慧城市系统初始化失败:', error);
    systemStatus.value = { text: '❌ 初始化失败', class: 'error' };
    isLoading.value = false;
  }
});

// 组件卸载
onUnmounted(() => {
  cleanup();
});

// 初始化离线智慧城市系统
async function initializeOfflineSmartCity() {
  await nextTick();
  
  loadingText.value = '正在创建离线3D地球...';
  systemStatus.value = { text: '离线模式启动中...', class: 'loading' };
  
  try {
    // 创建完全离线的Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false,
      // 使用椭球地形提供商（完全离线）
      terrainProvider: new Cesium.EllipsoidTerrainProvider(),
      // 使用自定义离线影像提供商
      imageryProvider: createOfflineImageryProvider()
    });

    loadingText.value = '正在配置离线地球效果...';
    
    // 设置初始视图到珠海
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000),
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    });

    // 启用基本效果
    setupOfflineEffects();
    
    loadingText.value = '正在加载城市数据...';
    
    // 加载城市数据
    await loadOfflineCityData();
    
    systemStatus.value = { text: '🟢 离线系统就绪', class: 'online' };
    isLoading.value = false;
    
    showNotification('success', '智慧珠海离线版', '离线系统初始化完成，所有功能正常运行！');
    
    // 自动展示城市特色
    setTimeout(() => {
      showcaseCity();
    }, 2000);
    
  } catch (error) {
    console.error('离线系统初始化失败:', error);
    systemStatus.value = { text: '❌ 离线模式失败', class: 'error' };
    isLoading.value = false;
    throw error;
  }
}

// 创建离线影像提供商
function createOfflineImageryProvider() {
  const canvas = document.createElement('canvas');
  canvas.width = 512;
  canvas.height = 512;
  const ctx = canvas.getContext('2d');
  
  // 创建地球背景
  const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
  gradient.addColorStop(0, '#4a90e2');
  gradient.addColorStop(0.3, '#2e7d32');
  gradient.addColorStop(0.7, '#1565c0');
  gradient.addColorStop(1, '#0d47a1');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, 512, 512);
  
  // 添加陆地形状（模拟中国南部）
  ctx.fillStyle = '#4caf50';
  ctx.beginPath();
  ctx.arc(256, 200, 80, 0, 2 * Math.PI);
  ctx.fill();
  
  // 添加珠海区域
  ctx.fillStyle = '#ff6b35';
  ctx.beginPath();
  ctx.arc(280, 220, 15, 0, 2 * Math.PI);
  ctx.fill();
  
  // 添加一些岛屿
  ctx.fillStyle = '#66bb6a';
  for (let i = 0; i < 10; i++) {
    ctx.beginPath();
    ctx.arc(
      200 + Math.random() * 112,
      180 + Math.random() * 80,
      Math.random() * 8 + 3,
      0,
      2 * Math.PI
    );
    ctx.fill();
  }
  
  // 添加文字标识
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 16px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('珠海市', 280, 240);
  
  return new Cesium.SingleTileImageryProvider({
    url: canvas.toDataURL(),
    rectangle: Cesium.Rectangle.fromDegrees(-180, -90, 180, 90)
  });
}

// 设置离线效果
function setupOfflineEffects() {
  // 启用基本光照
  viewer.scene.globe.enableLighting = false; // 关闭复杂光照避免性能问题
  
  // 设置大气效果
  viewer.scene.skyAtmosphere.show = true;
  viewer.scene.fog.enabled = true;
  viewer.scene.fog.density = 0.0001;
  
  // 设置地球颜色
  viewer.scene.globe.baseColor = Cesium.Color.fromCssColorString('#1a4b3a');
  
  // 设置背景色
  viewer.scene.backgroundColor = Cesium.Color.fromCssColorString('#001122');
}

// 加载离线城市数据
async function loadOfflineCityData() {
  // 加载POI数据
  if (showPOI.value) {
    await loadOfflinePOIData();
  }

  // 加载建筑物数据
  if (showBuildings.value) {
    await loadOfflineBuildingData();
  }

  // 加载天气数据
  if (showWeather.value) {
    await loadOfflineWeatherData();
  }
}

// 加载离线POI数据
async function loadOfflinePOIData() {
  const pois = [
    { name: '珠海市政府', lng: 113.5767, lat: 22.2707, icon: '🏛️', color: '#FF6B35' },
    { name: '珠海机场', lng: 113.3600, lat: 22.1400, icon: '✈️', color: '#4ECDC4' },
    { name: '港珠澳大桥', lng: 113.5200, lat: 22.2100, icon: '🌉', color: '#45B7D1' },
    { name: '珠海大剧院', lng: 113.5800, lat: 22.2600, icon: '🎭', color: '#96CEB4' },
    { name: '长隆海洋王国', lng: 113.4300, lat: 22.1200, icon: '🐋', color: '#FFEAA7' }
  ];

  pois.forEach(poi => {
    const entity = viewer.entities.add({
      name: poi.name,
      position: Cesium.Cartesian3.fromDegrees(poi.lng, poi.lat, 100),
      billboard: {
        image: createOfflinePOIIcon(poi.icon, poi.color),
        scale: 1.2,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: poi.name,
        font: '14pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -60),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    entities.poi.push(entity);
  });
}

// 加载离线建筑物数据
async function loadOfflineBuildingData() {
  const buildings = [
    { name: '珠海中心大厦', lng: 113.5767, lat: 22.2707, height: 280, width: 80, depth: 80, color: Cesium.Color.CYAN },
    { name: '华发商都', lng: 113.5800, lat: 22.2650, height: 180, width: 120, depth: 100, color: Cesium.Color.ORANGE },
    { name: '珠海国际会展中心', lng: 113.5850, lat: 22.2600, height: 60, width: 200, depth: 150, color: Cesium.Color.PURPLE },
    { name: '横琴金融大厦', lng: 113.4300, lat: 22.1300, height: 220, width: 70, depth: 70, color: Cesium.Color.GOLD },
    { name: '珠海大剧院', lng: 113.5800, lat: 22.2600, height: 45, width: 150, depth: 120, color: Cesium.Color.PINK },
    { name: '金湾机场航站楼', lng: 113.3600, lat: 22.1400, height: 35, width: 300, depth: 200, color: Cesium.Color.LIGHTBLUE }
  ];

  buildings.forEach(building => {
    const entity = viewer.entities.add({
      name: building.name,
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat, building.height / 2),
      box: {
        dimensions: new Cesium.Cartesian3(building.width, building.depth, building.height),
        material: building.color.withAlpha(0.8),
        outline: true,
        outlineColor: building.color
      },
      label: {
        text: building.name,
        font: '12pt Microsoft YaHei',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -building.height / 2 - 30),
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.buildings.push(entity);

    // 添加建筑顶部灯光
    const topLight = viewer.entities.add({
      name: `${building.name}_顶灯`,
      position: Cesium.Cartesian3.fromDegrees(building.lng, building.lat, building.height + 5),
      point: {
        pixelSize: 12,
        color: building.color,
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    });

    entities.buildings.push(topLight);
  });
}

// 加载离线天气数据
async function loadOfflineWeatherData() {
  // 创建天气效果区域
  const weatherLayers = [
    { name: '低层云', height: 2000, radius: 15000, color: Cesium.Color.LIGHTBLUE.withAlpha(0.3) },
    { name: '中层云', height: 4000, radius: 18000, color: Cesium.Color.WHITE.withAlpha(0.2) },
    { name: '高层云', height: 6000, radius: 12000, color: Cesium.Color.LIGHTGRAY.withAlpha(0.15) }
  ];

  weatherLayers.forEach(layer => {
    const weatherEntity = viewer.entities.add({
      name: layer.name,
      position: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, layer.height),
      ellipse: {
        semiMinorAxis: layer.radius,
        semiMajorAxis: layer.radius,
        material: layer.color,
        outline: true,
        outlineColor: layer.color.withAlpha(0.5),
        height: layer.height
      }
    });

    entities.weather.push(weatherEntity);
  });
}

// 创建离线POI图标
function createOfflinePOIIcon(icon, color) {
  const canvas = document.createElement('canvas');
  canvas.width = 64;
  canvas.height = 64;
  const ctx = canvas.getContext('2d');

  // 绘制圆形背景
  ctx.fillStyle = color;
  ctx.beginPath();
  ctx.arc(32, 32, 28, 0, 2 * Math.PI);
  ctx.fill();

  // 绘制边框
  ctx.strokeStyle = '#FFFFFF';
  ctx.lineWidth = 4;
  ctx.stroke();

  // 绘制图标
  ctx.font = '24px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = '#FFFFFF';
  ctx.fillText(icon, 32, 32);

  return canvas.toDataURL();
}

// 展示城市特色
function showcaseCity() {
  showNotification('info', '智慧珠海', '正在为您展示珠海市智慧城市特色...');

  // 依次展示各个区域
  setTimeout(() => flyToArea('xiangzhou'), 1000);
  setTimeout(() => flyToArea('hengqin'), 4000);
  setTimeout(() => flyToArea('jinwan'), 7000);
  setTimeout(() => flyToArea('doumen'), 10000);
  setTimeout(() => {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(113.5767, 22.2707, 100000),
      duration: 3
    });
  }, 13000);
}

// 飞行到指定区域
function flyToArea(areaKey) {
  const area = zhuhaiAreas[areaKey];
  if (!area || !viewer) return;

  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(area.position[0], area.position[1], area.position[2]),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    },
    duration: 3
  });

  showNotification('info', area.name, area.description);
}

// 切换功能
function toggleBuildings() {
  showBuildings.value = !showBuildings.value;
  entities.buildings.forEach(entity => {
    entity.show = showBuildings.value;
  });
  showNotification('info', '建筑物', showBuildings.value ? '已显示' : '已隐藏');
}

function toggleTraffic() {
  showTraffic.value = !showTraffic.value;
  if (showTraffic.value) {
    loadOfflineTrafficData();
  } else {
    entities.traffic.forEach(entity => {
      viewer.entities.remove(entity);
    });
    entities.traffic = [];
  }
  showNotification('info', '交通流', showTraffic.value ? '已显示' : '已隐藏');
}

function toggleWeather() {
  showWeather.value = !showWeather.value;
  entities.weather.forEach(entity => {
    entity.show = showWeather.value;
  });
  showNotification('info', '天气层', showWeather.value ? '已显示' : '已隐藏');
}

function togglePOI() {
  showPOI.value = !showPOI.value;
  entities.poi.forEach(entity => {
    entity.show = showPOI.value;
  });
  showNotification('info', '兴趣点', showPOI.value ? '已显示' : '已隐藏');
}

function toggleSensors() {
  showSensors.value = !showSensors.value;
  if (showSensors.value) {
    loadOfflineSensorData();
  } else {
    entities.sensors.forEach(entity => {
      viewer.entities.remove(entity);
    });
    entities.sensors = [];
  }
  showNotification('info', '传感器', showSensors.value ? '已显示' : '已隐藏');
}

function toggleDrones() {
  showDrones.value = !showDrones.value;
  if (showDrones.value) {
    loadOfflineDroneData();
  } else {
    entities.drones.forEach(entity => {
      viewer.entities.remove(entity);
    });
    entities.drones = [];
  }
  showNotification('info', '无人机', showDrones.value ? '已显示' : '已隐藏');
}

// 加载离线交通数据
function loadOfflineTrafficData() {
  const trafficRoutes = [
    { name: '香洲-华发快速路', start: [113.5767, 22.2707], end: [113.5800, 22.2650], color: Cesium.Color.GREEN, width: 12 },
    { name: '珠海大道', start: [113.5800, 22.2650], end: [113.4300, 22.1300], color: Cesium.Color.YELLOW, width: 10 },
    { name: '机场高速', start: [113.4300, 22.1300], end: [113.3600, 22.1400], color: Cesium.Color.RED, width: 15 },
    { name: '港珠澳大桥连接线', start: [113.5200, 22.2100], end: [113.5500, 22.2300], color: Cesium.Color.BLUE, width: 20 }
  ];

  trafficRoutes.forEach(route => {
    const roadEntity = viewer.entities.add({
      name: route.name,
      polyline: {
        positions: [
          Cesium.Cartesian3.fromDegrees(route.start[0], route.start[1], 20),
          Cesium.Cartesian3.fromDegrees(route.end[0], route.end[1], 20)
        ],
        width: route.width,
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: 0.3,
          color: route.color
        }),
        clampToGround: false
      }
    });

    entities.traffic.push(roadEntity);
  });
}

// 加载离线传感器数据
function loadOfflineSensorData() {
  const sensors = [
    { name: '空气质量监测站', lng: 113.5767, lat: 22.2707, type: 'air', color: Cesium.Color.GREEN },
    { name: '交通监控点', lng: 113.5800, lat: 22.2650, type: 'traffic', color: Cesium.Color.BLUE },
    { name: '噪音监测点', lng: 113.4300, lat: 22.1300, type: 'noise', color: Cesium.Color.ORANGE }
  ];

  sensors.forEach(sensor => {
    const entity = viewer.entities.add({
      name: sensor.name,
      position: Cesium.Cartesian3.fromDegrees(sensor.lng, sensor.lat, 25),
      cylinder: {
        length: 50,
        topRadius: 15,
        bottomRadius: 20,
        material: sensor.color.withAlpha(0.8),
        outline: true,
        outlineColor: sensor.color,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    });

    entities.sensors.push(entity);
  });
}

// 加载离线无人机数据
function loadOfflineDroneData() {
  const drones = [
    { name: '巡逻无人机1', lng: 113.5767, lat: 22.2707, height: 150, color: Cesium.Color.YELLOW },
    { name: '监控无人机2', lng: 113.4300, lat: 22.1300, height: 120, color: Cesium.Color.BLUE }
  ];

  drones.forEach(drone => {
    const entity = viewer.entities.add({
      name: drone.name,
      position: Cesium.Cartesian3.fromDegrees(drone.lng, drone.lat, drone.height),
      box: {
        dimensions: new Cesium.Cartesian3(8, 8, 3),
        material: drone.color.withAlpha(0.9),
        outline: true,
        outlineColor: drone.color
      }
    });

    entities.drones.push(entity);
  });
}

// 启动时间更新
function startTimeUpdate() {
  timeInterval = setInterval(() => {
    const now = new Date();
    currentTime.value = now.toLocaleTimeString('zh-CN');
    currentDate.value = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  }, 1000);
}

// 启动天气更新
function startWeatherUpdate() {
  weatherInterval = setInterval(() => {
    weatherData.value.temperature = Math.round(20 + Math.random() * 10);
    weatherData.value.windSpeed = Math.round(5 + Math.random() * 20);
    weatherData.value.humidity = Math.round(50 + Math.random() * 30);
  }, 30000);
}

// 显示通知
function showNotification(type, title, message) {
  notification.value = { type, title, message };
  setTimeout(() => {
    notification.value = null;
  }, 4000);
}

// 获取通知图标
function getNotificationIcon(type) {
  const icons = {
    success: '✅',
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌'
  };
  return icons[type] || 'ℹ️';
}

// 清理资源
function cleanup() {
  if (timeInterval) {
    clearInterval(timeInterval);
  }

  if (weatherInterval) {
    clearInterval(weatherInterval);
  }

  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
}
</script>

<style scoped>
.offline-smart-city {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

.cesium-viewer {
  width: 100%;
  height: 100%;
}

/* 智慧城市控制面板 */
.smart-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  max-height: 90vh;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 25px;
  color: white;
  z-index: 1000;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.panel-header {
  text-align: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(74, 144, 226, 0.3);
}

.panel-header h2 {
  margin: 0 0 10px 0;
  font-size: 22px;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.loading {
  background: #ff9800;
}

.status-dot.online {
  background: #4caf50;
}

.status-dot.error {
  background: #f44336;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* 城市概览 */
.city-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(123, 104, 238, 0.1) 100%);
  border: 1px solid rgba(74, 144, 226, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.overview-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.2);
}

.item-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  border-radius: 10px;
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 12px;
  color: #b0bec5;
  margin-bottom: 4px;
}

.item-value {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

/* 智慧功能控制 */
.smart-controls {
  margin-bottom: 25px;
}

.smart-controls h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.smart-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.smart-btn:hover {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.3) 0%, rgba(123, 104, 238, 0.3) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(74, 144, 226, 0.3);
}

.smart-btn.active {
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  border-color: #4a90e2;
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
}

/* 区域选择 */
.area-selection {
  margin-bottom: 25px;
}

.area-selection h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.area-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.area-btn {
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 10px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.area-btn:hover {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

/* 实时数据 */
.realtime-data {
  margin-bottom: 25px;
}

.realtime-data h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #4a90e2;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  padding-bottom: 8px;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 13px;
}

.data-label {
  color: #b0bec5;
}

.data-value {
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
}

.data-value.good {
  background: rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.data-value.normal {
  background: rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

/* 时间显示 */
.time-display {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(123, 104, 238, 0.1) 100%);
  border-radius: 12px;
  border: 1px solid rgba(74, 144, 226, 0.2);
}

.current-time {
  font-size: 20px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 5px;
}

.current-date {
  font-size: 12px;
  color: #b0bec5;
}

/* 通知系统 */
.notification {
  position: fixed;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  color: white;
  z-index: 2000;
  max-width: 400px;
  animation: slideInDown 0.5s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.notification.success {
  border-left: 4px solid #4caf50;
}

.notification.info {
  border-left: 4px solid #2196f3;
}

.notification.warning {
  border-left: 4px solid #ff9800;
}

.notification.error {
  border-left: 4px solid #f44336;
}

.notification-icon {
  font-size: 24px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #b0bec5;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(12, 20, 69, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(74, 144, 226, 0.3);
  border-top: 4px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-text {
  font-size: 16px;
  color: #4a90e2;
}

/* 动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 滚动条样式 */
.smart-panel::-webkit-scrollbar {
  width: 6px;
}

.smart-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.smart-panel::-webkit-scrollbar-thumb {
  background: rgba(74, 144, 226, 0.5);
  border-radius: 3px;
}

.smart-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(74, 144, 226, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .smart-panel {
    width: 300px;
    right: 10px;
    top: 10px;
    padding: 20px;
  }

  .city-overview {
    grid-template-columns: 1fr;
  }

  .control-grid,
  .area-grid {
    grid-template-columns: 1fr;
  }

  .notification {
    max-width: 90%;
    left: 5%;
    transform: none;
  }
}
</style>
