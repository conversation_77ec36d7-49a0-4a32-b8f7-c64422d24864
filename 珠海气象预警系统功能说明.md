# 🌍 珠海市低空三维空域动态气象预警系统

## 🎯 **系统概述**

这是一个基于CesiumJS的三维气象预警可视化系统，专门为珠海市低空空域管理设计，提供实时气象预警监控、三维可视化展示和智能预警管理功能。

---

## 🚀 **核心功能**

### 🌍 **1. 三维地球可视化**
- **高分辨率卫星影像**：使用ArcGIS高清影像服务
- **真实地形渲染**：支持地形高程和阴影效果
- **建筑物三维模型**：集成OSM建筑物数据
- **珠海市地理锁定**：相机范围限制在珠海市区域

### 🚨 **2. 智能预警系统**
- **6种预警类型**：大风💨、暴雨🌧️、雷电⚡、大雾🌫️、冰雹🧊、龙卷风🌪️
- **4级预警等级**：红色🔴、橙色🟠、黄色🟡、蓝色🔵
- **三维预警可视化**：椭球体表示预警范围和强度
- **自动预警生成**：智能模拟真实预警场景
- **实时预警管理**：支持手动生成和清除预警

### 📊 **3. 数据统计监控**
- **分级统计**：各等级预警数量实时统计
- **区域分布**：6大珠海重点区域预警分布
- **时间追踪**：预警发布时间和更新时间
- **系统状态**：实时监控系统运行状态

### 🗺️ **4. 珠海区域管理**
- **香洲区**🏢：珠海市中心区域
- **拱北口岸**🚪：连接澳门的重要口岸
- **横琴新区**🏗️：珠海经济特区
- **金湾区**✈️：珠海机场所在地
- **斗门区**🌾：珠海农业区
- **港珠澳大桥**🌉：连接港澳的跨海大桥

### ✈️ **5. 低空空域系统**
- **珠海机场管制区**：3000米高度限制
- **港珠澳大桥空域**：1500米高度限制
- **横琴新区限制区**：2000米高度限制
- **三维空域边界**：透明多边形显示管制范围

---

## 🎮 **操作指南**

### 📋 **系统初始化**
1. **访问系统**：打开 http://localhost:5173/
2. **等待加载**：观察左上角系统状态
3. **确认就绪**：看到"🟢 系统就绪"和"🟢 地图就绪"

### 🚨 **预警管理操作**

#### **生成新预警**
- 点击"➕ 生成新预警"按钮
- 系统随机生成预警类型、等级和位置
- 在地图上显示三维预警区域

#### **查看预警详情**
- 点击预警列表中的任意预警项
- 相机自动飞行到预警位置
- 查看预警的详细信息

#### **清除预警**
- 点击"🗑️ 清除所有预警"按钮
- 移除地图上所有预警显示
- 重置预警统计数据

#### **自动更新模式**
- 点击"▶️ 开启自动更新"按钮
- 系统每10秒自动检查并可能生成新预警
- 点击"⏸️ 暂停自动更新"停止自动模式

### 🗺️ **区域导航操作**

#### **快速定位**
- 点击右上角区域面板中的任意区域
- 相机平滑飞行到选定区域
- 查看该区域的预警统计信息

#### **手动导航**
- 鼠标左键拖拽：旋转地球
- 鼠标滚轮：缩放视图
- 鼠标右键拖拽：平移视图

### 📊 **图层控制操作**

#### **建筑物图层**🏢
- 勾选/取消勾选控制建筑物显示
- 显示珠海市三维建筑模型

#### **气象预警图层**☁️
- 控制所有预警实体的显示/隐藏
- 不影响预警数据，仅控制可视化

#### **低空空域图层**✈️
- 控制空域管制区域的显示/隐藏
- 显示三维空域边界和标签

#### **区域标签图层**🏷️
- 控制珠海各区域标签的显示/隐藏
- 显示区域名称和图标

---

## 🎨 **视觉设计特色**

### 🌈 **预警颜色系统**
- **红色预警**：最高级别，红色椭球体
- **橙色预警**：高级别，橙色椭球体
- **黄色预警**：中级别，黄色椭球体
- **蓝色预警**：低级别，蓝色椭球体

### 📐 **三维可视化规则**
- **预警范围**：1-7公里半径（根据强度调整）
- **预警高度**：300-3900米（根据强度调整）
- **透明度**：60%透明度便于观察地形
- **边框**：实线边框突出预警边界

### 🎯 **用户界面设计**
- **深色主题**：适合长时间监控使用
- **半透明面板**：不遮挡地图内容
- **响应式布局**：适配不同屏幕尺寸
- **图标化操作**：直观的emoji图标系统

---

## 📈 **系统性能特点**

### ⚡ **高性能渲染**
- **WebGL加速**：利用GPU硬件加速
- **LOD优化**：根据距离调整细节级别
- **内存管理**：自动清理过期预警数据
- **流畅交互**：60FPS流畅操作体验

### 🔄 **实时更新机制**
- **增量更新**：仅更新变化的数据
- **智能缓存**：缓存常用地理数据
- **异步加载**：非阻塞式数据加载
- **错误恢复**：网络异常自动重试

---

## 🛠️ **技术架构**

### 🏗️ **前端技术栈**
- **Vue 3**：现代响应式框架
- **CesiumJS**：三维地球渲染引擎
- **Vite**：快速构建工具
- **JavaScript ES6+**：现代JavaScript特性

### 🌐 **地理数据服务**
- **Cesium Ion**：全球地形和影像数据
- **ArcGIS**：高分辨率卫星影像
- **OSM Buildings**：开源建筑物数据
- **本地坐标**：珠海市精确地理坐标

---

## 🎉 **使用场景**

### 🚁 **低空飞行管理**
- 无人机飞行路径规划
- 直升机紧急救援导航
- 低空空域交通管制

### 🌦️ **气象监测预警**
- 恶劣天气实时监控
- 航空气象服务支持
- 城市防灾减灾管理

### 🏙️ **城市规划应用**
- 三维城市可视化展示
- 空域规划辅助决策
- 公共安全应急响应

### 📚 **教育培训用途**
- 气象学教学演示
- 航空管制培训
- 地理信息系统教学

---

## 🔮 **未来扩展方向**

### 📡 **实时数据接入**
- 气象雷达数据集成
- 卫星云图实时显示
- 风场动画可视化

### 🤖 **智能预警算法**
- 机器学习预警预测
- 多源数据融合分析
- 自动预警等级评估

### 📱 **移动端适配**
- 响应式移动界面
- 触摸手势操作
- 离线数据缓存

### 🔗 **系统集成接口**
- RESTful API接口
- WebSocket实时通信
- 第三方系统对接

---

## 🎊 **立即体验**

访问 **http://localhost:5173/** 开始体验珠海市低空三维空域动态气象预警系统！

🌟 **享受沉浸式的三维气象预警可视化体验！**
